/**
 * 合同对比模块状态管理Store
 * 
 * 使用Zustand实现状态管理，管理文件上传、对比结果、选中差异等状态
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type {
  ParsedDocument,
  ComparisonResult,
  DifferenceItem,
  FileUploadStatus,
  FileValidationResult,
  ComparisonProgress,
  ContractComparisonError
} from '../types/contractComparison';

// ==================== Store接口定义 ====================

/**
 * 合同对比Store状态接口
 */
interface ContractComparisonState {
  // 文件上传相关状态
  uploadedFiles: {
    primary: File | null;
    secondary: File | null;
  };
  uploadStatus: {
    primary: FileUploadStatus;
    secondary: FileUploadStatus;
  };
  validationResults: {
    primary: FileValidationResult | null;
    secondary: FileValidationResult | null;
  };
  
  // 文档解析相关状态
  parsedDocuments: {
    primary: ParsedDocument | null;
    secondary: ParsedDocument | null;
  };
  
  // 对比相关状态
  comparisonResult: ComparisonResult | null;
  selectedDifference: DifferenceItem | null;
  isProcessing: boolean;
  comparisonProgress: ComparisonProgress | null;
  
  // 界面状态
  currentStep: 'upload' | 'processing' | 'result';
  viewMode: 'side-by-side' | 'unified';
  showOnlyDifferences: boolean;
  differenceFilter: {
    types: string[];
    severities: string[];
  };
  
  // 错误处理
  error: ContractComparisonError | null;
  
  // 导出相关
  isExporting: boolean;
  exportProgress: number;
}

/**
 * 合同对比Store操作接口
 */
interface ContractComparisonActions {
  // 文件上传操作
  setFile: (file: File, type: 'primary' | 'secondary') => void;
  setUploadStatus: (status: FileUploadStatus, type: 'primary' | 'secondary') => void;
  setValidationResult: (result: FileValidationResult, type: 'primary' | 'secondary') => void;
  clearFile: (type: 'primary' | 'secondary') => void;
  
  // 文档解析操作
  setParsedDocument: (document: ParsedDocument, type: 'primary' | 'secondary') => void;
  
  // 对比操作
  setComparisonResult: (result: ComparisonResult) => void;
  setSelectedDifference: (difference: DifferenceItem | null) => void;
  setProcessing: (processing: boolean) => void;
  setComparisonProgress: (progress: ComparisonProgress | null) => void;
  
  // 界面操作
  setCurrentStep: (step: 'upload' | 'processing' | 'result') => void;
  setViewMode: (mode: 'side-by-side' | 'unified') => void;
  setShowOnlyDifferences: (show: boolean) => void;
  setDifferenceFilter: (filter: { types: string[]; severities: string[] }) => void;
  
  // 错误处理
  setError: (error: ContractComparisonError | null) => void;
  clearError: () => void;
  
  // 导出操作
  setExporting: (exporting: boolean) => void;
  setExportProgress: (progress: number) => void;
  
  // 重置操作
  reset: () => void;
  resetComparison: () => void;
}

/**
 * 完整的Store类型
 */
type ContractComparisonStore = ContractComparisonState & ContractComparisonActions;

// ==================== 初始状态 ====================

/**
 * Store初始状态
 */
const initialState: ContractComparisonState = {
  // 文件上传相关状态
  uploadedFiles: {
    primary: null,
    secondary: null,
  },
  uploadStatus: {
    primary: FileUploadStatus.IDLE,
    secondary: FileUploadStatus.IDLE,
  },
  validationResults: {
    primary: null,
    secondary: null,
  },
  
  // 文档解析相关状态
  parsedDocuments: {
    primary: null,
    secondary: null,
  },
  
  // 对比相关状态
  comparisonResult: null,
  selectedDifference: null,
  isProcessing: false,
  comparisonProgress: null,
  
  // 界面状态
  currentStep: 'upload',
  viewMode: 'side-by-side',
  showOnlyDifferences: false,
  differenceFilter: {
    types: [],
    severities: [],
  },
  
  // 错误处理
  error: null,
  
  // 导出相关
  isExporting: false,
  exportProgress: 0,
};

// ==================== Store实现 ====================

/**
 * 合同对比Store
 */
export const useContractComparisonStore = create<ContractComparisonStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // 文件上传操作
      setFile: (file: File, type: 'primary' | 'secondary') => {
        set((state) => ({
          uploadedFiles: {
            ...state.uploadedFiles,
            [type]: file,
          },
          uploadStatus: {
            ...state.uploadStatus,
            [type]: FileUploadStatus.SELECTING,
          },
          // 清除之前的验证结果和解析文档
          validationResults: {
            ...state.validationResults,
            [type]: null,
          },
          parsedDocuments: {
            ...state.parsedDocuments,
            [type]: null,
          },
          // 如果有对比结果，清除它
          comparisonResult: null,
          selectedDifference: null,
          error: null,
        }), false, 'setFile');
      },
      
      setUploadStatus: (status: FileUploadStatus, type: 'primary' | 'secondary') => {
        set((state) => ({
          uploadStatus: {
            ...state.uploadStatus,
            [type]: status,
          },
        }), false, 'setUploadStatus');
      },
      
      setValidationResult: (result: FileValidationResult, type: 'primary' | 'secondary') => {
        set((state) => ({
          validationResults: {
            ...state.validationResults,
            [type]: result,
          },
          uploadStatus: {
            ...state.uploadStatus,
            [type]: result.isValid ? FileUploadStatus.READY : FileUploadStatus.ERROR,
          },
        }), false, 'setValidationResult');
      },
      
      clearFile: (type: 'primary' | 'secondary') => {
        set((state) => ({
          uploadedFiles: {
            ...state.uploadedFiles,
            [type]: null,
          },
          uploadStatus: {
            ...state.uploadStatus,
            [type]: FileUploadStatus.IDLE,
          },
          validationResults: {
            ...state.validationResults,
            [type]: null,
          },
          parsedDocuments: {
            ...state.parsedDocuments,
            [type]: null,
          },
          // 清除对比结果
          comparisonResult: null,
          selectedDifference: null,
          error: null,
        }), false, 'clearFile');
      },
      
      // 文档解析操作
      setParsedDocument: (document: ParsedDocument, type: 'primary' | 'secondary') => {
        set((state) => ({
          parsedDocuments: {
            ...state.parsedDocuments,
            [type]: document,
          },
          uploadStatus: {
            ...state.uploadStatus,
            [type]: FileUploadStatus.READY,
          },
        }), false, 'setParsedDocument');
      },
      
      // 对比操作
      setComparisonResult: (result: ComparisonResult) => {
        set({
          comparisonResult: result,
          currentStep: 'result',
          isProcessing: false,
          comparisonProgress: null,
          error: null,
        }, false, 'setComparisonResult');
      },
      
      setSelectedDifference: (difference: DifferenceItem | null) => {
        set({ selectedDifference: difference }, false, 'setSelectedDifference');
      },
      
      setProcessing: (processing: boolean) => {
        set((state) => ({
          isProcessing: processing,
          currentStep: processing ? 'processing' : state.currentStep,
          error: processing ? null : state.error,
        }), false, 'setProcessing');
      },
      
      setComparisonProgress: (progress: ComparisonProgress | null) => {
        set({ comparisonProgress: progress }, false, 'setComparisonProgress');
      },
      
      // 界面操作
      setCurrentStep: (step: 'upload' | 'processing' | 'result') => {
        set({ currentStep: step }, false, 'setCurrentStep');
      },
      
      setViewMode: (mode: 'side-by-side' | 'unified') => {
        set({ viewMode: mode }, false, 'setViewMode');
      },
      
      setShowOnlyDifferences: (show: boolean) => {
        set({ showOnlyDifferences: show }, false, 'setShowOnlyDifferences');
      },
      
      setDifferenceFilter: (filter: { types: string[]; severities: string[] }) => {
        set({ differenceFilter: filter }, false, 'setDifferenceFilter');
      },
      
      // 错误处理
      setError: (error: ContractComparisonError | null) => {
        set({
          error,
          isProcessing: false,
          comparisonProgress: null,
        }, false, 'setError');
      },
      
      clearError: () => {
        set({ error: null }, false, 'clearError');
      },
      
      // 导出操作
      setExporting: (exporting: boolean) => {
        set({
          isExporting: exporting,
          exportProgress: exporting ? 0 : 100,
        }, false, 'setExporting');
      },
      
      setExportProgress: (progress: number) => {
        set({ exportProgress: progress }, false, 'setExportProgress');
      },
      
      // 重置操作
      reset: () => {
        set(initialState, false, 'reset');
      },
      
      resetComparison: () => {
        set((state) => ({
          comparisonResult: null,
          selectedDifference: null,
          isProcessing: false,
          comparisonProgress: null,
          currentStep: 'upload',
          error: null,
          isExporting: false,
          exportProgress: 0,
        }), false, 'resetComparison');
      },
    }),
    {
      name: 'contract-comparison-store',
      // 在开发环境中启用devtools
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// ==================== 选择器函数 ====================

/**
 * 检查是否可以开始对比
 */
export const useCanStartComparison = () => {
  return useContractComparisonStore((state) => {
    const { uploadedFiles, validationResults } = state;
    return (
      uploadedFiles.primary &&
      uploadedFiles.secondary &&
      validationResults.primary?.isValid &&
      validationResults.secondary?.isValid &&
      !state.isProcessing
    );
  });
};

/**
 * 获取过滤后的差异列表
 */
export const useFilteredDifferences = () => {
  return useContractComparisonStore((state) => {
    const { comparisonResult, differenceFilter, showOnlyDifferences } = state;
    
    if (!comparisonResult) return [];
    
    let differences = comparisonResult.differences;
    
    // 应用类型过滤
    if (differenceFilter.types.length > 0) {
      differences = differences.filter(diff => 
        differenceFilter.types.includes(diff.type)
      );
    }
    
    // 应用严重程度过滤
    if (differenceFilter.severities.length > 0) {
      differences = differences.filter(diff => 
        differenceFilter.severities.includes(diff.severity)
      );
    }
    
    return differences;
  });
};

/**
 * 获取上传进度
 */
export const useUploadProgress = () => {
  return useContractComparisonStore((state) => {
    const { uploadStatus, validationResults } = state;
    
    const primaryReady = uploadStatus.primary === FileUploadStatus.READY && 
                        validationResults.primary?.isValid;
    const secondaryReady = uploadStatus.secondary === FileUploadStatus.READY && 
                          validationResults.secondary?.isValid;
    
    if (primaryReady && secondaryReady) return 100;
    if (primaryReady || secondaryReady) return 50;
    return 0;
  });
};
