import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Contracts from './pages/Contracts';
import ContractDetail from './pages/ContractDetail';
import Review from './pages/Review';
import ReviewResult from './pages/ReviewResult';
import Compare from './pages/Compare';
import Draft from './pages/Draft';
import Knowledge from './pages/Knowledge';
import Profile from './pages/Profile';
import FileCompare from './pages/FileCompare';
import ContractComparison from './pages/ContractComparison';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { NavigationProvider } from './contexts/NavigationContext';
import ProtectedRoute from './components/ProtectedRoute';

export default function App() {
  return (
    <ThemeProvider>
      <NavigationProvider>
        <AuthProvider>
        <Router>
          <Routes>
            {/* 公开路由 */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            
            {/* 受保护的路由 */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout>
                  <Home />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/contracts" element={
              <ProtectedRoute>
                <Layout>
                  <Contracts />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/contracts/:id" element={
              <ProtectedRoute>
                <Layout>
                  <ContractDetail />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/review" element={
              <ProtectedRoute>
                <Layout>
                  <Review />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/review/result/:taskId" element={
              <ProtectedRoute>
                <Layout>
                  <ReviewResult />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/compare" element={
              <ProtectedRoute>
                <Layout>
                  <Compare />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/draft" element={
              <ProtectedRoute>
                <Layout>
                  <Draft />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/knowledge" element={
              <ProtectedRoute>
                <Layout>
                  <Knowledge />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/profile" element={
              <ProtectedRoute>
                <Layout>
                  <Profile />
                </Layout>
              </ProtectedRoute>
            } />
            
            <Route path="/file-compare" element={
              <ProtectedRoute>
                <Layout>
                  <FileCompare />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/contract-comparison" element={
              <ProtectedRoute>
                <Layout>
                  <ContractComparison />
                </Layout>
              </ProtectedRoute>
            } />

            {/* 404 页面 */}
            <Route path="*" element={
              <Layout>
                <div className="flex items-center justify-center min-h-screen">
                  <div className="text-center">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                    <p className="text-gray-600">页面未找到</p>
                  </div>
                </div>
              </Layout>
            } />
          </Routes>
        </Router>
        </AuthProvider>
      </NavigationProvider>
    </ThemeProvider>
  );
}
