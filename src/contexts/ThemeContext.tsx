import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface ThemeContextType {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  colors: {
    primary: string;
    primaryHover: string;
    primaryLight: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// 专业蓝色系主题配色
const lightTheme = {
  primary: '#1e40af', // 深蓝色
  primaryHover: '#1d4ed8', // 蓝色悬停
  primaryLight: '#3b82f6', // 亮蓝色
  secondary: '#64748b', // 灰蓝色
  accent: '#0ea5e9', // 天蓝色
  background: '#f8fafc', // 浅灰背景
  surface: '#ffffff', // 白色表面
  text: '#1e293b', // 深灰文字
  textSecondary: '#64748b', // 次要文字
  border: '#e2e8f0', // 边框颜色
  success: '#10b981', // 成功绿色
  warning: '#f59e0b', // 警告橙色
  error: '#ef4444', // 错误红色
  info: '#3b82f6' // 信息蓝色
};

const darkTheme = {
  primary: '#3b82f6', // 亮蓝色
  primaryHover: '#2563eb', // 蓝色悬停
  primaryLight: '#60a5fa', // 更亮蓝色
  secondary: '#94a3b8', // 亮灰蓝色
  accent: '#38bdf8', // 亮天蓝色
  background: '#0f172a', // 深色背景
  surface: '#1e293b', // 深色表面
  text: '#f1f5f9', // 浅色文字
  textSecondary: '#94a3b8', // 次要文字
  border: '#334155', // 深色边框
  success: '#22c55e', // 成功绿色
  warning: '#fbbf24', // 警告橙色
  error: '#f87171', // 错误红色
  info: '#60a5fa' // 信息蓝色
};

export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<'light' | 'dark'>(() => {
    // 从localStorage获取主题设置，默认为light
    const savedTheme = localStorage.getItem('theme');
    return (savedTheme as 'light' | 'dark') || 'light';
  });

  const colors = theme === 'light' ? lightTheme : darkTheme;

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  // 应用主题到document根元素
  useEffect(() => {
    const root = document.documentElement;
    
    // 设置CSS变量
    root.style.setProperty('--color-primary', colors.primary);
    root.style.setProperty('--color-primary-hover', colors.primaryHover);
    root.style.setProperty('--color-primary-light', colors.primaryLight);
    root.style.setProperty('--color-secondary', colors.secondary);
    root.style.setProperty('--color-accent', colors.accent);
    root.style.setProperty('--color-background', colors.background);
    root.style.setProperty('--color-surface', colors.surface);
    root.style.setProperty('--color-text', colors.text);
    root.style.setProperty('--color-text-secondary', colors.textSecondary);
    root.style.setProperty('--color-border', colors.border);
    root.style.setProperty('--color-success', colors.success);
    root.style.setProperty('--color-warning', colors.warning);
    root.style.setProperty('--color-error', colors.error);
    root.style.setProperty('--color-info', colors.info);
    
    // 设置主题类名
    root.className = theme;
    
    // 设置body背景色
    document.body.style.backgroundColor = colors.background;
    document.body.style.color = colors.text;
  }, [theme, colors]);

  const value: ThemeContextType = {
    theme,
    toggleTheme,
    colors
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;