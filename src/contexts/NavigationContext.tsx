import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type NavigationMode = 'sidebar' | 'header';

interface NavigationContextType {
  navigationMode: NavigationMode;
  toggleNavigationMode: () => void;
  setNavigationMode: (mode: NavigationMode) => void;
  isMobile: boolean;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export const NavigationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // 从localStorage获取导航模式设置，默认为sidebar
  const [navigationMode, setNavigationModeState] = useState<NavigationMode>(() => {
    const savedMode = localStorage.getItem('navigationMode');
    return (savedMode as NavigationMode) || 'sidebar';
  });

  // 检测移动端设备
  const [isMobile, setIsMobile] = useState(false);

  // 监听屏幕尺寸变化
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 576);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const setNavigationMode = (mode: NavigationMode) => {
    setNavigationModeState(mode);
    localStorage.setItem('navigationMode', mode);
  };

  const toggleNavigationMode = () => {
    const newMode = navigationMode === 'sidebar' ? 'header' : 'sidebar';
    setNavigationMode(newMode);
  };

  const value: NavigationContextType = {
    navigationMode,
    toggleNavigationMode,
    setNavigationMode,
    isMobile
  };

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = (): NavigationContextType => {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

export default NavigationContext;