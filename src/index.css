/* 导入导航样式 - 必须在最前面 */
@import './styles/navigation.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 专业蓝色系主题样式 */
:root {
  /* 主色调 - 蓝色系 */
  --color-primary: #1e40af;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #3b82f6;
  --color-secondary: #64748b;
  --color-accent: #0ea5e9;
  
  /* 背景色 */
  --color-background: #f8fafc;
  --color-surface: #ffffff;
  
  /* 文字颜色 */
  --color-text: #1e293b;
  --color-text-secondary: #64748b;
  
  /* 边框和分割线 */
  --color-border: #e2e8f0;
  
  /* 状态颜色 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
}

/* 暗色主题 */
.dark {
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-primary-light: #60a5fa;
  --color-secondary: #94a3b8;
  --color-accent: #38bdf8;
  
  --color-background: #0f172a;
  --color-surface: #1e293b;
  
  --color-text: #f1f5f9;
  --color-text-secondary: #94a3b8;
  
  --color-border: #334155;
  
  --color-success: #22c55e;
  --color-warning: #fbbf24;
  --color-error: #f87171;
  --color-info: #60a5fa;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--color-background);
  color: var(--color-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ant Design 组件样式覆盖 */
.ant-layout {
  background-color: var(--color-background) !important;
}

.ant-layout-header {
  background-color: var(--color-surface) !important;
  border-bottom: 1px solid var(--color-border) !important;
}

.ant-layout-sider {
  background-color: var(--color-surface) !important;
}

.ant-menu {
  background-color: transparent !important;
  border-right: none !important;
}

.ant-menu-item {
  color: var(--color-text) !important;
  border-radius: var(--radius-md) !important;
  margin: 4px 8px !important;
  width: calc(100% - 16px) !important;
}

.ant-menu-item:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
  color: var(--color-primary) !important;
}

.ant-menu-item-selected {
  background-color: rgba(59, 130, 246, 0.15) !important;
  color: var(--color-primary) !important;
}

.ant-menu-item-selected::after {
  display: none !important;
}

.ant-card {
  background-color: var(--color-surface) !important;
  border-color: var(--color-border) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
}

.ant-card-head {
  border-bottom-color: var(--color-border) !important;
}

.ant-card-head-title {
  color: var(--color-text) !important;
}

.ant-btn-primary {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  box-shadow: none !important;
}

.ant-btn-primary:hover {
  background-color: var(--color-primary-hover) !important;
  border-color: var(--color-primary-hover) !important;
}

.ant-input {
  background-color: var(--color-surface) !important;
  border-color: var(--color-border) !important;
  color: var(--color-text) !important;
}

.ant-input:focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.ant-select {
  color: var(--color-text) !important;
}

.ant-select-selector {
  background-color: var(--color-surface) !important;
  border-color: var(--color-border) !important;
}

.ant-select-focused .ant-select-selector {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.ant-table {
  background-color: var(--color-surface) !important;
}

.ant-table-thead > tr > th {
  background-color: var(--color-background) !important;
  border-bottom-color: var(--color-border) !important;
  color: var(--color-text) !important;
}

.ant-table-tbody > tr > td {
  border-bottom-color: var(--color-border) !important;
  color: var(--color-text) !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: rgba(59, 130, 246, 0.05) !important;
}

.ant-pagination-item {
  background-color: var(--color-surface) !important;
  border-color: var(--color-border) !important;
}

.ant-pagination-item a {
  color: var(--color-text) !important;
}

.ant-pagination-item-active {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

.ant-pagination-item-active a {
  color: white !important;
}

.ant-modal-content {
  background-color: var(--color-surface) !important;
}

.ant-modal-header {
  background-color: var(--color-surface) !important;
  border-bottom-color: var(--color-border) !important;
}

.ant-modal-title {
  color: var(--color-text) !important;
}

.ant-form-item-label > label {
  color: var(--color-text) !important;
}

.ant-upload {
  background-color: var(--color-surface) !important;
  border-color: var(--color-border) !important;
}

.ant-upload:hover {
  border-color: var(--color-primary) !important;
}

.ant-progress-bg {
  background-color: var(--color-primary) !important;
}

.ant-tag {
  border-radius: var(--radius-sm) !important;
}

.ant-statistic-title {
  color: var(--color-text-secondary) !important;
}

.ant-statistic-content {
  color: var(--color-text) !important;
}

.ant-list-item {
  border-bottom-color: var(--color-border) !important;
}

.ant-list-item-meta-title {
  color: var(--color-text) !important;
}

.ant-list-item-meta-description {
  color: var(--color-text-secondary) !important;
}

.ant-drawer-content {
  background-color: var(--color-surface) !important;
}

.ant-drawer-header {
  background-color: var(--color-surface) !important;
  border-bottom-color: var(--color-border) !important;
}

.ant-drawer-title {
  color: var(--color-text) !important;
}

.ant-dropdown {
  background-color: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  box-shadow: var(--shadow-lg) !important;
}

.ant-dropdown-menu {
  background-color: var(--color-surface) !important;
}

.ant-dropdown-menu-item {
  color: var(--color-text) !important;
}

.ant-dropdown-menu-item:hover {
  background-color: rgba(59, 130, 246, 0.1) !important;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-background);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    left: -240px !important;
    z-index: 1000 !important;
    transition: left 0.3s ease !important;
  }
  
  .ant-layout-sider.ant-layout-sider-collapsed {
    left: 0 !important;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 工具类 */
.text-gradient {
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

:root {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 双轨制架构 - HTML内容渲染器样式 */
.html-content-renderer {
  line-height: 1.6;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 高亮样式 - 双轨制架构 */
.highlight-difference {
  background-color: rgba(239, 68, 68, 0.2);
  border-radius: 2px;
  padding: 1px 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.highlight-difference:hover {
  background-color: rgba(239, 68, 68, 0.3);
}

.highlight-difference.selected {
  background-color: rgba(239, 68, 68, 0.4);
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.5);
}

/* 差异严重程度样式 */
.highlight-high {
  background-color: rgba(239, 68, 68, 0.3);
}

.highlight-medium {
  background-color: rgba(245, 158, 11, 0.3);
}

.highlight-low {
  background-color: rgba(34, 197, 94, 0.2);
}

.highlight-similarity {
  background-color: rgba(34, 197, 94, 0.2);
  border-radius: 2px;
  padding: 1px 2px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.highlight-similarity:hover {
  background-color: rgba(34, 197, 94, 0.3);
}

.highlight-similarity.selected {
  background-color: rgba(34, 197, 94, 0.4);
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.5);
}

/* 相似度级别样式 */
.highlight-similarity-high {
  background-color: rgba(34, 197, 94, 0.3);
}

.highlight-similarity-medium {
  background-color: rgba(34, 197, 94, 0.2);
}

.highlight-similarity-low {
  background-color: rgba(34, 197, 94, 0.1);
}

/* HTML内容样式 */
.html-content-renderer h1,
.html-content-renderer h2,
.html-content-renderer h3,
.html-content-renderer h4,
.html-content-renderer h5,
.html-content-renderer h6 {
  margin: 1em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.3;
}

.html-content-renderer h1 { font-size: 1.5em; }
.html-content-renderer h2 { font-size: 1.3em; }
.html-content-renderer h3 { font-size: 1.1em; }

.html-content-renderer p {
  margin: 0.5em 0;
}

.html-content-renderer table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
  border: 1px solid #e5e7eb;
}

.html-content-renderer th,
.html-content-renderer td {
  border: 1px solid #e5e7eb;
  padding: 8px 12px;
  text-align: left;
}

.html-content-renderer th {
  background-color: #f9fafb;
  font-weight: 600;
}

.html-content-renderer tr:nth-child(even) {
  background-color: #f9fafb;
}

.html-content-renderer ul,
.html-content-renderer ol {
  margin: 0.5em 0;
  padding-left: 2em;
}

.html-content-renderer li {
  margin: 0.25em 0;
}

.html-content-renderer strong {
  font-weight: 600;
}

.html-content-renderer em {
  font-style: italic;
}

/* 纯文本内容样式 */
.plain-text-content {
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
}