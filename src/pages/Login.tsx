import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Divider } from 'antd';
import { UserOutlined, LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

const { Title, Text } = Typography;

interface LoginForm {
  email: string;
  password: string;
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false);
  const { login, isAuthenticated } = useAuth();
  const { colors } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  const from = (location.state as any)?.from?.pathname || '/';

  // 监听认证状态变化，登录成功后自动跳转
  useEffect(() => {
    if (loginSuccess && isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [loginSuccess, isAuthenticated, navigate, from]);

  const onFinish = async (values: LoginForm) => {
    setLoading(true);
    try {
      const result = await login(values.email, values.password);
      if (result.success) {
        message.success('登录成功！');
        setLoginSuccess(true); // 标记登录成功，触发useEffect监听
      } else {
        message.error(result.message || '登录失败');
      }
    } catch (error) {
      message.error('登录过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div 
      className="min-h-screen flex items-center justify-center px-4"
      style={{ backgroundColor: colors.background }}
    >
      <div className="w-full max-w-md">
        {/* Logo和标题 */}
        <div className="text-center mb-8">
          <div 
            className="w-16 h-16 mx-auto rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-4"
            style={{ backgroundColor: colors.primary }}
          >
            C
          </div>
          <Title level={2} style={{ color: colors.text, marginBottom: 8 }}>
            智能合同审查系统
          </Title>
          <Text type="secondary" style={{ color: colors.textSecondary }}>
            专业的合同管理与智能审查平台
          </Text>
        </div>

        {/* 登录表单 */}
        <Card 
          style={{ 
            backgroundColor: colors.surface,
            borderColor: colors.border,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }}
        >
          <Form
            form={form}
            name="login"
            onFinish={onFinish}
            layout="vertical"
            size="large"
            autoComplete="off"
            initialValues={{
              email: '<EMAIL>',
              password: 'admin123'
            }}
          >
            <Form.Item
              name="email"
              label={<span style={{ color: colors.text }}>邮箱</span>}
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input
                prefix={<UserOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请输入邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label={<span style={{ color: colors.text }}>密码</span>}
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位字符' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请输入密码"
                autoComplete="current-password"
                iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                style={{
                  backgroundColor: colors.primary,
                  borderColor: colors.primary,
                  height: '48px',
                  fontSize: '16px'
                }}
              >
                {loading ? '登录中...' : '登录'}
              </Button>
            </Form.Item>
          </Form>

          <Divider style={{ borderColor: colors.border }}>
            <Text type="secondary" style={{ color: colors.textSecondary }}>或</Text>
          </Divider>

          <div className="text-center">
            <Text style={{ color: colors.textSecondary }}>还没有账号？</Text>
            <Link 
              to="/register" 
              style={{ 
                color: colors.primary,
                marginLeft: '8px',
                textDecoration: 'none'
              }}
            >
              立即注册
            </Link>
          </div>
        </Card>

        {/* 演示账号信息 */}
        <Card 
          size="small"
          style={{ 
            marginTop: '16px',
            backgroundColor: colors.surface,
            borderColor: colors.border
          }}
        >
          <Text strong style={{ color: colors.text }}>演示账号：</Text>
          <div className="mt-2 space-y-1">
            <div style={{ color: colors.textSecondary }}>
              <Text code><EMAIL></Text> / <Text code>admin123</Text> (管理员)
            </div>
            <div style={{ color: colors.textSecondary }}>
              <Text code><EMAIL></Text> / <Text code>manager123</Text> (法务主管)
            </div>
            <div style={{ color: colors.textSecondary }}>
              <Text code><EMAIL></Text> / <Text code>staff123</Text> (法务专员)
            </div>
            <div style={{ color: colors.textSecondary }}>
              <Text code><EMAIL></Text> / <Text code>user123</Text> (普通用户)
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;