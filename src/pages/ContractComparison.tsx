/**
 * 合同对比主页面
 * 
 * 实现步骤式界面（上传→处理→结果），集成所有子组件
 * 提供完整的合同对比工作流程
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Steps,
  Card,
  Button,
  Space,
  Alert,
  Typography,
  Row,
  Col,
  Spin,
  Progress,
  message,
  Modal,
  Breadcrumb
} from 'antd';
import {
  UploadOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  HomeOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useContractComparisonStore, useCanStartComparison } from '../stores/contractComparisonStore';
import DocumentUploader from '../components/contract-comparison/DocumentUploader';
import ComparisonViewer from '../components/contract-comparison/ComparisonViewer';
import DifferenceNavigator from '../components/contract-comparison/DifferenceNavigator';
import '../styles/contractComparison.css';
import type {
  ComparisonResult,
  ComparisonProgress
} from '../types/contractComparison';

const { Step } = Steps;
const { Title, Paragraph, Text } = Typography;

/**
 * 处理步骤配置
 */
const PROCESSING_STEPS = [
  { key: 'upload', title: '文件上传', description: '上传文档文件到服务器' },
  { key: 'parse', title: '文档解析', description: '解析文档内容和结构' },
  { key: 'analyze', title: '差异分析', description: '分析两个文档的差异' },
  { key: 'format', title: '结果格式化', description: '生成对比结果和统计信息' }
];

/**
 * 合同对比主页面组件
 */
const ContractComparison: React.FC = () => {
  const navigate = useNavigate();
  
  const {
    uploadedFiles,
    comparisonResult,
    currentStep,
    setCurrentStep,
    isProcessing,
    setProcessing,
    comparisonProgress,
    setComparisonProgress,
    error,
    setError,
    reset,
    resetComparison
  } = useContractComparisonStore();

  const canStartComparison = useCanStartComparison();

  // 本地状态
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [showResetModal, setShowResetModal] = useState(false);

  /**
   * 上传文件到服务器
   */
  const uploadFiles = useCallback(async (): Promise<string> => {
    if (!uploadedFiles.primary || !uploadedFiles.secondary) {
      throw new Error('请先选择要对比的文件');
    }

    const formData = new FormData();
    formData.append('primaryFile', uploadedFiles.primary);
    formData.append('secondaryFile', uploadedFiles.secondary);

    const response = await fetch('/api/contract-comparison/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: formData
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || '文件上传失败');
    }

    const data = await response.json();
    return data.data.sessionId;
  }, [uploadedFiles]);

  /**
   * 执行对比分析
   */
  const performComparison = useCallback(async (sessionId: string): Promise<ComparisonResult> => {
    const response = await fetch('/api/contract-comparison/compare', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        sessionId,
        options: {
          ignoreWhitespace: true,
          ignoreCase: false,
          minDifferenceLength: 3,
          similarityThreshold: 0.8,
          generateDetailedStats: true
        }
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || '对比分析失败');
    }

    const data = await response.json();
    return data.data;
  }, []);

  /**
   * 开始对比流程
   */
  const startComparison = useCallback(async () => {
    if (!canStartComparison) {
      message.error('请先上传两个有效的文档文件');
      return;
    }

    setProcessing(true);
    setCurrentStep('processing');
    setError(null);

    try {
      // 步骤1: 上传文件
      setComparisonProgress({
        sessionId: '',
        progress: 10,
        currentStep: '上传文件',
        totalSteps: 4,
        description: '正在上传文档文件...'
      });

      const uploadedSessionId = await uploadFiles();
      setSessionId(uploadedSessionId);

      // 步骤2: 开始对比
      setComparisonProgress({
        sessionId: uploadedSessionId,
        progress: 30,
        currentStep: '解析文档',
        totalSteps: 4,
        description: '正在解析文档内容和结构...'
      });

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setComparisonProgress(prev => {
          if (!prev || prev.progress >= 90) return prev;
          return {
            ...prev,
            progress: prev.progress + 10,
            description: prev.progress < 50 ? '正在分析文档差异...' : '正在生成对比结果...'
          };
        });
      }, 1000);

      const result = await performComparison(uploadedSessionId);
      
      clearInterval(progressInterval);

      // 完成
      setComparisonProgress({
        sessionId: uploadedSessionId,
        progress: 100,
        currentStep: '完成',
        totalSteps: 4,
        description: '对比分析完成'
      });

      // 设置结果并切换到结果页面
      setTimeout(() => {
        setCurrentStep('result');
        setProcessing(false);
        setComparisonProgress(null);
        message.success('对比分析完成！');
      }, 1000);

    } catch (error) {
      console.error('对比失败:', error);
      setError({
        type: 'COMPARISON_FAILED' as any,
        message: error instanceof Error ? error.message : '对比分析失败',
        timestamp: new Date()
      });
      setProcessing(false);
      setCurrentStep('upload');
      setComparisonProgress(null);
      message.error(error instanceof Error ? error.message : '对比分析失败');
    }
  }, [
    canStartComparison,
    uploadFiles,
    performComparison,
    setProcessing,
    setCurrentStep,
    setError,
    setComparisonProgress
  ]);

  /**
   * 重置对比
   */
  const handleReset = useCallback(() => {
    setShowResetModal(true);
  }, []);

  /**
   * 确认重置
   */
  const confirmReset = useCallback(() => {
    reset();
    setSessionId(null);
    setShowResetModal(false);
    message.info('已重置，可以重新开始对比');
  }, [reset]);

  /**
   * 重新对比
   */
  const handleRetry = useCallback(() => {
    resetComparison();
    setCurrentStep('upload');
  }, [resetComparison, setCurrentStep]);

  /**
   * 获取当前步骤索引
   */
  const getCurrentStepIndex = () => {
    switch (currentStep) {
      case 'upload': return 0;
      case 'processing': return 1;
      case 'result': return 2;
      default: return 0;
    }
  };

  /**
   * 渲染处理进度
   */
  const renderProcessingProgress = () => (
    <div className="processing-indicator">
      <div className="text-center mb-8">
        <Spin 
          indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />}
          className="mb-4"
        />
        <Title level={3}>正在处理文档对比</Title>
        <Paragraph type="secondary">
          请稍候，我们正在分析您的文档...
        </Paragraph>
      </div>

      {comparisonProgress && (
        <div className="max-w-md mx-auto">
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <Text strong>{comparisonProgress.currentStep}</Text>
              <Text type="secondary">{comparisonProgress.progress}%</Text>
            </div>
            <Progress 
              percent={comparisonProgress.progress} 
              status={comparisonProgress.progress === 100 ? 'success' : 'active'}
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
          </div>
          
          <Text type="secondary" className="block text-center">
            {comparisonProgress.description}
          </Text>

          {/* 处理步骤 */}
          <div className="mt-6">
            <Steps 
              direction="vertical" 
              size="small"
              current={Math.floor(comparisonProgress.progress / 25)}
            >
              {PROCESSING_STEPS.map((step, index) => (
                <Step
                  key={step.key}
                  title={step.title}
                  description={step.description}
                  status={
                    index < Math.floor(comparisonProgress.progress / 25) ? 'finish' :
                    index === Math.floor(comparisonProgress.progress / 25) ? 'process' : 'wait'
                  }
                />
              ))}
            </Steps>
          </div>
        </div>
      )}
    </div>
  );

  /**
   * 渲染结果页面
   */
  const renderResultPage = () => (
    <div className="comparison-result-layout">
      <Row gutter={24}>
        {/* 左侧差异导航 */}
        <Col span={6}>
          <DifferenceNavigator />
        </Col>
        
        {/* 右侧对比视图 */}
        <Col span={18}>
          <ComparisonViewer />
        </Col>
      </Row>
    </div>
  );

  return (
    <div className="contract-comparison-container">
      {/* 头部 */}
      <div className="contract-comparison-header">
        <div className="max-w-7xl mx-auto">
          <Breadcrumb className="mb-4">
            <Breadcrumb.Item>
              <HomeOutlined />
              <span className="ml-1">首页</span>
            </Breadcrumb.Item>
            <Breadcrumb.Item>
              <FileTextOutlined />
              <span className="ml-1">合同对比</span>
            </Breadcrumb.Item>
          </Breadcrumb>
          
          <div className="flex items-center justify-between">
            <div>
              <Title level={2} className="mb-2">合同对比分析</Title>
              <Paragraph type="secondary" className="mb-0">
                上传两个文档进行智能对比分析，快速识别差异和变更
              </Paragraph>
            </div>
            
            <Space>
              {currentStep === 'result' && (
                <Button 
                  icon={<ReloadOutlined />}
                  onClick={handleRetry}
                >
                  重新对比
                </Button>
              )}
              
              <Button 
                danger
                onClick={handleReset}
                disabled={isProcessing}
              >
                重置
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="contract-comparison-main">
        {/* 步骤指示器 */}
        <Card className="mb-6">
          <Steps current={getCurrentStepIndex()} className="mb-4">
            <Step 
              title="上传文档" 
              description="选择要对比的文档文件"
              icon={currentStep === 'upload' ? <UploadOutlined /> : undefined}
            />
            <Step 
              title="分析处理" 
              description="解析文档并分析差异"
              icon={currentStep === 'processing' ? <LoadingOutlined /> : undefined}
            />
            <Step 
              title="查看结果" 
              description="查看对比结果和差异详情"
              icon={currentStep === 'result' ? <CheckCircleOutlined /> : undefined}
            />
          </Steps>
        </Card>

        {/* 错误提示 */}
        {error && (
          <Alert
            message="处理失败"
            description={error.message}
            type="error"
            closable
            onClose={() => setError(null)}
            className="mb-6"
            action={
              <Button size="small" onClick={handleRetry}>
                重试
              </Button>
            }
          />
        )}

        {/* 内容区域 */}
        <div className="content-area">
          {currentStep === 'upload' && (
            <Card>
              <DocumentUploader />
              
              {canStartComparison && (
                <div className="text-center mt-8">
                  <Button
                    type="primary"
                    size="large"
                    onClick={startComparison}
                    disabled={isProcessing}
                    loading={isProcessing}
                    className="px-12"
                  >
                    开始对比分析
                  </Button>
                </div>
              )}
            </Card>
          )}

          {currentStep === 'processing' && (
            <Card>
              {renderProcessingProgress()}
            </Card>
          )}

          {currentStep === 'result' && comparisonResult && (
            renderResultPage()
          )}
        </div>
      </div>

      {/* 重置确认对话框 */}
      <Modal
        title="确认重置"
        open={showResetModal}
        onOk={confirmReset}
        onCancel={() => setShowResetModal(false)}
        okText="确认重置"
        cancelText="取消"
        okButtonProps={{ danger: true }}
      >
        <div className="flex items-start space-x-3">
          <ExclamationCircleOutlined className="text-orange-500 text-lg mt-1" />
          <div>
            <Paragraph className="mb-2">
              重置将清除所有已上传的文件和对比结果，您需要重新开始整个对比流程。
            </Paragraph>
            <Paragraph type="secondary" className="mb-0">
              此操作无法撤销，请确认是否继续？
            </Paragraph>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ContractComparison;
