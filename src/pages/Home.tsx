import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, List, Tag, Button, Space, Typography } from 'antd';
import {
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  Bar<PERSON>hartOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface DashboardStats {
  totalContracts: number;
  pendingReview: number;
  completedReviews: number;
  highRiskContracts: number;
  recentActivities: Activity[];
  contractsByStatus: StatusCount[];
  riskDistribution: RiskCount[];
}

interface Activity {
  id: string;
  type: 'contract_created' | 'review_completed' | 'contract_signed';
  title: string;
  description: string;
  timestamp: string;
  user: string;
}

interface StatusCount {
  status: string;
  count: number;
  percentage: number;
}

interface RiskCount {
  level: string;
  count: number;
  percentage: number;
}

export default function Home() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const { user, token } = useAuth();
  const { colors } = useTheme();
  const navigate = useNavigate();

  // 获取仪表板数据
  const fetchDashboardData = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/contracts/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else {
        console.error('Failed to fetch dashboard data');
      }
    } catch (error) {
      console.error('Dashboard data fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchDashboardData();
    }
  }, [token]);

  // 活动类型配置
  const activityConfig = {
    contract_created: { icon: <FileTextOutlined />, color: colors.primary, text: '合同创建' },
    review_completed: { icon: <CheckCircleOutlined />, color: colors.success, text: '审查完成' },
    contract_signed: { icon: <CheckCircleOutlined />, color: colors.info, text: '合同签署' }
  };

  return (
    <div>
      {/* 欢迎区域 */}
      <div className="mb-6">
        <Title level={2} style={{ color: colors.text, marginBottom: 8 }}>
          欢迎回来，{user?.name || user?.username}！
        </Title>
        <Text type="secondary" style={{ color: colors.textSecondary }}>
          今天是 {dayjs().format('YYYY年MM月DD日')}，让我们开始高效的合同管理工作。
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card 
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            loading={loading}
          >
            <Statistic
              title={<span style={{ color: colors.textSecondary }}>总合同数</span>}
              value={stats?.totalContracts || 0}
              prefix={<FileTextOutlined style={{ color: colors.primary }} />}
              valueStyle={{ color: colors.text }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card 
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            loading={loading}
          >
            <Statistic
              title={<span style={{ color: colors.textSecondary }}>待审查</span>}
              value={stats?.pendingReview || 0}
              prefix={<ClockCircleOutlined style={{ color: colors.warning }} />}
              valueStyle={{ color: colors.text }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card 
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            loading={loading}
          >
            <Statistic
              title={<span style={{ color: colors.textSecondary }}>已完成审查</span>}
              value={stats?.completedReviews || 0}
              prefix={<CheckCircleOutlined style={{ color: colors.success }} />}
              valueStyle={{ color: colors.text }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card 
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            loading={loading}
          >
            <Statistic
              title={<span style={{ color: colors.textSecondary }}>高风险合同</span>}
              value={stats?.highRiskContracts || 0}
              prefix={<ExclamationCircleOutlined style={{ color: colors.error }} />}
              valueStyle={{ color: colors.text }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 合同状态分布 */}
        <Col xs={24} lg={12}>
          <Card 
            title={<span style={{ color: colors.text }}>合同状态分布</span>}
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            loading={loading}
          >
            {stats?.contractsByStatus?.map((item, index) => (
              <div key={index} className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span style={{ color: colors.text }}>{item.status}</span>
                  <span style={{ color: colors.textSecondary }}>{item.count}</span>
                </div>
                <Progress 
                  percent={isNaN(item.percentage) ? 0 : item.percentage} 
                  strokeColor={colors.primary}
                  trailColor={colors.border}
                  showInfo={false}
                />
              </div>
            ))}
          </Card>
        </Col>

        {/* 风险等级分布 */}
        <Col xs={24} lg={12}>
          <Card 
            title={<span style={{ color: colors.text }}>风险等级分布</span>}
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            loading={loading}
          >
            {stats?.riskDistribution?.map((item, index) => {
              const riskColors = {
                '低风险': colors.success,
                '中风险': colors.warning,
                '高风险': colors.error
              };
              return (
                <div key={index} className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span style={{ color: colors.text }}>{item.level}</span>
                    <span style={{ color: colors.textSecondary }}>{item.count}</span>
                  </div>
                  <Progress 
                    percent={isNaN(item.percentage) ? 0 : item.percentage} 
                    strokeColor={riskColors[item.level as keyof typeof riskColors] || colors.primary}
                    trailColor={colors.border}
                    showInfo={false}
                  />
                </div>
              );
            })}
          </Card>
        </Col>
      </Row>

      {/* 最近活动 */}
      <Row gutter={[16, 16]} className="mt-4">
        <Col xs={24}>
          <Card 
            title={
              <div className="flex justify-between items-center">
                <span style={{ color: colors.text }}>最近活动</span>
                <Button 
                  type="link" 
                  onClick={() => navigate('/contracts')}
                  style={{ color: colors.primary }}
                >
                  查看全部
                </Button>
              </div>
            }
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            loading={loading}
          >
            <List
              dataSource={stats?.recentActivities || []}
              renderItem={(item) => {
                const config = activityConfig[item.type];
                return (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <div 
                          className="w-8 h-8 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: `${config.color}20`, color: config.color }}
                        >
                          {config.icon}
                        </div>
                      }
                      title={
                        <div className="flex items-center space-x-2">
                          <span style={{ color: colors.text }}>{item.title}</span>
                          <Tag color={config.color}>{config.text}</Tag>
                        </div>
                      }
                      description={
                        <div>
                          <p style={{ color: colors.textSecondary, margin: 0 }}>{item.description}</p>
                          <p style={{ color: colors.textSecondary, margin: 0, fontSize: '12px' }}>
                            {dayjs(item.timestamp).format('MM-DD HH:mm')} · {item.user}
                          </p>
                        </div>
                      }
                    />
                  </List.Item>
                );
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Row gutter={[16, 16]} className="mt-4">
        <Col xs={24}>
          <Card 
            title={<span style={{ color: colors.text }}>快速操作</span>}
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
          >
            <Space size="middle" wrap>
              <Button 
                type="primary" 
                icon={<FileTextOutlined />}
                onClick={() => navigate('/contracts')}
                style={{ backgroundColor: colors.primary, borderColor: colors.primary }}
              >
                新建合同
              </Button>
              <Button 
                icon={<EyeOutlined />}
                onClick={() => navigate('/review')}
              >
                智能审查
              </Button>
              <Button 
                icon={<BarChartOutlined />}
                onClick={() => navigate('/compare')}
              >
                条款对比
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
}