import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Button, 
  Input, 
  Select, 
  Table, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  message, 
  Tabs, 
  List, 
  Avatar, 
  Divider, 
  Row, 
  Col,
  Tooltip,
  Popconfirm,
  Upload,
  Progress,
  Alert,
  Tree,
  Collapse,
  Badge
} from 'antd';
import { 
  BookOutlined, 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  DownloadOutlined, 
  UploadOutlined, 
  FileTextOutlined, 
  FolderOutlined, 
  StarOutlined, 
  EyeOutlined, 
  ShareAltOutlined,
  TagsOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Panel } = Collapse;

interface KnowledgeItem {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  type: 'law' | 'regulation' | 'case' | 'template' | 'guide';
  status: 'published' | 'draft' | 'archived';
  author: string;
  created_at: string;
  updated_at: string;
  views: number;
  favorites: number;
  is_favorite?: boolean;
}

interface KnowledgeCategory {
  id: string;
  name: string;
  description: string;
  count: number;
  children?: KnowledgeCategory[];
}

const Knowledge: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const [form] = Form.useForm();
  
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeItem[]>([]);
  const [categories, setCategories] = useState<KnowledgeCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<KnowledgeItem | null>(null);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingItem, setViewingItem] = useState<KnowledgeItem | null>(null);
  const [activeTab, setActiveTab] = useState('list');
  
  useEffect(() => {
    fetchKnowledgeItems();
    fetchCategories();
  }, []);
  
  const fetchKnowledgeItems = async () => {
    try {
      setLoading(true);
      // 这里应该调用获取知识库的API
      // 暂时使用模拟数据
      const mockItems: KnowledgeItem[] = [
        {
          id: 'kb-1',
          title: '合同法基本原则',
          content: '合同法的基本原则包括：平等原则、自愿原则、公平原则、诚实信用原则、守法原则等...',
          category: 'contract-law',
          tags: ['合同法', '基本原则', '法律基础'],
          type: 'law',
          status: 'published',
          author: '法务专家',
          created_at: new Date(Date.now() - 86400000 * 7).toISOString(),
          updated_at: new Date(Date.now() - 86400000 * 2).toISOString(),
          views: 1250,
          favorites: 89,
          is_favorite: true
        },
        {
          id: 'kb-2',
          title: '销售合同常见条款解析',
          content: '销售合同中的关键条款包括：标的物条款、价款条款、履行期限、违约责任等...',
          category: 'contract-templates',
          tags: ['销售合同', '条款解析', '实务指导'],
          type: 'guide',
          status: 'published',
          author: '合同专家',
          created_at: new Date(Date.now() - 86400000 * 5).toISOString(),
          updated_at: new Date(Date.now() - 86400000 * 1).toISOString(),
          views: 890,
          favorites: 67
        },
        {
          id: 'kb-3',
          title: '最高法院合同纠纷典型案例',
          content: '案例一：关于合同解除的条件和后果...案例二：违约金调整的司法实践...',
          category: 'case-law',
          tags: ['典型案例', '合同纠纷', '司法实践'],
          type: 'case',
          status: 'published',
          author: '案例研究员',
          created_at: new Date(Date.now() - 86400000 * 3).toISOString(),
          updated_at: new Date(Date.now() - 86400000 * 1).toISOString(),
          views: 2100,
          favorites: 156
        },
        {
          id: 'kb-4',
          title: '民法典合同编新规定',
          content: '民法典合同编相比原合同法的主要变化：新增保理合同、物业服务合同等典型合同...',
          category: 'regulations',
          tags: ['民法典', '合同编', '新规定'],
          type: 'regulation',
          status: 'published',
          author: '立法研究员',
          created_at: new Date(Date.now() - 86400000 * 10).toISOString(),
          updated_at: new Date(Date.now() - 86400000 * 5).toISOString(),
          views: 1680,
          favorites: 123
        }
      ];
      setKnowledgeItems(mockItems);
    } catch (error) {
      console.error('获取知识库失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchCategories = async () => {
    try {
      // 这里应该调用获取分类的API
      // 暂时使用模拟数据
      const mockCategories: KnowledgeCategory[] = [
        {
          id: 'contract-law',
          name: '合同法律',
          description: '合同相关的法律法规',
          count: 45
        },
        {
          id: 'contract-templates',
          name: '合同模板',
          description: '各类合同模板和范本',
          count: 32
        },
        {
          id: 'case-law',
          name: '案例法',
          description: '典型案例和司法实践',
          count: 28
        },
        {
          id: 'regulations',
          name: '法规政策',
          description: '相关法规和政策文件',
          count: 19
        },
        {
          id: 'practice-guides',
          name: '实务指南',
          description: '实务操作指南和经验分享',
          count: 23
        }
      ];
      setCategories(mockCategories);
    } catch (error) {
      console.error('获取分类失败:', error);
    }
  };
  
  const handleSearch = (value: string) => {
    setSearchText(value);
    // 这里应该调用搜索API
  };
  
  const handleAddKnowledge = () => {
    setEditingItem(null);
    form.resetFields();
    setModalVisible(true);
  };
  
  const handleEditKnowledge = (item: KnowledgeItem) => {
    setEditingItem(item);
    form.setFieldsValue({
      title: item.title,
      content: item.content,
      category: item.category,
      tags: item.tags,
      type: item.type,
      status: item.status
    });
    setModalVisible(true);
  };
  
  const handleViewKnowledge = (item: KnowledgeItem) => {
    setViewingItem(item);
    setViewModalVisible(true);
    // 增加浏览次数
    setKnowledgeItems(prev => 
      prev.map(k => k.id === item.id ? { ...k, views: k.views + 1 } : k)
    );
  };
  
  const handleToggleFavorite = (item: KnowledgeItem) => {
    setKnowledgeItems(prev => 
      prev.map(k => {
        if (k.id === item.id) {
          const newFavorite = !k.is_favorite;
          return {
            ...k,
            is_favorite: newFavorite,
            favorites: newFavorite ? k.favorites + 1 : k.favorites - 1
          };
        }
        return k;
      })
    );
    message.success(item.is_favorite ? '已取消收藏' : '已添加收藏');
  };
  
  const handleDeleteKnowledge = async (id: string) => {
    try {
      // 这里应该调用删除API
      setKnowledgeItems(prev => prev.filter(item => item.id !== id));
      message.success('删除成功');
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };
  
  const handleSubmit = async (values: any) => {
    try {
      if (editingItem) {
        // 更新知识库项目
        setKnowledgeItems(prev => 
          prev.map(item => 
            item.id === editingItem.id 
              ? { ...item, ...values, updated_at: new Date().toISOString() }
              : item
          )
        );
        message.success('更新成功');
      } else {
        // 新增知识库项目
        const newItem: KnowledgeItem = {
          id: `kb-${Date.now()}`,
          ...values,
          author: user?.username || '当前用户',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          views: 0,
          favorites: 0
        };
        setKnowledgeItems(prev => [newItem, ...prev]);
        message.success('添加成功');
      }
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };
  
  const filteredItems = knowledgeItems.filter(item => {
    const matchesSearch = !searchText || 
      item.title.toLowerCase().includes(searchText.toLowerCase()) ||
      item.content.toLowerCase().includes(searchText.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    const matchesType = selectedType === 'all' || item.type === selectedType;
    
    return matchesSearch && matchesCategory && matchesType;
  });
  
  const getTypeColor = (type: string) => {
    const colors = {
      law: 'red',
      regulation: 'orange', 
      case: 'green',
      template: 'blue',
      guide: 'purple'
    };
    return colors[type as keyof typeof colors] || 'default';
  };
  
  const getTypeName = (type: string) => {
    const names = {
      law: '法律',
      regulation: '法规',
      case: '案例',
      template: '模板',
      guide: '指南'
    };
    return names[type as keyof typeof names] || type;
  };
  
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: KnowledgeItem) => (
        <Space>
          <Button 
            type="link" 
            onClick={() => handleViewKnowledge(record)}
            style={{ padding: 0, height: 'auto' }}
          >
            <Text strong style={{ color: colors.primary }}>{text}</Text>
          </Button>
          {record.is_favorite && <StarOutlined style={{ color: '#faad14' }} />}
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>{getTypeName(type)}</Tag>
      )
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      width: 200,
      render: (tags: string[]) => (
        <Space wrap>
          {tags.slice(0, 2).map(tag => (
            <Tag key={tag}>{tag}</Tag>
          ))}
          {tags.length > 2 && (
            <Tooltip title={tags.slice(2).join(', ')}>
              <Tag>+{tags.length - 2}</Tag>
            </Tooltip>
          )}
        </Space>
      )
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
      width: 100
    },
    {
      title: '浏览/收藏',
      key: 'stats',
      width: 100,
      render: (record: KnowledgeItem) => (
        <Space direction="vertical" size={0}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <EyeOutlined /> {record.views}
          </Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <StarOutlined /> {record.favorites}
          </Text>
        </Space>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 120,
      render: (date: string) => (
        <Text type="secondary" style={{ fontSize: '12px' }}>
          {new Date(date).toLocaleDateString()}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: KnowledgeItem) => (
        <Space>
          <Tooltip title="查看">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleViewKnowledge(record)}
            />
          </Tooltip>
          <Tooltip title={record.is_favorite ? '取消收藏' : '收藏'}>
            <Button 
              type="text" 
              icon={<StarOutlined />} 
              size="small"
              style={{ color: record.is_favorite ? '#faad14' : undefined }}
              onClick={() => handleToggleFavorite(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEditKnowledge(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个知识库项目吗？"
            onConfirm={() => handleDeleteKnowledge(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                icon={<DeleteOutlined />} 
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];
  
  const renderListView = () => (
    <div>
      {/* 搜索和筛选 */}
      <Card 
        style={{ backgroundColor: colors.surface, borderColor: colors.border, marginBottom: '16px' }}
      >
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Input.Search
              placeholder="搜索知识库内容..."
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择分类"
              value={selectedCategory}
              onChange={setSelectedCategory}
              style={{ width: '100%' }}
            >
              <Option value="all">全部分类</Option>
              {categories.map(cat => (
                <Option key={cat.id} value={cat.id}>{cat.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择类型"
              value={selectedType}
              onChange={setSelectedType}
              style={{ width: '100%' }}
            >
              <Option value="all">全部类型</Option>
              <Option value="law">法律</Option>
              <Option value="regulation">法规</Option>
              <Option value="case">案例</Option>
              <Option value="template">模板</Option>
              <Option value="guide">指南</Option>
            </Select>
          </Col>
          <Col span={8}>
            <Space>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddKnowledge}>
                添加知识
              </Button>
              <Button icon={<UploadOutlined />}>批量导入</Button>
              <Button icon={<DownloadOutlined />}>导出</Button>
            </Space>
          </Col>
        </Row>
      </Card>
      
      {/* 知识库列表 */}
      <Card 
        style={{ backgroundColor: colors.surface, borderColor: colors.border }}
        headStyle={{ color: colors.text, borderBottomColor: colors.border }}
      >
        <Table
          columns={columns}
          dataSource={filteredItems}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>
    </div>
  );
  
  const renderCategoryView = () => (
    <Row gutter={[16, 16]}>
      {categories.map(category => (
        <Col span={8} key={category.id}>
          <Card
            hoverable
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            onClick={() => {
              setSelectedCategory(category.id);
              setActiveTab('list');
            }}
          >
            <Card.Meta
              avatar={<FolderOutlined style={{ fontSize: '24px', color: colors.primary }} />}
              title={
                <Space>
                  <Text style={{ color: colors.text }}>{category.name}</Text>
                  <Badge count={category.count} style={{ backgroundColor: colors.primary }} />
                </Space>
              }
              description={
                <Text type="secondary">{category.description}</Text>
              }
            />
          </Card>
        </Col>
      ))}
    </Row>
  );

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <Title level={2} style={{ color: colors.text, margin: 0 }}>
          法律知识库
        </Title>
        <Space>
          <Text type="secondary">
            共 {knowledgeItems.length} 条知识
          </Text>
        </Space>
      </div>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="知识列表" key="list">
          {renderListView()}
        </TabPane>
        <TabPane tab="分类浏览" key="category">
          {renderCategoryView()}
        </TabPane>
        <TabPane tab="我的收藏" key="favorites">
          <Card style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
            <List
              dataSource={knowledgeItems.filter(item => item.is_favorite)}
              renderItem={item => (
                <List.Item
                  actions={[
                    <Button type="link" onClick={() => handleViewKnowledge(item)}>查看</Button>,
                    <Button type="link" onClick={() => handleToggleFavorite(item)}>取消收藏</Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={<BookOutlined style={{ color: colors.primary }} />}
                    title={
                      <Space>
                        <Text style={{ color: colors.text }}>{item.title}</Text>
                        <Tag color={getTypeColor(item.type)}>{getTypeName(item.type)}</Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary">{item.content.substring(0, 100)}...</Text>
                        <br />
                        <Space style={{ marginTop: '8px' }}>
                          <Text type="secondary"><UserOutlined /> {item.author}</Text>
                          <Text type="secondary"><ClockCircleOutlined /> {new Date(item.updated_at).toLocaleDateString()}</Text>
                          <Text type="secondary"><EyeOutlined /> {item.views}</Text>
                        </Space>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无收藏的知识' }}
            />
          </Card>
        </TabPane>
      </Tabs>
      
      {/* 添加/编辑知识模态框 */}
      <Modal
        title={editingItem ? '编辑知识' : '添加知识'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="title"
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <Input placeholder="请输入知识标题" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="type"
                label="类型"
                rules={[{ required: true, message: '请选择类型' }]}
              >
                <Select placeholder="请选择类型">
                  <Option value="law">法律</Option>
                  <Option value="regulation">法规</Option>
                  <Option value="case">案例</Option>
                  <Option value="template">模板</Option>
                  <Option value="guide">指南</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="category"
                label="分类"
                rules={[{ required: true, message: '请选择分类' }]}
              >
                <Select placeholder="请选择分类">
                  {categories.map(cat => (
                    <Option key={cat.id} value={cat.id}>{cat.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="draft">草稿</Option>
                  <Option value="published">已发布</Option>
                  <Option value="archived">已归档</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="请输入标签，按回车添加"
              style={{ width: '100%' }}
            />
          </Form.Item>
          
          <Form.Item
            name="content"
            label="内容"
            rules={[{ required: true, message: '请输入内容' }]}
          >
            <TextArea 
              rows={10} 
              placeholder="请输入知识内容" 
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingItem ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
      
      {/* 查看知识模态框 */}
      <Modal
        title={viewingItem?.title}
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setViewModalVisible(false)}>关闭</Button>,
          <Button 
            key="favorite" 
            icon={<StarOutlined />}
            onClick={() => viewingItem && handleToggleFavorite(viewingItem)}
            style={{ color: viewingItem?.is_favorite ? '#faad14' : undefined }}
          >
            {viewingItem?.is_favorite ? '取消收藏' : '收藏'}
          </Button>,
          <Button key="share" icon={<ShareAltOutlined />}>分享</Button>
        ]}
        width={800}
      >
        {viewingItem && (
          <div>
            <Space style={{ marginBottom: '16px' }} wrap>
              <Tag color={getTypeColor(viewingItem.type)}>{getTypeName(viewingItem.type)}</Tag>
              {viewingItem.tags.map(tag => (
                <Tag key={tag}>{tag}</Tag>
              ))}
            </Space>
            
            <Divider />
            
            <div style={{ 
              backgroundColor: colors.background, 
              padding: '16px', 
              borderRadius: '6px',
              marginBottom: '16px'
            }}>
              <pre style={{ 
                color: colors.text, 
                whiteSpace: 'pre-wrap',
                fontFamily: 'inherit',
                margin: 0
              }}>
                {viewingItem.content}
              </pre>
            </div>
            
            <Row gutter={16}>
              <Col span={12}>
                <Text type="secondary">作者：{viewingItem.author}</Text>
              </Col>
              <Col span={12}>
                <Text type="secondary">更新时间：{new Date(viewingItem.updated_at).toLocaleString()}</Text>
              </Col>
              <Col span={12}>
                <Text type="secondary">浏览次数：{viewingItem.views}</Text>
              </Col>
              <Col span={12}>
                <Text type="secondary">收藏次数：{viewingItem.favorites}</Text>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Knowledge;