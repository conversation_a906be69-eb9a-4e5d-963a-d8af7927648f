/**
 * 文件对比主页面
 * 集成文件上传、对比处理、结果展示组件
 * 实现完整的文件对比工作流程
 * 专为文件对比功能设计，与现有功能完全独立
 */
import React, { useState, useCallback, useEffect, useRef } from 'react';
import {
  Layout,
  Steps,
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Alert,
  Spin,
  Progress,
  Modal,
  message,
  Breadcrumb,
  Divider,
  Tag,
  Tooltip,
  BackTop
} from 'antd';
import {
  UploadOutlined,
  DiffOutlined,
  FileTextOutlined,
  DownloadOutlined,
  ReloadOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  HomeOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import FileUploadArea from '../components/FileUploadArea';
import ComparisonResultView from '../components/ComparisonResultView';
import DiffNavigator from '../components/DiffNavigator';
import type {
  FileComparisonResult,
  DifferenceItem,
  ComparisonOptions,
  FileValidationResult
} from '../types/fileCompare';

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface FileCompareState {
  /** 当前步骤 */
  currentStep: number;
  /** 主文件 */
  primaryFile: File | null;
  /** 副文件 */
  secondaryFile: File | null;
  /** 主文件验证结果 */
  primaryValidation: FileValidationResult | null;
  /** 副文件验证结果 */
  secondaryValidation: FileValidationResult | null;
  /** 对比选项 */
  comparisonOptions: ComparisonOptions;
  /** 对比结果 */
  comparisonResult: FileComparisonResult | null;
  /** 是否正在对比 */
  isComparing: boolean;
  /** 对比进度 */
  compareProgress: number;
  /** 错误信息 */
  error: string | null;
  /** 是否显示设置 */
  showSettings: boolean;
  /** 选中的差异项 */
  selectedDifferenceId: string | null;
  /** 修复后的主文件名 */
  correctedPrimaryFileName: string | null;
  /** 修复后的副文件名 */
  correctedSecondaryFileName: string | null;
}

interface CompareProgress {
  /** 当前阶段 */
  stage: 'uploading' | 'parsing' | 'analyzing' | 'comparing' | 'generating';
  /** 进度百分比 */
  progress: number;
  /** 阶段描述 */
  description: string;
}

const FileCompare: React.FC = () => {
  const navigate = useNavigate();
  const compareRequestRef = useRef<AbortController | null>(null);

  // 加载用户偏好
  const loadUserPreferences = useCallback(() => {
    try {
      const savedOptions = localStorage.getItem('fileCompareOptions');
      if (savedOptions) {
        return JSON.parse(savedOptions);
      }
    } catch (error) {
      console.warn('无法加载用户偏好:', error);
    }
    return {
      ignoreFormatting: false,
      ignoreWhitespace: true,
      enableClauseComparison: true,
      similarityThreshold: 70,
      generateSuggestions: true,
      language: 'zh'
    };
  }, []);

  const [state, setState] = useState<FileCompareState>({
    currentStep: 0,
    primaryFile: null,
    secondaryFile: null,
    primaryValidation: null,
    secondaryValidation: null,
    comparisonOptions: loadUserPreferences(),
    comparisonResult: null,
    isComparing: false,
    compareProgress: 0,
    error: null,
    showSettings: false,
    selectedDifferenceId: null,
    correctedPrimaryFileName: null,
    correctedSecondaryFileName: null
  });

  const [compareProgressInfo, setCompareProgressInfo] = useState<CompareProgress>({
    stage: 'uploading',
    progress: 0,
    description: '准备上传文件...'
  });

  // 步骤配置
  const steps = [
    {
      title: '上传文件',
      description: '选择要对比的两个文件',
      icon: <UploadOutlined />
    },
    {
      title: '配置选项',
      description: '设置对比参数',
      icon: <SettingOutlined />
    },
    {
      title: '执行对比',
      description: '分析文件差异',
      icon: <DiffOutlined />
    },
    {
      title: '查看结果',
      description: '浏览对比结果',
      icon: <FileTextOutlined />
    }
  ];

  // 检查是否可以进入下一步
  const canProceedToNextStep = useCallback(() => {
    switch (state.currentStep) {
      case 0: // 上传文件步骤
        return state.primaryFile && 
               state.secondaryFile && 
               state.primaryValidation?.isValid && 
               state.secondaryValidation?.isValid;
      case 1: // 配置选项步骤
        return true; // 配置选项有默认值，总是可以继续
      case 2: // 执行对比步骤
        return state.comparisonResult !== null;
      default:
        return false;
    }
  }, [state.currentStep, state.primaryFile, state.secondaryFile, state.primaryValidation, state.secondaryValidation, state.comparisonResult]);

  // 处理主文件变化
  const handlePrimaryFileChange = useCallback((file: File | null) => {
    setState(prev => ({ ...prev, primaryFile: file }));
  }, []);

  // 处理副文件变化
  const handleSecondaryFileChange = useCallback((file: File | null) => {
    setState(prev => ({ ...prev, secondaryFile: file }));
  }, []);

  // 处理主文件验证变化
  const handlePrimaryValidationChange = useCallback((validation: FileValidationResult) => {
    setState(prev => ({ ...prev, primaryValidation: validation }));
  }, []);

  // 处理副文件验证变化
  const handleSecondaryValidationChange = useCallback((validation: FileValidationResult) => {
    setState(prev => ({ ...prev, secondaryValidation: validation }));
  }, []);

  // 处理对比选项变化
  const handleOptionsChange = useCallback((options: Partial<ComparisonOptions>) => {
    const newOptions = { ...state.comparisonOptions, ...options };
    setState(prev => ({
      ...prev,
      comparisonOptions: newOptions
    }));
    
    // 保存用户偏好到localStorage
    try {
      localStorage.setItem('fileCompareOptions', JSON.stringify(newOptions));
    } catch (error) {
      console.warn('无法保存用户偏好:', error);
    }
  }, [state.comparisonOptions]);

  // 模拟对比进度更新
  const simulateCompareProgress = useCallback(() => {
    const stages: CompareProgress[] = [
      { stage: 'uploading', progress: 10, description: '上传文件到服务器...' },
      { stage: 'parsing', progress: 30, description: '解析文档内容...' },
      { stage: 'analyzing', progress: 50, description: '分析文档结构...' },
      { stage: 'comparing', progress: 80, description: '执行智能对比...' },
      { stage: 'generating', progress: 100, description: '生成对比报告...' }
    ];

    let currentStageIndex = 0;
    const updateProgress = () => {
      if (currentStageIndex < stages.length && stages[currentStageIndex]) {
        const currentStage = stages[currentStageIndex];
        setCompareProgressInfo(currentStage);
        setState(prev => ({ ...prev, compareProgress: currentStage.progress || 0 }));
        currentStageIndex++;
        setTimeout(updateProgress, 1500);
      }
    };

    updateProgress();
  }, []);

  // 网络重试机制
  const executeWithRetry = useCallback(async (fn: () => Promise<Response>, maxRetries = 3): Promise<Response> => {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await fn();
        if (response.ok) {
          return response;
        }
        
        // 如果是服务器错误且不是最后一次尝试，则重试
        if (response.status >= 500 && attempt < maxRetries) {
          lastError = new Error(`服务器错误 ${response.status}，正在重试... (${attempt}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // 递增延迟
          continue;
        }
        
        throw new Error(`对比失败: ${response.status} ${response.statusText}`);
      } catch (error: any) {
        lastError = error;
        
        // 如果是网络错误且不是最后一次尝试，则重试
        if ((error.name === 'TypeError' || error.message.includes('fetch')) && attempt < maxRetries) {
          message.warning(`网络错误，正在重试... (${attempt}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          continue;
        }
        
        throw error;
      }
    }
    
    throw lastError!;
  }, []);

  // 执行文件对比
  const executeComparison = useCallback(async () => {
    if (!state.primaryFile || !state.secondaryFile) {
      message.error('请先上传两个文件');
      return;
    }

    // 文件大小检查
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (state.primaryFile.size > maxSize || state.secondaryFile.size > maxSize) {
      message.error('文件大小不能超过50MB');
      return;
    }

    setState(prev => ({ 
      ...prev, 
      isComparing: true, 
      compareProgress: 0, 
      error: null,
      comparisonResult: null 
    }));

    // 创建新的请求控制器
    compareRequestRef.current = new AbortController();

    try {
      // 开始进度模拟
      simulateCompareProgress();

      // 准备FormData
      const formData = new FormData();
      formData.append('primaryFile', state.primaryFile);
      formData.append('secondaryFile', state.secondaryFile);
      formData.append('options', JSON.stringify(state.comparisonOptions));

      console.log('🚀 [FileCompare] 开始文件对比:', {
        primaryFile: state.primaryFile.name,
        secondaryFile: state.secondaryFile.name,
        options: state.comparisonOptions
      });

      // 使用重试机制发送对比请求
      const response = await executeWithRetry(() => 
        fetch('/api/review/file-compare', {
          method: 'POST',
          body: formData,
          signal: compareRequestRef.current!.signal,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
      );

      const responseData = await response.json();
      
      // 检查API响应是否成功
      if (!responseData.success) {
        throw new Error(responseData.message || '文件对比失败');
      }
      
      // 提取实际的对比结果数据
      const result: FileComparisonResult = responseData.data;
      
      console.log('✅ [FileCompare] 对比完成:', result);

      // 提取修复后的文件名
      const correctedPrimaryName = result.files?.primary?.name || null;
      const correctedSecondaryName = result.files?.secondary?.name || null;

      setState(prev => ({
        ...prev,
        comparisonResult: result,
        correctedPrimaryFileName: correctedPrimaryName,
        correctedSecondaryFileName: correctedSecondaryName,
        isComparing: false,
        currentStep: 3 // 自动跳转到结果页面
      }));

      message.success('文件对比完成！');

    } catch (error: any) {
      console.error('❌ [FileCompare] 对比失败:', error);
      
      if (error.name === 'AbortError') {
        message.info('对比已取消');
      } else {
        const errorMessage = error.message || '文件对比失败，请重试';
        setState(prev => ({ ...prev, error: errorMessage }));
        message.error(errorMessage);
      }
    } finally {
      setState(prev => ({ ...prev, isComparing: false }));
      compareRequestRef.current = null;
    }
  }, [state.primaryFile, state.secondaryFile, state.comparisonOptions, simulateCompareProgress, executeWithRetry]);

  // 取消对比
  const cancelComparison = useCallback(() => {
    if (compareRequestRef.current) {
      compareRequestRef.current.abort();
      compareRequestRef.current = null;
    }
    setState(prev => ({ ...prev, isComparing: false, compareProgress: 0 }));
    
    // 重置进度信息
    setCompareProgressInfo({
      stage: 'uploading',
      progress: 0,
      description: '准备上传文件...'
    });
  }, []);

  // 获取文件信息
  const getFileInfo = useCallback((file: File, correctedName?: string) => {
    const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
    const lastModified = new Date(file.lastModified).toLocaleString('zh-CN');
    const displayName = correctedName || file.name;
    
    return {
      name: displayName,
      size: `${sizeInMB} MB`,
      type: file.type || '未知类型',
      lastModified,
      extension: displayName.split('.').pop()?.toUpperCase() || '无扩展名'
    };
  }, []);

  // 重置对比
  const resetComparison = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentStep: 0,
      primaryFile: null,
      secondaryFile: null,
      primaryValidation: null,
      secondaryValidation: null,
      comparisonResult: null,
      isComparing: false,
      compareProgress: 0,
      error: null,
      selectedDifferenceId: null,
      correctedPrimaryFileName: null,
      correctedSecondaryFileName: null
    }));
    
    // 重置进度信息
    setCompareProgressInfo({
      stage: 'uploading',
      progress: 0,
      description: '准备上传文件...'
    });
  }, []);

  // 处理差异项点击
  const handleDifferenceClick = useCallback((difference: DifferenceItem) => {
    setState(prev => ({ ...prev, selectedDifferenceId: difference.id }));
  }, []);

  // 处理导出
  const handleExport = useCallback((format: 'pdf' | 'excel' | 'word') => {
    if (!state.comparisonResult) {
      message.error('没有可导出的对比结果');
      return;
    }

    // 这里应该调用实际的导出API
    message.success(`正在导出${format.toUpperCase()}格式的对比报告...`);
    
    // 模拟导出过程
    setTimeout(() => {
      message.success('导出完成！');
    }, 2000);
  }, [state.comparisonResult]);

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + Enter: 执行对比
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        if (state.currentStep === 1 && state.primaryFile && state.secondaryFile && !state.isComparing) {
          event.preventDefault();
          executeComparison();
        }
      }
      
      // Escape: 取消对比或重置
      if (event.key === 'Escape') {
        if (state.isComparing) {
          event.preventDefault();
          cancelComparison();
        } else if (state.currentStep > 0) {
          event.preventDefault();
          resetComparison();
        }
      }
      
      // Ctrl/Cmd + R: 重置
      if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        if (state.currentStep > 0) {
          event.preventDefault();
          resetComparison();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [state.currentStep, state.primaryFile, state.secondaryFile, state.isComparing, executeComparison, cancelComparison, resetComparison]);

  // 下一步
  const nextStep = useCallback(() => {
    if (state.currentStep === 2) {
      // 在对比步骤，执行对比
      executeComparison();
    } else {
      setState(prev => ({ ...prev, currentStep: prev.currentStep + 1 }));
    }
  }, [state.currentStep, executeComparison]);

  // 上一步
  const prevStep = useCallback(() => {
    setState(prev => ({ ...prev, currentStep: Math.max(0, prev.currentStep - 1) }));
  }, []);

  // 渲染文件上传步骤
  const renderUploadStep = () => (
    <div className="space-y-6">
      <Row gutter={24}>
        <Col span={12}>
          <FileUploadArea
            title="主文件"
            description="选择作为对比基准的文件"
            required
            file={state.primaryFile}
            onFileChange={handlePrimaryFileChange}
            onValidationChange={handlePrimaryValidationChange}
          />
          
          {state.primaryFile && (
            <Card size="small" className="bg-blue-50 mt-4">
              <div className="text-sm">
                <div className="font-medium text-blue-800 mb-2">主文件信息</div>
                {(() => {
                  const info = getFileInfo(state.primaryFile, state.correctedPrimaryFileName || undefined);
                  return (
                    <div className="grid grid-cols-2 gap-2 text-gray-600">
                      <div>文件名: {info.name}</div>
                      <div>大小: {info.size}</div>
                      <div>类型: {info.extension}</div>
                      <div>修改时间: {info.lastModified}</div>
                    </div>
                  );
                })()}
              </div>
            </Card>
          )}
        </Col>
        <Col span={12}>
          <FileUploadArea
            title="副文件"
            description="选择要与主文件对比的文件"
            required
            file={state.secondaryFile}
            onFileChange={handleSecondaryFileChange}
            onValidationChange={handleSecondaryValidationChange}
          />
          
          {state.secondaryFile && (
            <Card size="small" className="bg-green-50 mt-4">
              <div className="text-sm">
                <div className="font-medium text-green-800 mb-2">副文件信息</div>
                {(() => {
                  const info = getFileInfo(state.secondaryFile, state.correctedSecondaryFileName || undefined);
                  return (
                    <div className="grid grid-cols-2 gap-2 text-gray-600">
                      <div>文件名: {info.name}</div>
                      <div>大小: {info.size}</div>
                      <div>类型: {info.extension}</div>
                      <div>修改时间: {info.lastModified}</div>
                    </div>
                  );
                })()}
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );

  // 渲染配置选项步骤
  const renderOptionsStep = () => (
    <Card title="对比配置" className="max-w-2xl mx-auto">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <Text strong>忽略格式差异</Text>
            <div className="text-sm text-gray-500">忽略字体、颜色等格式差异</div>
          </div>
          <Button
            type={state.comparisonOptions.ignoreFormatting ? 'primary' : 'default'}
            onClick={() => handleOptionsChange({ ignoreFormatting: !state.comparisonOptions.ignoreFormatting })}
          >
            {state.comparisonOptions.ignoreFormatting ? '已启用' : '已禁用'}
          </Button>
        </div>

        <Divider />

        <div className="flex justify-between items-center">
          <div>
            <Text strong>忽略空白字符</Text>
            <div className="text-sm text-gray-500">忽略多余的空格、换行等</div>
          </div>
          <Button
            type={state.comparisonOptions.ignoreWhitespace ? 'primary' : 'default'}
            onClick={() => handleOptionsChange({ ignoreWhitespace: !state.comparisonOptions.ignoreWhitespace })}
          >
            {state.comparisonOptions.ignoreWhitespace ? '已启用' : '已禁用'}
          </Button>
        </div>

        <Divider />

        <div className="flex justify-between items-center">
          <div>
            <Text strong>智能条款对比</Text>
            <div className="text-sm text-gray-500">启用条款级别的智能分析</div>
          </div>
          <Button
            type={state.comparisonOptions.enableClauseComparison ? 'primary' : 'default'}
            onClick={() => handleOptionsChange({ enableClauseComparison: !state.comparisonOptions.enableClauseComparison })}
          >
            {state.comparisonOptions.enableClauseComparison ? '已启用' : '已禁用'}
          </Button>
        </div>

        <Divider />

        <div className="flex justify-between items-center">
          <div>
            <Text strong>生成改进建议</Text>
            <div className="text-sm text-gray-500">为发现的差异生成改进建议</div>
          </div>
          <Button
            type={state.comparisonOptions.generateSuggestions ? 'primary' : 'default'}
            onClick={() => handleOptionsChange({ generateSuggestions: !state.comparisonOptions.generateSuggestions })}
          >
            {state.comparisonOptions.generateSuggestions ? '已启用' : '已禁用'}
          </Button>
        </div>
      </div>
    </Card>
  );

  // 渲染对比执行步骤
  const renderCompareStep = () => (
    <div className="max-w-2xl mx-auto">
      {state.isComparing ? (
        <Card>
          <div className="text-center space-y-4">
            <Spin size="large" />
            <Title level={4}>正在执行文件对比</Title>
            <Text type="secondary">{compareProgressInfo?.description || '正在处理...'}</Text>
            
            <Progress 
              percent={isNaN(state.compareProgress) ? 0 : state.compareProgress} 
              status="active"
              strokeColor={{
                '0%': '#108ee9',
                '100%': '#87d068',
              }}
            />
            
            <div className="flex justify-center">
              <Button onClick={cancelComparison}>
                取消对比
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <Card>
          <div className="text-center space-y-4">
            <DiffOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            <Title level={4}>准备执行对比</Title>
            <Paragraph type="secondary">
              将对比以下两个文件：
            </Paragraph>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <Text strong>主文件：</Text>
                <Text>{state.primaryFile?.name}</Text>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                <Text strong>副文件：</Text>
                <Text>{state.secondaryFile?.name}</Text>
              </div>
            </div>

            {state.error && (
              <Alert
                message="对比失败"
                description={state.error}
                type="error"
                showIcon
                action={
                  <Button size="small" onClick={() => setState(prev => ({ ...prev, error: null }))}>
                    关闭
                  </Button>
                }
              />
            )}
          </div>
        </Card>
      )}
    </div>
  );

  // 渲染结果步骤
  const renderResultStep = () => (
    <Row gutter={24}>
      <Col span={6}>
        <DiffNavigator
          result={state.comparisonResult}
          selectedDifferenceId={state.selectedDifferenceId}
          onDifferenceClick={handleDifferenceClick}
          onExport={handleExport}
        />
      </Col>
      <Col span={18}>
        <ComparisonResultView
          result={state.comparisonResult}
          onDifferenceClick={handleDifferenceClick}
          onExport={handleExport}
        />
      </Col>
    </Row>
  );

  // 渲染当前步骤内容
  const renderStepContent = () => {
    switch (state.currentStep) {
      case 0:
        return renderUploadStep();
      case 1:
        return renderOptionsStep();
      case 2:
        return renderCompareStep();
      case 3:
        return renderResultStep();
      default:
        return null;
    }
  };

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Content className="p-6">
        {/* 面包屑导航 */}
        <Breadcrumb className="mb-6">
          <Breadcrumb.Item>
            <HomeOutlined />
            <span 
              className="cursor-pointer ml-1" 
              onClick={() => navigate('/')}
            >
              首页
            </span>
          </Breadcrumb.Item>
          <Breadcrumb.Item>文件对比</Breadcrumb.Item>
        </Breadcrumb>

        {/* 页面标题 */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <Title level={2} className="mb-2">
              文件对比分析
            </Title>
            <Text type="secondary">
              智能对比两个文档，识别差异并生成详细分析报告
            </Text>
          </div>
          
          <Space>
            {state.currentStep > 0 && (
              <Button 
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/')}
              >
                返回首页
              </Button>
            )}
            {state.comparisonResult && (
              <Button 
                icon={<ReloadOutlined />}
                onClick={resetComparison}
              >
                重新开始
              </Button>
            )}
          </Space>
        </div>

        {/* 步骤指示器 */}
        <Card className="mb-6">
          <Steps 
            current={state.currentStep} 
            items={steps}
            className="mb-6"
          />
          
          {/* 步骤操作按钮 */}
          <div className="flex justify-between">
            <Button 
              onClick={prevStep}
              disabled={state.currentStep === 0 || state.isComparing}
            >
              上一步
            </Button>
            
            <div className="flex space-x-2">
              {state.currentStep < 3 && (
                <Button 
                  type="primary"
                  onClick={nextStep}
                  disabled={
                    state.currentStep === 2 
                      ? state.isComparing  // 第2步只在正在对比时禁用
                      : (!canProceedToNextStep() || state.isComparing)  // 其他步骤使用原逻辑
                  }
                  loading={state.isComparing && state.currentStep === 2}
                >
                  {state.currentStep === 2 ? '开始对比' : '下一步'}
                </Button>
              )}
            </div>
          </div>
        </Card>

        {/* 步骤内容 */}
        <div className="mb-6">
          {renderStepContent()}
        </div>

        {/* 回到顶部 */}
        <BackTop />
      </Content>
    </Layout>
  );
};

export default FileCompare;