import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typo<PERSON>, 
  Button, 
  Tag, 
  Progress, 
  Spin, 
  Alert, 
  Descriptions, 
  List, 
  Space, 
  Divider, 
  Tabs, 
  Table, 
  Badge,
  Statistic,
  Row,
  Col,
  message,
  Modal
} from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  WarningOutlined, 
  InfoCircleOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  PrinterOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { useParams, useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface ReviewResult {
  id: string;
  contract_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  risk_level: string;
  risk_score: number;
  summary: {
    total_issues: number;
    critical_issues: number;
    high_risk_issues: number;
    medium_risk_issues: number;
    low_risk_issues: number;
    compliance_score: number;
  };
  suggestions: Array<{
    id: string;
    type: 'critical' | 'high' | 'medium' | 'low';
    category: string;
    title: string;
    description: string;
    suggestion: string;
    clause_reference?: string;
  }>;
  compliance_analysis: Array<{
    regulation: string;
    status: 'compliant' | 'non_compliant' | 'partial';
    details: string;
  }>;
  created_at: string;
  completed_at?: string;
}

const ReviewResult: React.FC = () => {
  const { colors } = useTheme();
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  
  const [result, setResult] = useState<ReviewResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);
  
  useEffect(() => {
    if (taskId) {
      fetchReviewResult(taskId);
    }
  }, [taskId]);
  
  const fetchReviewResult = async (id: string) => {
    try {
      setLoading(true);
      
      const response = await fetch(`http://localhost:3001/api/review/result/${id}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setResult(data.result);
      } else {
        message.error('获取审查结果失败');
      }
    } catch (error) {
      console.error('获取审查结果失败:', error);
      message.error('获取审查结果失败');
    } finally {
      setLoading(false);
    }
  };
  
  const handleExport = async (format: 'pdf' | 'excel' | 'json') => {
    try {
      setExporting(true);
      
      const response = await fetch(`http://localhost:3001/api/review/export/${taskId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ format })
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `review-result-${taskId}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        message.success('导出成功');
      } else {
        message.error('导出失败');
      }
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    } finally {
      setExporting(false);
    }
  };
  
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'green';
      case 'medium': return 'orange';
      case 'high': return 'red';
      case 'critical': return 'red';
      default: return 'default';
    }
  };
  
  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'critical': return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'high': return <WarningOutlined style={{ color: '#fa8c16' }} />;
      case 'medium': return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      case 'low': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      default: return <InfoCircleOutlined />;
    }
  };
  
  const getComplianceStatus = (status: string) => {
    switch (status) {
      case 'compliant': return <Badge status="success" text="合规" />;
      case 'non_compliant': return <Badge status="error" text="不合规" />;
      case 'partial': return <Badge status="warning" text="部分合规" />;
      default: return <Badge status="default" text="未知" />;
    }
  };
  
  const suggestionColumns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type: string) => (
        <Tag color={getRiskLevelColor(type)}>
          {type === 'critical' && '严重'}
          {type === 'high' && '高'}
          {type === 'medium' && '中'}
          {type === 'low' && '低'}
        </Tag>
      )
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 120
    },
    {
      title: '问题描述',
      dataIndex: 'title',
      key: 'title',
      render: (title: string, record: any) => (
        <div>
          <Text strong>{title}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: '建议',
      dataIndex: 'suggestion',
      key: 'suggestion'
    },
    {
      title: '条款引用',
      dataIndex: 'clause_reference',
      key: 'clause_reference',
      width: 120,
      render: (ref: string) => ref ? <Tag>{ref}</Tag> : '-'
    }
  ];

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>
          <Text style={{ color: colors.textSecondary }}>正在加载审查结果...</Text>
        </div>
      </div>
    );
  }
  
  if (!result) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="未找到审查结果"
          description="请检查任务ID是否正确，或联系管理员。"
          type="error"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/review')}
            style={{ marginRight: '16px' }}
          >
            返回
          </Button>
          <Title level={2} style={{ color: colors.text, display: 'inline', margin: 0 }}>
            审查结果
          </Title>
        </div>
        <Space>
          <Button 
            icon={<DownloadOutlined />} 
            onClick={() => handleExport('pdf')}
            loading={exporting}
          >
            导出PDF
          </Button>
          <Button 
            icon={<DownloadOutlined />} 
            onClick={() => handleExport('excel')}
            loading={exporting}
          >
            导出Excel
          </Button>
          <Button icon={<ShareAltOutlined />}>分享</Button>
          <Button icon={<PrinterOutlined />}>打印</Button>
        </Space>
      </div>
      
      {/* 审查状态和进度 */}
      {result.status === 'processing' && (
        <Alert
          message="审查进行中"
          description={
            <div>
              <Progress percent={isNaN(result.progress) ? 0 : result.progress} />
              <Text type="secondary">预计还需要几分钟完成...</Text>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}
      
      {/* 概览统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
            <Statistic
              title="风险评分"
              value={result.risk_score}
              suffix="/ 100"
              valueStyle={{ color: getRiskLevelColor(result.risk_level) === 'red' ? '#ff4d4f' : '#52c41a' }}
            />
            <Tag color={getRiskLevelColor(result.risk_level)} style={{ marginTop: '8px' }}>
              {result.risk_level === 'low' && '低风险'}
              {result.risk_level === 'medium' && '中风险'}
              {result.risk_level === 'high' && '高风险'}
              {result.risk_level === 'critical' && '严重风险'}
            </Tag>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
            <Statistic
              title="合规评分"
              value={result.summary.compliance_score}
              suffix="/ 100"
              valueStyle={{ color: result.summary.compliance_score >= 80 ? '#52c41a' : '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
            <Statistic
              title="发现问题"
              value={result.summary.total_issues}
              suffix="个"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
            <Statistic
              title="严重问题"
              value={result.summary.critical_issues}
              suffix="个"
              valueStyle={{ color: result.summary.critical_issues > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>
      
      {/* 详细结果 */}
      <Card 
        style={{ backgroundColor: colors.surface, borderColor: colors.border }}
        headStyle={{ color: colors.text, borderBottomColor: colors.border }}
      >
        <Tabs defaultActiveKey="summary">
          <TabPane tab="审查摘要" key="summary">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="任务ID">{result.id}</Descriptions.Item>
              <Descriptions.Item label="合同ID">{result.contract_id}</Descriptions.Item>
              <Descriptions.Item label="开始时间">
                {new Date(result.created_at).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="完成时间">
                {result.completed_at ? new Date(result.completed_at).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="总问题数" span={2}>
                <Space>
                  <Tag color="red">严重: {result.summary.critical_issues}</Tag>
                  <Tag color="orange">高风险: {result.summary.high_risk_issues}</Tag>
                  <Tag color="yellow">中风险: {result.summary.medium_risk_issues}</Tag>
                  <Tag color="green">低风险: {result.summary.low_risk_issues}</Tag>
                </Space>
              </Descriptions.Item>
            </Descriptions>
          </TabPane>
          
          <TabPane tab="问题与建议" key="suggestions">
            <Table
              dataSource={result.suggestions}
              columns={suggestionColumns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              size="middle"
            />
          </TabPane>
          
          <TabPane tab="合规性分析" key="compliance">
            <List
              dataSource={result.compliance_analysis}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text strong>{item.regulation}</Text>
                        {getComplianceStatus(item.status)}
                      </Space>
                    }
                    description={item.details}
                  />
                </List.Item>
              )}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ReviewResult;