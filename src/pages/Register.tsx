import React, { useState } from 'react';
import { Form, Input, Button, Card, Typography, message, Select, Divider } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, PhoneOutlined, TeamOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

const { Title, Text } = Typography;
const { Option } = Select;

interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
  role: 'legal_manager' | 'legal_staff';
  department?: string;
  phone?: string;
}

const Register: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { register } = useAuth();
  const { colors } = useTheme();
  const navigate = useNavigate();

  const onFinish = async (values: RegisterForm) => {
    setLoading(true);
    try {
      const { confirmPassword, ...registerData } = values;
      const result = await register(registerData);
      if (result.success) {
        message.success('注册成功！请登录');
        navigate('/login');
      } else {
        message.error(result.message || '注册失败');
      }
    } catch (error) {
      message.error('注册过程中发生错误');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div 
      className="min-h-screen flex items-center justify-center px-4 py-8"
      style={{ backgroundColor: colors.background }}
    >
      <div className="w-full max-w-md">
        {/* Logo和标题 */}
        <div className="text-center mb-8">
          <div 
            className="w-16 h-16 mx-auto rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-4"
            style={{ backgroundColor: colors.primary }}
          >
            C
          </div>
          <Title level={2} style={{ color: colors.text, marginBottom: 8 }}>
            注册账号
          </Title>
          <Text type="secondary" style={{ color: colors.textSecondary }}>
            创建您的智能合同审查系统账号
          </Text>
        </div>

        {/* 注册表单 */}
        <Card 
          style={{ 
            backgroundColor: colors.surface,
            borderColor: colors.border,
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
          }}
        >
          <Form
            form={form}
            name="register"
            onFinish={onFinish}
            layout="vertical"
            size="large"
            autoComplete="off"
            scrollToFirstError
          >
            <Form.Item
              name="username"
              label={<span style={{ color: colors.text }}>用户名</span>}
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3位字符' },
                { max: 20, message: '用户名最多20位字符' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
              ]}
            >
              <Input
                prefix={<UserOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请输入用户名"
                autoComplete="username"
              />
            </Form.Item>

            <Form.Item
              name="name"
              label={<span style={{ color: colors.text }}>真实姓名</span>}
              rules={[
                { required: true, message: '请输入真实姓名' },
                { min: 2, message: '姓名至少2位字符' },
                { max: 10, message: '姓名最多10位字符' }
              ]}
            >
              <Input
                prefix={<TeamOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请输入真实姓名"
                autoComplete="name"
              />
            </Form.Item>

            <Form.Item
              name="email"
              label={<span style={{ color: colors.text }}>邮箱</span>}
              rules={[
                { required: true, message: '请输入邮箱地址' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input
                prefix={<MailOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请输入邮箱地址"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="role"
              label={<span style={{ color: colors.text }}>角色</span>}
              rules={[{ required: true, message: '请选择角色' }]}
            >
              <Select placeholder="请选择您的角色">
                <Option value="legal_manager">法务主管</Option>
                <Option value="legal_staff">法务专员</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="department"
              label={<span style={{ color: colors.text }}>部门</span>}
            >
              <Input
                prefix={<TeamOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请输入所属部门（可选）"
              />
            </Form.Item>

            <Form.Item
              name="phone"
              label={<span style={{ color: colors.text }}>手机号</span>}
              rules={[
                { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
              ]}
            >
              <Input
                prefix={<PhoneOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请输入手机号（可选）"
                autoComplete="tel"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label={<span style={{ color: colors.text }}>密码</span>}
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位字符' },
                { max: 20, message: '密码最多20位字符' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请输入密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label={<span style={{ color: colors.text }}>确认密码</span>}
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: colors.textSecondary }} />}
                placeholder="请再次输入密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                style={{
                  backgroundColor: colors.primary,
                  borderColor: colors.primary,
                  height: '48px',
                  fontSize: '16px'
                }}
              >
                {loading ? '注册中...' : '注册'}
              </Button>
            </Form.Item>
          </Form>

          <Divider style={{ borderColor: colors.border }}>
            <Text type="secondary" style={{ color: colors.textSecondary }}>或</Text>
          </Divider>

          <div className="text-center">
            <Text style={{ color: colors.textSecondary }}>已有账号？</Text>
            <Link 
              to="/login" 
              style={{ 
                color: colors.primary,
                marginLeft: '8px',
                textDecoration: 'none'
              }}
            >
              立即登录
            </Link>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Register;