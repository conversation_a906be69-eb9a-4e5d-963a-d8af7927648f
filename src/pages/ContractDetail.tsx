import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Descriptions, 
  Tag, 
  Button, 
  Space, 
  Spin, 
  message, 
  Modal,
  Tooltip,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Upload
} from 'antd';
import { 
  useParams, 
  useNavigate, 
  useSearchParams 
} from 'react-router-dom';
import { 
  EditOutlined, 
  ArrowLeftOutlined, 
  FileTextOutlined,
  DownloadOutlined,
  SaveOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import FilePreview from '../components/FilePreview';
import dayjs from 'dayjs';
import type { UploadFile } from 'antd/es/upload/interface';
import { getContractTypeOptions, getContractTypeLabel } from '../constants/contractTypes';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface Contract {
  id: string;
  title: string;
  category: string; // 修复：使用数据库实际字段名
  status: 'draft' | 'reviewing' | 'approved' | 'rejected' | 'signed' | 'expired' | 'uploaded' | 'processing' | 'reviewed' | 'archived'; // 修复：包含所有数据库状态
  counterparty: string;
  amount?: number;
  start_date?: string; // 修复：使用数据库字段名
  end_date?: string; // 修复：使用数据库字段名
  created_at: string; // 修复：使用数据库字段名
  updated_at: string; // 修复：使用数据库字段名
  user_id: string; // 修复：使用数据库字段名
  risk_level: 'low' | 'medium' | 'high' | 'critical'; // 修复：使用数据库字段名并包含所有值
  file_path?: string;
  file_url?: string; // 新增：数据库中的字段
  content?: string;
  ocr_content?: string; // 新增：数据库中的字段
  
  // 为了向后兼容，保留前端使用的字段名作为计算属性
  type?: string; // 映射到category
  startDate?: string; // 映射到start_date
  endDate?: string; // 映射到end_date
  createdAt?: string; // 映射到created_at
  updatedAt?: string; // 映射到updated_at
  createdBy?: string; // 映射到user_id
  riskLevel?: 'low' | 'medium' | 'high' | 'critical'; // 映射到risk_level
}

const ContractDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { colors } = useTheme();
  const { token } = useAuth();
  const [form] = Form.useForm();
  const [contract, setContract] = useState<Contract | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<{
    filePath?: string;
    fileName?: string;
    fileUrl?: string;
    mimeType?: string;
  }>({});
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const mode = searchParams.get('mode'); // 'edit' 模式
  const isEditMode = mode === 'edit';

  // 状态标签配置
  const statusConfig = {
    draft: { color: 'default', text: '草稿' },
    reviewing: { color: 'processing', text: '审查中' },
    approved: { color: 'success', text: '已批准' },
    rejected: { color: 'error', text: '已拒绝' },
    signed: { color: 'blue', text: '已签署' },
    expired: { color: 'warning', text: '已过期' },
    uploaded: { color: 'cyan', text: '已上传' },
    processing: { color: 'processing', text: '处理中' },
    reviewed: { color: 'green', text: '已审查' },
    archived: { color: 'default', text: '已归档' }
  } as const;

  // 风险等级配置
  const riskConfig = {
    low: { color: 'green', text: '低风险' },
    medium: { color: 'orange', text: '中风险' },
    high: { color: 'red', text: '高风险' },
    critical: { color: 'magenta', text: '严重风险' }
  };

  // 获取合同详情
  const fetchContractDetail = async () => {
    if (!id) return;
    
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:3001/api/contracts/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const contractData = data.contract;
          setContract({
            ...contractData,
            type: contractData.category,
            startDate: contractData.start_date,
            endDate: contractData.end_date,
            createdAt: contractData.created_at,
            updatedAt: contractData.updated_at,
            riskLevel: contractData.risk_level
          });
          
          // 如果是编辑模式，填充表单数据
          if (isEditMode) {
            form.setFieldsValue({
              title: contractData.title,
              category: contractData.category, // 统一使用category字段
              status: contractData.status,
              counterparty: contractData.counterparty,
              amount: contractData.amount,
              start_date: contractData.start_date ? dayjs(contractData.start_date) : null,
              end_date: contractData.end_date ? dayjs(contractData.end_date) : null,
              risk_level: contractData.risk_level,
              content: contractData.content?.replace(/\\n/g, '\n') || ''
            });

            // 如果有文件，设置文件列表
            if (contractData.file_path) {
              setFileList([{
                uid: '-1',
                name: contractData.title + '.' + (contractData.file_path.split('.').pop() || 'pdf'),
                status: 'done',
                url: contractData.file_url
              }]);
            }
          }
        } else {
          message.error(data.message || '获取合同详情失败');
        }
      } else {
        const errorData = await response.json();
        message.error(errorData.message || '获取合同详情失败');
      }
    } catch (error) {
      console.error('Fetch contract detail error:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 文件预览
  const handleFilePreview = () => {
    if (!contract?.file_path) {
      message.warning('该合同没有关联文件');
      return;
    }

    // 根据文件扩展名推断MIME类型
    const getFileType = (filePath: string) => {
      const extension = filePath.toLowerCase().split('.').pop();
      switch (extension) {
        case 'pdf':
          return 'application/pdf';
        case 'doc':
          return 'application/msword';
        case 'docx':
          return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        case 'txt':
          return 'text/plain';
        default:
          return 'application/octet-stream';
      }
    };

    setPreviewFile({
      filePath: contract.file_path,
      fileName: contract.title + '.' + (contract.file_path.split('.').pop() || 'pdf'),
      fileUrl: contract.file_url,
      mimeType: getFileType(contract.file_path)
    });
    setPreviewVisible(true);
  };

  // 保存合同
  const handleSave = async (values: any) => {
    if (!id) return;
    
    setSaving(true);
    try {
      console.log('🔄 [编辑合同调试] 开始保存合同，表单数据:', values);
      
      const formData = new FormData();
      let hasFile = false;
      let fileInfo = null;
      
      // 首先处理文件（如果有新上传的文件）
      const newFile = fileList.find(file => file.originFileObj);
      if (newFile && newFile.originFileObj) {
        const file = newFile.originFileObj;
        console.log('📁 [编辑合同调试] 检测到新文件上传:', {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified
        });
        
        formData.append('file', file);
        hasFile = true;
        fileInfo = {
          name: file.name,
          size: file.size,
          type: file.type
        };
        console.log('✅ [编辑合同调试] 文件已添加到FormData:', fileInfo);
      } else {
        console.log('📄 [编辑合同调试] 未检测到新文件上传');
      }
      
      // 添加基本字段，确保前端字段名正确映射到后端数据库字段名
      formData.append('title', values.title);
      console.log('📝 [编辑合同调试] 字段 title:', values.title);
      
      formData.append('category', values.category); // 使用统一的category字段
              console.log('📝 [编辑合同调试] 字段 category:', values.category);
      
      formData.append('status', values.status);
      console.log('📝 [编辑合同调试] 字段 status:', values.status);
      
      formData.append('counterparty', values.counterparty || '');
      console.log('📝 [编辑合同调试] 字段 counterparty:', values.counterparty || '');
      
      formData.append('risk_level', values.risk_level || 'medium');
      console.log('📝 [编辑合同调试] 字段 risk_level:', values.risk_level || 'medium');
      
      if (values.amount) {
        formData.append('amount', values.amount.toString());
        console.log('📝 [编辑合同调试] 字段 amount:', values.amount.toString());
      }
      
      if (values.start_date) {
        const formattedStartDate = dayjs(values.start_date).format('YYYY-MM-DD');
        formData.append('start_date', formattedStartDate);
        console.log('📅 [编辑合同调试] 字段 start_date:', formattedStartDate);
      }
      
      if (values.end_date) {
        const formattedEndDate = dayjs(values.end_date).format('YYYY-MM-DD');
        formData.append('end_date', formattedEndDate);
        console.log('📅 [编辑合同调试] 字段 end_date:', formattedEndDate);
      }
      
      if (values.content) {
        // 将换行符转换为\n字符串格式存储
        const processedContent = values.content.replace(/\n/g, '\\n');
        formData.append('content', processedContent);
        console.log('📝 [编辑合同调试] 字段 content (已处理换行符):', processedContent.substring(0, 100) + '...');
      }
      
      // 打印FormData内容（用于调试）
      console.log('📦 [编辑合同调试] FormData 内容:');
      for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`  ${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
        } else {
          console.log(`  ${key}: ${value}`);
        }
      }
      
      if (hasFile) {
        console.log('🔍 [编辑合同调试] 准备发送文件进行文档解析...');
        message.loading('正在更新合同并解析文件...', 0);
      } else {
        console.log('📄 [编辑合同调试] 无新文件上传，仅更新合同信息');
        message.loading('正在更新合同...', 0);
      }

      const response = await fetch(`http://localhost:3001/api/contracts/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });
      
      message.destroy(); // 清除loading消息
      
      console.log('🌐 [编辑合同调试] 服务器响应状态:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ [编辑合同调试] 服务器响应数据:', data);
        
        if (data.success) {
          message.success('合同更新成功');
          
          // 如果有文档解析结果，显示解析状态
          if (data.parsing) {
            console.log('🔍 [编辑合同调试] 文档解析结果:', data.parsing);
            
            if (data.parsing.success && data.parsing.contentExtracted) {
              const wordCount = data.parsing.wordCount || '未知';
              console.log('✅ [编辑合同调试] 文档解析成功，提取词数:', wordCount);
              message.info(`文档解析成功，已提取 ${wordCount} 个词的内容`);
              
              // 如果解析出内容，提示用户刷新页面查看
              if (data.parsing.content) {
                console.log('📝 [编辑合同调试] 文档解析出内容，建议刷新页面查看');
                message.success('已自动填充解析的文档内容，页面将刷新');
              }
            } else if (data.parsing.error) {
              console.error('❌ [编辑合同调试] 文档解析失败:', data.parsing.error);
              message.warning(`文档解析失败: ${data.parsing.error}`);
            } else {
              console.log('⚠️ [编辑合同调试] 文档解析状态未知:', data.parsing);
              message.info('文档已上传，但解析状态未知');
            }
          } else if (hasFile) {
            console.log('⚠️ [编辑合同调试] 有文件上传但服务器未返回解析结果');
            message.warning('文件已上传，但未收到解析结果');
          }
          
          // 重新获取合同数据并退出编辑模式
          navigate(`/contracts/${id}`);
          fetchContractDetail();
        } else {
          console.error('❌ [编辑合同调试] 服务器返回失败状态:', data);
          message.error(data.message || '更新失败');
        }
      } else {
        const errorData = await response.json();
        console.error('❌ [编辑合同调试] 服务器错误响应:', errorData);
        message.error(errorData.message || '更新失败');
      }
    } catch (error) {
      console.error('Update contract error:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  // 编辑合同
  const handleEdit = () => {
    navigate(`/contracts/${id}?mode=edit`);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    navigate(`/contracts/${id}`);
  };

  // 文件上传配置
  const uploadProps = {
    fileList,
    beforeUpload: (file: File) => {
      const isValidType = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
      ].includes(file.type);
      
      if (!isValidType) {
        message.error('只能上传 PDF、Word 或文本文件！');
        return false;
      }
      
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过 10MB！');
        return false;
      }
      
      return false; // 阻止自动上传
    },
    onChange: (info: any) => {
      setFileList(info.fileList.slice(-1)); // 只保留最新的一个文件
    },
    onRemove: () => {
      setFileList([]);
    }
  };

  useEffect(() => {
    fetchContractDetail();
  }, [id]);

  // 当切换到编辑模式且已获取到合同数据时，填充表单，解决表单为空问题
  useEffect(() => {
    if (isEditMode && contract) {
      form.setFieldsValue({
        title: contract.title,
        category: contract.category,
        status: contract.status,
        counterparty: contract.counterparty,
        amount: contract.amount,
        start_date: contract.start_date ? dayjs(contract.start_date) : null,
        end_date: contract.end_date ? dayjs(contract.end_date) : null,
        risk_level: contract.risk_level,
        content: contract.content?.replace(/\\n/g, '\n') || ''
      });

      if (contract.file_path) {
        setFileList([{
          uid: '-1',
          name: contract.title + '.' + (contract.file_path.split('.').pop() || 'pdf'),
          status: 'done',
          url: contract.file_url
        }]);
      }
    }
  }, [isEditMode, contract, form]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!contract) {
    return (
      <div className="text-center py-8">
        <p style={{ color: colors.textSecondary }}>合同不存在或已被删除</p>
        <Button onClick={() => navigate('/contracts')}>返回合同列表</Button>
      </div>
    );
  }

  return (
    <div>
      {/* 页面头部 */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/contracts')}
          >
            返回列表
          </Button>
          <div>
            <Title level={2} style={{ color: colors.text, margin: 0 }}>
              {isEditMode ? '编辑合同' : contract.title}
            </Title>
            <p className="text-gray-500 mt-1">
              {isEditMode ? '修改合同信息' : '合同详情信息'}
            </p>
          </div>
        </div>
        <Space>
          {!isEditMode && contract.file_path && (
            <Tooltip title="预览文件">
              <Button 
                icon={<FileTextOutlined />} 
                onClick={handleFilePreview}
              >
                预览文件
              </Button>
            </Tooltip>
          )}
          {!isEditMode ? (
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              onClick={handleEdit}
              style={{ backgroundColor: colors.primary, borderColor: colors.primary }}
            >
              编辑合同
            </Button>
          ) : (
            <Space>
              <Button onClick={handleCancelEdit}>
                取消
              </Button>
              <Button 
                type="primary" 
                icon={<SaveOutlined />} 
                loading={saving}
                onClick={() => form.submit()}
                style={{ backgroundColor: colors.primary, borderColor: colors.primary }}
              >
                保存更改
              </Button>
            </Space>
          )}
        </Space>
      </div>

      {/* 合同基本信息 */}
      <Card 
        title={isEditMode ? "编辑合同信息" : "基本信息"} 
        className="mb-4"
        style={{ backgroundColor: colors.surface, borderColor: colors.border }}
      >
        {isEditMode ? (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSave}
            initialValues={{
              risk_level: 'medium',
              status: 'draft'
            }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Form.Item
                name="title"
                label="合同标题"
                rules={[{ required: true, message: '请输入合同标题' }]}
              >
                <Input placeholder="请输入合同标题" />
              </Form.Item>

              <Form.Item
                name="category"
                label="合同类型"
                rules={[{ required: true, message: '请选择合同类型' }]}
              >
                <Select placeholder="请选择合同类型">
                  {getContractTypeOptions().map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="counterparty"
                label="对方当事人"
                rules={[{ required: true, message: '请输入对方当事人' }]}
              >
                <Input placeholder="请输入对方当事人" />
              </Form.Item>

              <Form.Item
                name="amount"
                label="合同金额"
              >
                <InputNumber
                  placeholder="请输入合同金额"
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value: string | undefined) => value ? parseFloat(value.replace(/¥\s?|(,*)/g, '')) : 0}
                />
              </Form.Item>

              <Form.Item
                name="start_date"
                label="合同开始日期"
              >
                <DatePicker 
                  placeholder="请选择开始日期" 
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>

              <Form.Item
                name="end_date"
                label="合同结束日期"
              >
                <DatePicker 
                  placeholder="请选择结束日期" 
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                />
              </Form.Item>

              <Form.Item
                name="status"
                label="合同状态"
                rules={[{ required: true, message: '请选择合同状态' }]}
              >
                <Select placeholder="请选择合同状态">
                  <Option value="draft">草稿</Option>
                  <Option value="reviewing">审查中</Option>
                  <Option value="approved">已批准</Option>
                  <Option value="rejected">已拒绝</Option>
                  <Option value="signed">已签署</Option>
                  <Option value="expired">已过期</Option>
                  <Option value="uploaded">已上传</Option>
                  <Option value="processing">处理中</Option>
                  <Option value="reviewed">已审查</Option>
                  <Option value="archived">已归档</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="risk_level"
                label="风险等级"
                rules={[{ required: true, message: '请选择风险等级' }]}
              >
                <Select placeholder="请选择风险等级">
                  <Option value="low">低风险</Option>
                  <Option value="medium">中风险</Option>
                  <Option value="high">高风险</Option>
                  <Option value="critical">严重风险</Option>
                </Select>
              </Form.Item>
            </div>

            <Form.Item
              name="content"
              label="合同内容"
            >
              <TextArea 
                placeholder="请输入合同内容" 
                rows={6}
              />
            </Form.Item>

            <Form.Item
              label="合同文件"
            >
              <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />}>选择文件</Button>
              </Upload>
              <p className="text-gray-500 text-sm mt-1">
                支持 PDF、Word、文本文件，文件大小不超过 10MB
              </p>
            </Form.Item>
          </Form>
        ) : (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="合同标题">{contract.title}</Descriptions.Item>
            <Descriptions.Item label="合同类型">{getContractTypeLabel(contract.category || contract.type)}</Descriptions.Item>
            <Descriptions.Item label="对方当事人">{contract.counterparty}</Descriptions.Item>
            <Descriptions.Item label="合同金额">
              {contract.amount ? `¥${contract.amount.toLocaleString()}` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="合同状态">
              <Tag color={statusConfig[contract.status as keyof typeof statusConfig]?.color || 'default'}>
                {statusConfig[contract.status as keyof typeof statusConfig]?.text || contract.status}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="风险等级">
              <Tag color={riskConfig[contract.riskLevel as keyof typeof riskConfig]?.color || 'default'}>
                {riskConfig[contract.riskLevel as keyof typeof riskConfig]?.text || contract.riskLevel}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="开始日期">
              {contract.startDate ? dayjs(contract.startDate).format('YYYY-MM-DD') : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="结束日期">
              {contract.endDate ? dayjs(contract.endDate).format('YYYY-MM-DD') : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {dayjs(contract.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {dayjs(contract.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Card>

      {/* 合同内容 - 仅在查看模式下显示 */}
      {!isEditMode && contract.content && (
        <Card 
          title="合同内容" 
          className="mb-4"
          style={{ backgroundColor: colors.surface, borderColor: colors.border }}
        >
          <div 
            style={{ 
              color: colors.text, 
              whiteSpace: 'pre-wrap',
              lineHeight: '1.6',
              maxHeight: '400px',
              overflowY: 'auto'
            }}
          >
            {contract.content?.replace(/\\n/g, '\n') || '暂无内容'}
          </div>
        </Card>
      )}



      {/* 文件预览组件 */}
      <FilePreview
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        filePath={previewFile.filePath}
        fileName={previewFile.fileName}
        fileUrl={previewFile.fileUrl}
        mimeType={previewFile.mimeType}
      />
    </div>
  );
};

export default ContractDetail;