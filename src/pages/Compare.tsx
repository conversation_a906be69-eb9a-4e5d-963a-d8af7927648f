import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  Button, 
  Select, 
  Form, 
  message, 
  Spin, 
  Table, 
  Tag, 
  Space, 
  Divider, 
  Alert, 
  Row, 
  Col,
  Tabs,
  List,
  Tooltip,
  Progress
} from 'antd';
import { 
  SwapOutlined, 
  FileTextOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  DownloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

interface Contract {
  id: string;
  title: string;
  type: string;
  status: string;
  created_at: string;
}

interface ComparisonResult {
  id: string;
  contract1_id: string;
  contract2_id: string;
  differences: Array<{
    clause_type: string;
    contract1_content: string;
    contract2_content: string;
    difference_type: 'missing' | 'different' | 'additional';
    severity: 'low' | 'medium' | 'high';
    description: string;
    recommendation?: string;
  }>;
  similarities: Array<{
    clause_type: string;
    content: string;
    match_percentage: number;
  }>;
  summary: {
    total_clauses_compared: number;
    differences_found: number;
    similarity_score: number;
    risk_assessment: string;
  };
  created_at: string;
}

const Compare: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const [form] = Form.useForm();
  
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(false);
  const [comparing, setComparing] = useState(false);
  const [comparisonResult, setComparisonResult] = useState<ComparisonResult | null>(null);
  const [recentComparisons, setRecentComparisons] = useState<ComparisonResult[]>([]);
  
  useEffect(() => {
    fetchContracts();
    fetchRecentComparisons();
  }, []);
  
  const fetchContracts = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:3001/api/contracts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setContracts(data.contracts || []);
      }
    } catch (error) {
      console.error('获取合同列表失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchRecentComparisons = async () => {
    try {
      // 这里应该调用获取最近对比记录的API
      // 暂时使用模拟数据
      const mockComparisons: ComparisonResult[] = [
        {
          id: 'comp-1',
          contract1_id: 'contract-1',
          contract2_id: 'contract-2',
          differences: [],
          similarities: [],
          summary: {
            total_clauses_compared: 25,
            differences_found: 8,
            similarity_score: 78,
            risk_assessment: 'medium'
          },
          created_at: new Date(Date.now() - 86400000).toISOString()
        }
      ];
      setRecentComparisons(mockComparisons);
    } catch (error) {
      console.error('获取对比记录失败:', error);
    }
  };
  
  const handleCompare = async (values: any) => {
    if (values.contract1_id === values.contract2_id) {
      message.error('请选择不同的合同进行对比');
      return;
    }
    
    try {
      setComparing(true);
      
      const compareRequest = {
        contract1_id: values.contract1_id,
        contract2_id: values.contract2_id,
        comparison_type: values.comparison_type || 'full',
        focus_areas: values.focus_areas || []
      };
      
      const response = await fetch('http://localhost:3001/api/review/compare', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(compareRequest)
      });
      
      const data = await response.json();
      
      if (data.success) {
        setComparisonResult(data.result);
        message.success('对比完成');
        fetchRecentComparisons();
      } else {
        message.error(data.message || '对比失败');
      }
    } catch (error) {
      console.error('对比失败:', error);
      message.error('对比失败');
    } finally {
      setComparing(false);
    }
  };
  
  const getDifferenceColor = (type: string) => {
    switch (type) {
      case 'missing': return 'red';
      case 'different': return 'orange';
      case 'additional': return 'blue';
      default: return 'default';
    }
  };
  
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'default';
    }
  };
  
  const differenceColumns = [
    {
      title: '条款类型',
      dataIndex: 'clause_type',
      key: 'clause_type',
      width: 120
    },
    {
      title: '差异类型',
      dataIndex: 'difference_type',
      key: 'difference_type',
      width: 100,
      render: (type: string) => (
        <Tag color={getDifferenceColor(type)}>
          {type === 'missing' && '缺失'}
          {type === 'different' && '不同'}
          {type === 'additional' && '额外'}
        </Tag>
      )
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => (
        <Tag color={getSeverityColor(severity)}>
          {severity === 'high' && '高'}
          {severity === 'medium' && '中'}
          {severity === 'low' && '低'}
        </Tag>
      )
    },
    {
      title: '合同A内容',
      dataIndex: 'contract1_content',
      key: 'contract1_content',
      render: (content: string) => (
        <Tooltip title={content}>
          <Text ellipsis style={{ maxWidth: '200px', display: 'block' }}>
            {content || '-'}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '合同B内容',
      dataIndex: 'contract2_content',
      key: 'contract2_content',
      render: (content: string) => (
        <Tooltip title={content}>
          <Text ellipsis style={{ maxWidth: '200px', display: 'block' }}>
            {content || '-'}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '建议',
      dataIndex: 'recommendation',
      key: 'recommendation',
      render: (rec: string) => rec || '-'
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ color: colors.text, marginBottom: '24px' }}>
        条款对比
      </Title>
      
      <Row gutter={[24, 24]}>
        {/* 对比设置 */}
        <Col span={24}>
          <Card 
            title="设置对比"
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            headStyle={{ color: colors.text, borderBottomColor: colors.border }}
          >
            <Form
              form={form}
              layout="inline"
              onFinish={handleCompare}
              style={{ width: '100%' }}
            >
              <Form.Item
                name="contract1_id"
                label={<span style={{ color: colors.text }}>合同A</span>}
                rules={[{ required: true, message: '请选择合同A' }]}
                style={{ minWidth: '200px' }}
              >
                <Select placeholder="选择合同A" loading={loading}>
                  {contracts.map(contract => (
                    <Option key={contract.id} value={contract.id}>
                      <Space>
                        <FileTextOutlined />
                        {contract.title}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Form.Item style={{ margin: '0 16px' }}>
                <SwapOutlined style={{ color: colors.primary, fontSize: '18px' }} />
              </Form.Item>
              
              <Form.Item
                name="contract2_id"
                label={<span style={{ color: colors.text }}>合同B</span>}
                rules={[{ required: true, message: '请选择合同B' }]}
                style={{ minWidth: '200px' }}
              >
                <Select placeholder="选择合同B" loading={loading}>
                  {contracts.map(contract => (
                    <Option key={contract.id} value={contract.id}>
                      <Space>
                        <FileTextOutlined />
                        {contract.title}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              
              <Form.Item
                name="comparison_type"
                label={<span style={{ color: colors.text }}>对比类型</span>}
                initialValue="full"
                style={{ minWidth: '150px' }}
              >
                <Select>
                  <Option value="full">全面对比</Option>
                  <Option value="key_clauses">关键条款</Option>
                  <Option value="risk_clauses">风险条款</Option>
                </Select>
              </Form.Item>
              
              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  loading={comparing}
                  icon={<SwapOutlined />}
                >
                  开始对比
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>
        
        {/* 对比结果 */}
        {comparisonResult && (
          <Col span={24}>
            <Card 
              title="对比结果"
              style={{ backgroundColor: colors.surface, borderColor: colors.border }}
              headStyle={{ color: colors.text, borderBottomColor: colors.border }}
              extra={
                <Button 
                  icon={<DownloadOutlined />}
                  type="link"
                >
                  导出报告
                </Button>
              }
            >
              {/* 对比摘要 */}
              <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
                <Col span={6}>
                  <Card size="small" style={{ backgroundColor: colors.background }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: colors.primary }}>
                        {comparisonResult.summary.similarity_score}%
                      </div>
                      <div style={{ color: colors.textSecondary }}>相似度</div>
                    </div>
                  </Card>
                </Col>
                <Col span={6}>
                  <Card size="small" style={{ backgroundColor: colors.background }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: colors.text }}>
                        {comparisonResult.summary.total_clauses_compared}
                      </div>
                      <div style={{ color: colors.textSecondary }}>对比条款数</div>
                    </div>
                  </Card>
                </Col>
                <Col span={6}>
                  <Card size="small" style={{ backgroundColor: colors.background }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                        {comparisonResult.summary.differences_found}
                      </div>
                      <div style={{ color: colors.textSecondary }}>发现差异</div>
                    </div>
                  </Card>
                </Col>
                <Col span={6}>
                  <Card size="small" style={{ backgroundColor: colors.background }}>
                    <div style={{ textAlign: 'center' }}>
                      <Tag color={getSeverityColor(comparisonResult.summary.risk_assessment)}>
                        {comparisonResult.summary.risk_assessment === 'high' && '高风险'}
                        {comparisonResult.summary.risk_assessment === 'medium' && '中风险'}
                        {comparisonResult.summary.risk_assessment === 'low' && '低风险'}
                      </Tag>
                      <div style={{ color: colors.textSecondary, marginTop: '4px' }}>风险评估</div>
                    </div>
                  </Card>
                </Col>
              </Row>
              
              <Tabs defaultActiveKey="differences">
                <TabPane tab={`差异分析 (${comparisonResult.differences.length})`} key="differences">
                  <Table
                    dataSource={comparisonResult.differences}
                    columns={differenceColumns}
                    rowKey={(record, index) => `diff-${index}`}
                    pagination={{ pageSize: 10 }}
                    size="middle"
                    scroll={{ x: 1200 }}
                  />
                </TabPane>
                
                <TabPane tab={`相似条款 (${comparisonResult.similarities.length})`} key="similarities">
                  <List
                    dataSource={comparisonResult.similarities}
                    renderItem={(item, index) => (
                      <List.Item>
                        <List.Item.Meta
                          title={
                            <Space>
                              <Text strong>{item.clause_type}</Text>
                              <Progress 
                                percent={isNaN(item.match_percentage) ? 0 : item.match_percentage} 
                                size="small" 
                                style={{ width: '100px' }}
                              />
                              <Text type="secondary">{(isNaN(item.match_percentage) ? 0 : item.match_percentage)}% 匹配</Text>
                            </Space>
                          }
                          description={item.content}
                        />
                      </List.Item>
                    )}
                  />
                </TabPane>
              </Tabs>
            </Card>
          </Col>
        )}
        
        {/* 最近对比记录 */}
        <Col span={24}>
          <Card 
            title="最近对比记录"
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            headStyle={{ color: colors.text, borderBottomColor: colors.border }}
          >
            <List
              dataSource={recentComparisons}
              renderItem={comparison => {
                const contract1 = contracts.find(c => c.id === comparison.contract1_id);
                const contract2 = contracts.find(c => c.id === comparison.contract2_id);
                return (
                  <List.Item
                    actions={[
                      <Button 
                        type="link" 
                        icon={<EyeOutlined />}
                        onClick={() => setComparisonResult(comparison)}
                      >
                        查看
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <Text style={{ color: colors.text }}>
                            {contract1?.title || '未知合同'} vs {contract2?.title || '未知合同'}
                          </Text>
                          <Tag color="blue">{comparison.summary.similarity_score}% 相似</Tag>
                        </Space>
                      }
                      description={
                        <Space>
                          <Text type="secondary">
                            {new Date(comparison.created_at).toLocaleString()}
                          </Text>
                          <Text type="secondary">
                            发现 {comparison.summary.differences_found} 处差异
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                );
              }}
              locale={{ emptyText: '暂无对比记录' }}
            />
          </Card>
        </Col>
      </Row>
      
      {/* 使用说明 */}
      <Alert
        message="条款对比功能说明"
        description={
          <div style={{ color: colors.textSecondary }}>
            <p>• 选择两个不同的合同进行智能对比分析</p>
            <p>• 系统将自动识别条款差异、相似性和潜在风险</p>
            <p>• 支持全面对比、关键条款对比和风险条款对比三种模式</p>
            <p>• 对比结果包含详细的差异分析和改进建议</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginTop: '24px' }}
      />
    </div>
  );
};

export default Compare;