import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  <PERSON>po<PERSON>, 
  Button, 
  Select, 
  Form, 
  Input, 
  message, 
  Spin, 
  Steps, 
  Space, 
  Divider, 
  Alert, 
  Row, 
  Col,
  Tabs,
  List,
  Tag,
  Progress,
  Modal,
  Checkbox,
  DatePicker,
  InputNumber
} from 'antd';
import { 
  RobotOutlined, 
  FileTextOutlined, 
  CheckCircleOutlined, 
  EditOutlined,
  DownloadOutlined,
  SaveOutlined,
  EyeOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { getContractTypeLabel } from '../constants/contractTypes';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { Step } = Steps;
interface DraftTemplate {
  id: string;
  name: string;
  category: string; // 修复：使用数据库实际字段名
  description: string;
  content: string;
  fields?: TemplateField[]; // 模板字段定义
  created_at?: string;
  updated_at?: string;
  user_id?: string;
  
  // 为了向后兼容，保留前端使用的字段名
  type?: string; // 映射到category
}

interface TemplateField {
  name: string;
  label: string;
  type: string;
  required: boolean;
  options?: string[];
}

interface Template {
  id: string;
  name: string;
  category: string;
  description: string;
  content: string;
  fields: Array<{
    name: string;
    label: string;
    type: 'text' | 'number' | 'date' | 'select' | 'textarea';
    required: boolean;
    options?: string[];
  }>;
}

interface DraftRequest {
  template_id: string;
  contract_type: string;
  parties: Array<{
    name: string;
    role: string;
    address?: string;
  }>;
  terms: Record<string, any>;
  special_requirements?: string;
}

interface DraftResult {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  content?: string;
  suggestions?: Array<{
    section: string;
    suggestion: string;
    reason: string;
  }>;
  created_at: string;
}

const Draft: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const [form] = Form.useForm();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [templates, setTemplates] = useState<DraftTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<DraftTemplate | null>(null);
  const [draftRequest, setDraftRequest] = useState<DraftRequest | null>(null);
  const [draftResult, setDraftResult] = useState<DraftResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [drafting, setDrafting] = useState(false);
  const [recentDrafts, setRecentDrafts] = useState<DraftResult[]>([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  
  useEffect(() => {
    fetchTemplates();
    fetchRecentDrafts();
  }, []);
  
  const fetchTemplates = async () => {
    try {
      setLoading(true);
      // 这里应该调用获取模板的API
      // 暂时使用模拟数据
      const mockTemplates: DraftTemplate[] = [
        {
          id: 'template-1',
          name: '销售合同模板',
          category: 'sales',
          type: 'sales',
          description: '适用于商品销售的标准合同模板',
          content: '销售合同模板内容',
          fields: [
            { name: 'product_name', label: '商品名称', type: 'text', required: true },
            { name: 'quantity', label: '数量', type: 'number', required: true },
            { name: 'unit_price', label: '单价', type: 'number', required: true },
            { name: 'delivery_date', label: '交付日期', type: 'date', required: true },
            { name: 'payment_terms', label: '付款方式', type: 'select', required: true, options: ['现金', '银行转账', '支票', '分期付款'] }
          ]
        },
        {
          id: 'template-2',
          name: '服务合同模板',
          category: 'service',
          type: 'service',
          description: '适用于服务提供的标准合同模板',
          content: '服务合同模板内容',
          fields: [
            { name: 'service_description', label: '服务描述', type: 'textarea', required: true },
            { name: 'service_period', label: '服务期限', type: 'text', required: true },
            { name: 'service_fee', label: '服务费用', type: 'number', required: true },
            { name: 'payment_schedule', label: '付款计划', type: 'select', required: true, options: ['一次性付款', '按月付款', '按季度付款', '按年付款'] }
          ]
        },
        {
          id: 'template-3',
          name: '租赁合同模板',
          category: 'lease',
          type: 'lease',
          description: '适用于房屋或设备租赁的标准合同模板',
          content: '租赁合同模板内容',
          fields: [
            { name: 'property_address', label: '租赁物地址', type: 'text', required: true },
            { name: 'lease_term', label: '租赁期限', type: 'text', required: true },
            { name: 'monthly_rent', label: '月租金', type: 'number', required: true },
            { name: 'deposit', label: '押金', type: 'number', required: true },
            { name: 'rent_payment_date', label: '租金支付日', type: 'number', required: true }
          ]
        }
      ];
      setTemplates(mockTemplates);
    } catch (error) {
      console.error('获取模板失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const fetchRecentDrafts = async () => {
    try {
      // 这里应该调用获取最近起草记录的API
      // 暂时使用模拟数据
      const mockDrafts: DraftResult[] = [
        {
          id: 'draft-1',
          status: 'completed',
          progress: 100,
          content: '这是一份AI生成的销售合同...',
          created_at: new Date(Date.now() - 86400000).toISOString()
        },
        {
          id: 'draft-2',
          status: 'processing',
          progress: 65,
          created_at: new Date(Date.now() - 3600000).toISOString()
        }
      ];
      setRecentDrafts(mockDrafts);
    } catch (error) {
      console.error('获取起草记录失败:', error);
    }
  };
  
  const handleTemplateSelect = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template || null);
    if (template) {
      setCurrentStep(1);
    }
  };
  
  const handleBasicInfoSubmit = (values: any) => {
    const request: DraftRequest = {
      template_id: selectedTemplate!.id,
      contract_type: selectedTemplate!.type,
      parties: [
        { name: values.party1_name, role: '甲方', address: values.party1_address },
        { name: values.party2_name, role: '乙方', address: values.party2_address }
      ],
      terms: {},
      special_requirements: values.special_requirements
    };
    setDraftRequest(request);
    setCurrentStep(2);
  };
  
  const handleTermsSubmit = (values: any) => {
    if (draftRequest) {
      const updatedRequest = {
        ...draftRequest,
        terms: values
      };
      setDraftRequest(updatedRequest);
      setCurrentStep(3);
    }
  };
  
  const handleStartDraft = async () => {
    if (!draftRequest) return;
    
    try {
      setDrafting(true);
      
      const response = await fetch('http://localhost:3001/api/contracts/draft', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(draftRequest)
      });
      
      const data = await response.json();
      
      if (data.success) {
        setDraftResult(data.result);
        message.success('AI起草已启动');
        fetchRecentDrafts();
        
        // 模拟进度更新
        const interval = setInterval(() => {
          setDraftResult(prev => {
            if (!prev || prev.progress >= 100) {
              clearInterval(interval);
              return prev;
            }
            const newProgress = Math.min(prev.progress + 10, 100);
            const newResult = { ...prev, progress: newProgress };
            if (newProgress === 100) {
              newResult.status = 'completed';
              newResult.content = '这是一份由AI智能生成的合同内容...\n\n第一条 合同双方\n甲方：' + draftRequest.parties[0].name + '\n乙方：' + draftRequest.parties[1].name + '\n\n第二条 合同标的\n...';
            }
            return newResult;
          });
        }, 1000);
      } else {
        message.error(data.message || '起草失败');
      }
    } catch (error) {
      console.error('起草失败:', error);
      message.error('起草失败');
    } finally {
      setDrafting(false);
    }
  };
  
  const handleSaveDraft = async () => {
    if (!draftResult?.content) return;
    
    try {
      const response = await fetch('http://localhost:3001/api/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          title: `AI起草合同-${new Date().toLocaleDateString()}`,
          content: draftResult.content,
          type: selectedTemplate?.type,
          status: 'draft'
        })
      });
      
      if (response.ok) {
        message.success('合同已保存');
      } else {
        message.error('保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };
  
  const renderTemplateSelection = () => (
    <Card 
      title="选择合同模板"
      style={{ backgroundColor: colors.surface, borderColor: colors.border }}
      headStyle={{ color: colors.text, borderBottomColor: colors.border }}
    >
      <Row gutter={[16, 16]}>
        {templates.map(template => (
          <Col span={8} key={template.id}>
            <Card
              hoverable
              onClick={() => handleTemplateSelect(template.id)}
              style={{ 
                backgroundColor: colors.background,
                borderColor: selectedTemplate?.id === template.id ? colors.primary : colors.border
              }}
            >
              <Card.Meta
                avatar={<FileTextOutlined style={{ fontSize: '24px', color: colors.primary }} />}
                title={<Text style={{ color: colors.text }}>{template.name}</Text>}
                description={
                  <div>
                    <Text type="secondary">{template.description}</Text>
                    <br />
                    <Tag color="blue" style={{ marginTop: '8px' }}>{template.type}</Tag>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>
    </Card>
  );
  
  const renderBasicInfo = () => (
    <Card 
      title="填写基本信息"
      style={{ backgroundColor: colors.surface, borderColor: colors.border }}
      headStyle={{ color: colors.text, borderBottomColor: colors.border }}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleBasicInfoSubmit}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Form.Item
              name="party1_name"
              label={<span style={{ color: colors.text }}>甲方名称</span>}
              rules={[{ required: true, message: '请输入甲方名称' }]}
            >
              <Input placeholder="请输入甲方名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="party2_name"
              label={<span style={{ color: colors.text }}>乙方名称</span>}
              rules={[{ required: true, message: '请输入乙方名称' }]}
            >
              <Input placeholder="请输入乙方名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="party1_address"
              label={<span style={{ color: colors.text }}>甲方地址</span>}
            >
              <Input placeholder="请输入甲方地址" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="party2_address"
              label={<span style={{ color: colors.text }}>乙方地址</span>}
            >
              <Input placeholder="请输入乙方地址" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              name="special_requirements"
              label={<span style={{ color: colors.text }}>特殊要求</span>}
            >
              <TextArea 
                rows={4} 
                placeholder="请输入特殊要求或补充条款" 
              />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item>
          <Space>
            <Button onClick={() => setCurrentStep(0)}>上一步</Button>
            <Button type="primary" htmlType="submit">下一步</Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
  
  const renderTermsConfig = () => (
    <Card 
      title="配置合同条款"
      style={{ backgroundColor: colors.surface, borderColor: colors.border }}
      headStyle={{ color: colors.text, borderBottomColor: colors.border }}
    >
      <Form
        layout="vertical"
        onFinish={handleTermsSubmit}
      >
        <Row gutter={[16, 16]}>
          {selectedTemplate?.fields.map(field => (
            <Col span={field.type === 'textarea' ? 24 : 12} key={field.name}>
              <Form.Item
                name={field.name}
                label={<span style={{ color: colors.text }}>{field.label}</span>}
                rules={field.required ? [{ required: true, message: `请输入${field.label}` }] : []}
              >
                {field.type === 'text' && <Input placeholder={`请输入${field.label}`} />}
                {field.type === 'number' && <InputNumber placeholder={`请输入${field.label}`} style={{ width: '100%' }} />}
                {field.type === 'date' && <DatePicker placeholder={`请选择${field.label}`} style={{ width: '100%' }} />}
                {field.type === 'textarea' && <TextArea rows={4} placeholder={`请输入${field.label}`} />}
                {field.type === 'select' && (
                  <Select placeholder={`请选择${field.label}`}>
                    {field.options?.map(option => (
                      <Option key={option} value={option}>{option}</Option>
                    ))}
                  </Select>
                )}
              </Form.Item>
            </Col>
          ))}
        </Row>
        <Form.Item>
          <Space>
            <Button onClick={() => setCurrentStep(1)}>上一步</Button>
            <Button type="primary" htmlType="submit">下一步</Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
  
  const renderPreview = () => (
    <Card 
      title="预览与确认"
      style={{ backgroundColor: colors.surface, borderColor: colors.border }}
      headStyle={{ color: colors.text, borderBottomColor: colors.border }}
    >
      <div style={{ marginBottom: '24px' }}>
        <Title level={4} style={{ color: colors.text }}>合同信息预览</Title>
        <Divider />
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Text strong style={{ color: colors.text }}>模板类型：</Text>
            <Text style={{ color: colors.textSecondary }}>{selectedTemplate?.name}</Text>
          </Col>
          <Col span={12}>
            <Text strong style={{ color: colors.text }}>合同双方：</Text>
            <Text style={{ color: colors.textSecondary }}>
              {draftRequest?.parties.map(p => p.name).join(' vs ')}
            </Text>
          </Col>
        </Row>
      </div>
      
      <Space>
        <Button onClick={() => setCurrentStep(2)}>上一步</Button>
        <Button 
          type="primary" 
          icon={<RobotOutlined />}
          onClick={handleStartDraft}
          loading={drafting}
        >
          开始AI起草
        </Button>
      </Space>
    </Card>
  );
  
  const renderDraftResult = () => (
    <Card 
      title="起草结果"
      style={{ backgroundColor: colors.surface, borderColor: colors.border }}
      headStyle={{ color: colors.text, borderBottomColor: colors.border }}
      extra={
        draftResult?.status === 'completed' && (
          <Space>
            <Button icon={<SaveOutlined />} onClick={handleSaveDraft}>保存合同</Button>
            <Button icon={<DownloadOutlined />}>导出文档</Button>
            <Button icon={<EyeOutlined />} onClick={() => setPreviewVisible(true)}>预览</Button>
          </Space>
        )
      }
    >
      {draftResult?.status === 'processing' && (
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Progress percent={isNaN(draftResult.progress) ? 0 : draftResult.progress} />
            <Text style={{ color: colors.textSecondary, display: 'block', marginTop: '8px' }}>
              AI正在智能起草合同，请稍候...
            </Text>
          </div>
        </div>
      )}
      
      {draftResult?.status === 'completed' && (
        <div>
          <Alert
            message="合同起草完成"
            description="AI已成功生成合同内容，您可以预览、编辑或保存合同。"
            type="success"
            showIcon
            style={{ marginBottom: '16px' }}
          />
          
          <div style={{ 
            backgroundColor: colors.background, 
            padding: '16px', 
            borderRadius: '6px',
            maxHeight: '400px',
            overflow: 'auto'
          }}>
            <pre style={{ 
              color: colors.text, 
              whiteSpace: 'pre-wrap',
              fontFamily: 'inherit',
              margin: 0
            }}>
              {draftResult.content}
            </pre>
          </div>
        </div>
      )}
    </Card>
  );

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ color: colors.text, marginBottom: '24px' }}>
        AI智能起草
      </Title>
      
      <Row gutter={[24, 24]}>
        <Col span={18}>
          {/* 步骤指示器 */}
          <Card style={{ backgroundColor: colors.surface, borderColor: colors.border, marginBottom: '24px' }}>
            <Steps current={currentStep}>
              <Step title="选择模板" icon={<FileTextOutlined />} />
              <Step title="基本信息" icon={<EditOutlined />} />
              <Step title="条款配置" icon={<CheckCircleOutlined />} />
              <Step title="预览确认" icon={<EyeOutlined />} />
            </Steps>
          </Card>
          
          {/* 步骤内容 */}
          {currentStep === 0 && renderTemplateSelection()}
          {currentStep === 1 && renderBasicInfo()}
          {currentStep === 2 && renderTermsConfig()}
          {currentStep === 3 && renderPreview()}
          
          {/* 起草结果 */}
          {draftResult && renderDraftResult()}
        </Col>
        
        <Col span={6}>
          {/* 最近起草记录 */}
          <Card 
            title="最近起草记录"
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            headStyle={{ color: colors.text, borderBottomColor: colors.border }}
          >
            <List
              dataSource={recentDrafts}
              renderItem={draft => (
                <List.Item
                  actions={[
                    <Button 
                      type="link" 
                      icon={<EyeOutlined />}
                      size="small"
                    >
                      查看
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Text style={{ color: colors.text }}>起草任务</Text>
                        <Tag color={draft.status === 'completed' ? 'green' : 'blue'}>
                          {draft.status === 'completed' ? '已完成' : '进行中'}
                        </Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary">
                          {new Date(draft.created_at).toLocaleString()}
                        </Text>
                        {draft.status === 'processing' && (
                          <div style={{ marginTop: '4px' }}>
                            <Progress percent={isNaN(draft.progress) ? 0 : draft.progress} size="small" />
                          </div>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无起草记录' }}
            />
          </Card>
          
          {/* 功能说明 */}
          <Alert
            message="AI起草功能"
            description={
              <div style={{ color: colors.textSecondary }}>
                <p>• 选择合适的合同模板</p>
                <p>• 填写基本信息和条款</p>
                <p>• AI智能生成合同内容</p>
                <p>• 支持预览、编辑和保存</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginTop: '16px' }}
          />
        </Col>
      </Row>
      
      {/* 预览模态框 */}
      <Modal
        title="合同预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>关闭</Button>,
          <Button key="save" type="primary" icon={<SaveOutlined />} onClick={handleSaveDraft}>
            保存合同
          </Button>
        ]}
        width={800}
      >
        <div style={{ 
          maxHeight: '500px', 
          overflow: 'auto',
          backgroundColor: colors.background,
          padding: '16px',
          borderRadius: '6px'
        }}>
          <pre style={{ 
            color: colors.text, 
            whiteSpace: 'pre-wrap',
            fontFamily: 'inherit',
            margin: 0
          }}>
            {draftResult?.content}
          </pre>
        </div>
      </Modal>
    </div>
  );
};

export default Draft;