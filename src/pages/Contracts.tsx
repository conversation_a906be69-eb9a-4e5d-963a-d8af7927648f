import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, 
  Button, 
  Card, 
  Space, 
  Tag, 
  Input, 
  Select, 
  DatePicker, 
  Modal, 
  Form, 
  Upload, 
  message,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  FilterOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import FilePreview from '../components/FilePreview';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { Contract, CreateContractRequest, ContractQueryParams } from '../types/contract';
import { getContractTypeOptions, getContractTypeLabel } from '../constants/contractTypes';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

// Contract接口现在从types/contract.ts导入

const Contracts: React.FC = () => {
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [typeFilter, setTypeFilter] = useState<string>('');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createFileList, setCreateFileList] = useState<any[]>([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<{
    filePath?: string;
    fileName?: string;
    fileUrl?: string;
    mimeType?: string;
  }>({});
  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [form] = Form.useForm();
  
  const navigate = useNavigate();
  const { user, token } = useAuth();
  const { colors } = useTheme();

  // 状态标签配置
  const statusConfig = {
    draft: { color: 'default', text: '草稿' },
    reviewing: { color: 'processing', text: '审查中' },
    approved: { color: 'success', text: '已批准' },
    rejected: { color: 'error', text: '已拒绝' },
    signed: { color: 'blue', text: '已签署' },
    expired: { color: 'warning', text: '已过期' },
    uploaded: { color: 'cyan', text: '已上传' },
    processing: { color: 'processing', text: '处理中' },
    reviewed: { color: 'green', text: '已审查' },
    archived: { color: 'default', text: '已归档' }
  };

  // 风险等级配置
  const riskConfig = {
    low: { color: 'green', text: '低风险' },
    medium: { color: 'orange', text: '中风险' },
    high: { color: 'red', text: '高风险' },
    critical: { color: 'magenta', text: '严重风险' }
  };

  // 表格列配置
  const columns: ColumnsType<Contract> = [
    {
      title: '合同标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
      render: (text: string, record: Contract) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/contracts/${record.id}`)}
          style={{ padding: 0, height: 'auto', color: colors.primary }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '合同类型',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category: string) => getContractTypeLabel(category) || '-',
    },
    {
      title: '对方当事人',
      dataIndex: 'counterparty',
      key: 'counterparty',
      width: 150,
      ellipsis: true,
    },
    {
      title: '合同金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number) => amount ? `¥${amount.toLocaleString()}` : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: keyof typeof statusConfig) => {
        const config = statusConfig[status] || { color: 'default', text: '未知状态' };
        return (
          <Tag color={config.color}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '风险等级',
      dataIndex: 'risk_level',
      key: 'risk_level',
      width: 100,
      render: (level: keyof typeof riskConfig) => {
        const config = riskConfig[level as keyof typeof riskConfig] || { color: 'default', text: '未知风险' };
        return (
          <Tag color={config.color}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => {
        return date ? dayjs(date).format('YYYY-MM-DD') : '-';
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record: Contract) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => navigate(`/contracts/${record.id}`)}
            />
          </Tooltip>
          {record.file_path && (
            <Tooltip title="预览文件">
              <Button
                type="text"
                icon={<FileTextOutlined />}
                onClick={() => handleFilePreview(record)}
              />
            </Tooltip>
          )}
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个合同吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="text" 
                icon={<DeleteOutlined />} 
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 获取合同列表
  const fetchContracts = useCallback(async (
    page: number = 1,
    limit: number = 10,
    searchText: string = ''
  ) => {
    console.log('🔍 [前端调试] fetchContracts 被调用，参数:', { page, limit, searchText });
    setLoading(true);
    try {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());
      if (searchText) params.append('search', searchText);
      if (statusFilter) params.append('status', statusFilter);
      if (typeFilter) params.append('type', typeFilter); // 修复：改为type参数
      if (dateRange) {
        params.append('start_date', dateRange[0].format('YYYY-MM-DD'));
        params.append('end_date', dateRange[1].format('YYYY-MM-DD'));
      }

      const response = await fetch(`http://localhost:3001/api/contracts?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 现在前后端使用统一的字段名，直接使用
          setContracts(data.contracts || []);
          setTotal(data.total || 0);
          // 只在页码确实变化时才更新currentPage，避免循环
          if (data.page && data.page !== currentPage) {
            setCurrentPage(data.page);
          }
        } else {
          message.error(data.message || '获取合同列表失败');
        }
      } else {
        const errorData = await response.json();
        message.error(errorData.message || '获取合同列表失败');
      }
    } catch (error) {
      console.error('Fetch contracts error:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [pageSize, statusFilter, typeFilter, dateRange, token]);

  // 创建合同
  const handleCreate = async (values: any) => {
    try {
      console.log('🚀 [前端调试] 开始创建合同，表单数据:', values);
      console.log('📋 [前端调试] 表单数据详细分析:');
      
      // 详细记录每个字段的值和类型
      console.log('🔍 [前端调试] 关键字段值检查:');
      console.log('  startDate:', values.startDate, '(类型:', typeof values.startDate, ', 是否为dayjs对象:', values.startDate && typeof values.startDate.format === 'function', ')');
      console.log('  endDate:', values.endDate, '(类型:', typeof values.endDate, ', 是否为dayjs对象:', values.endDate && typeof values.endDate.format === 'function', ')');
      console.log('  riskLevel:', values.riskLevel, '(类型:', typeof values.riskLevel, ')');
      console.log('  type:', values.type, '(类型:', typeof values.type, ')');
      console.log('  title:', values.title, '(类型:', typeof values.title, ')');
      console.log('  counterparty:', values.counterparty, '(类型:', typeof values.counterparty, ')');
      console.log('  amount:', values.amount, '(类型:', typeof values.amount, ')');
      console.log('  content:', values.content, '(类型:', typeof values.content, ')');
      
      Object.keys(values).forEach(key => {
        if (key === 'file') {
          console.log(`  ${key}:`, values[key], '(类型:', typeof values[key], ', 长度:', values[key]?.length || 'undefined', ')');
          if (values[key] && Array.isArray(values[key])) {
            values[key].forEach((fileItem, index) => {
              console.log(`    文件[${index}]:`, {
                uid: fileItem.uid,
                name: fileItem.name,
                status: fileItem.status,
                hasOriginFileObj: !!fileItem.originFileObj,
                originFileObjType: typeof fileItem.originFileObj
              });
            });
          }
        } else {
          console.log(`  ${key}:`, values[key], '(类型:', typeof values[key], ')');
        }
      });
      
      const formData = new FormData();
      let hasFile = false;
      let fileInfo = null;
      
      // 处理文件上传 - 使用与编辑模块相同的逻辑
      const fileObj = createFileList.find((f: any) => f.originFileObj);
      if (fileObj?.originFileObj) {
        const file = fileObj.originFileObj;
        console.log('📁 [前端调试] 检测到文件上传:', {
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified
        });
        
        formData.append('file', file);
        hasFile = true;
        fileInfo = {
          name: file.name,
          size: file.size,
          type: file.type
        };
        console.log('✅ [前端调试] 文件已添加到FormData:', fileInfo);
      } else {
        console.log('📄 [前端调试] 未检测到有效的文件上传');
      }
      
      // 添加基本字段到FormData（参考编辑模块的逻辑）
      formData.append('title', values.title);
      console.log('📝 [前端调试] 字段 title:', values.title);
      
      formData.append('category', values.category);
      console.log('📝 [前端调试] 字段 category:', values.category);
      
      formData.append('counterparty', values.counterparty || '');
      console.log('📝 [前端调试] 字段 counterparty:', values.counterparty || '');
      
      formData.append('risk_level', values.risk_level || 'medium');
      console.log('📝 [前端调试] 字段 risk_level:', values.risk_level || 'medium');
      
      if (values.amount) {
        formData.append('amount', values.amount.toString());
        console.log('📝 [前端调试] 字段 amount:', values.amount.toString());
      }
      
      if (values.start_date) {
        const formattedStartDate = dayjs(values.start_date).format('YYYY-MM-DD');
        formData.append('start_date', formattedStartDate);
        console.log('📅 [前端调试] 字段 start_date:', formattedStartDate);
      }
      
      if (values.end_date) {
        const formattedEndDate = dayjs(values.end_date).format('YYYY-MM-DD');
        formData.append('end_date', formattedEndDate);
        console.log('📅 [前端调试] 字段 end_date:', formattedEndDate);
      }
      
      if (values.description) {
        formData.append('description', values.description);
        console.log('📝 [前端调试] 字段 description:', values.description);
      }
      
      // 打印FormData内容（用于调试）
      console.log('📦 [前端调试] FormData 内容:');
      for (let [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`  ${key}: [File] ${value.name} (${value.size} bytes, ${value.type})`);
        } else {
          console.log(`  ${key}: ${value}`);
        }
      }
      
      if (hasFile) {
        console.log('🔍 [前端调试] 准备发送文件进行文档解析...');
        message.loading('正在上传文件并解析内容...', 0);
      } else {
        console.log('📄 [前端调试] 无文件上传，仅创建合同记录');
      }

      const response = await fetch('http://localhost:3001/api/contracts', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });
      
      message.destroy(); // 清除loading消息
      
      console.log('🌐 [前端调试] 服务器响应状态:', response.status, response.statusText);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ [前端调试] 服务器响应数据:', data);
        
        // 显示创建成功消息
        message.success('合同创建成功');
        
        // 如果有文档解析结果，显示解析状态
        if (data.parsing) {
          console.log('🔍 [前端调试] 文档解析结果:', data.parsing);
          
          if (data.parsing.success && data.parsing.contentExtracted) {
            const wordCount = data.parsing.wordCount || '未知';
            console.log('✅ [前端调试] 文档解析成功，提取词数:', wordCount);
            message.info(`文档解析成功，已提取 ${wordCount} 个词的内容`);
            
            // 如果解析出内容，自动填充到表单
            if (data.parsing.content) {
              console.log('📝 [前端调试] 自动填充解析内容到合同内容字段');
              form.setFieldsValue({ content: data.parsing.content });
              message.success('已自动填充解析的文档内容');
            }
          } else if (data.parsing.error) {
            console.error('❌ [前端调试] 文档解析失败:', data.parsing.error);
            message.warning(`文档解析失败: ${data.parsing.error}`);
          } else {
            console.log('⚠️ [前端调试] 文档解析状态未知:', data.parsing);
            message.info('文档已上传，但解析状态未知');
          }
        } else if (hasFile) {
          console.log('⚠️ [前端调试] 有文件上传但服务器未返回解析结果');
          message.warning('文件已上传，但未收到解析结果');
        }
        
        setCreateModalVisible(false);
        form.resetFields();
        setCreateFileList([]); // 清空文件列表
        fetchContracts(1, pageSize, searchText); // 创建成功后保持当前搜索状态
      } else {
        const data = await response.json();
        console.error('❌ [前端调试] 服务器错误响应:', data);
        message.error(data.message || '创建失败');
      }
    } catch (error) {
      console.error('💥 [前端调试] 创建合同异常:', error);
      message.destroy(); // 清除可能的loading消息
      message.error('网络错误，请稍后重试');
    }
  };

  // 编辑合同
  const handleEdit = (contract: Contract) => {
    navigate(`/contracts/${contract.id}?mode=edit`);
  };

  // 文件预览
  const handleFilePreview = (contract: Contract) => {
    if (!contract.file_path) {
      message.warning('该合同没有关联文件');
      return;
    }

    // 根据文件扩展名推断MIME类型
    const getFileType = (filePath: string) => {
      const extension = filePath.toLowerCase().split('.').pop();
      switch (extension) {
        case 'pdf':
          return 'application/pdf';
        case 'doc':
          return 'application/msword';
        case 'docx':
          return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        case 'txt':
          return 'text/plain';
        default:
          return 'application/octet-stream';
      }
    };

    setPreviewFile({
      filePath: contract.file_path,
      fileName: contract.title + '.' + (contract.file_path.split('.').pop() || 'pdf'),
      fileUrl: contract.file_url,
      mimeType: getFileType(contract.file_path)
    });
    setPreviewVisible(true);
  };

  // 删除合同
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`http://localhost:3001/api/contracts/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        message.success('删除成功');
        fetchContracts(currentPage, pageSize, searchText); // 删除后保持当前搜索状态
      } else {
        message.error('删除失败');
      }
    } catch (error) {
      console.error('Delete contract error:', error);
      message.error('网络错误，请稍后重试');
    }
  };

  // 重置筛选
  const handleReset = () => {
    setSearchText('');
    setStatusFilter('');
    setTypeFilter('');
    setDateRange(null);
    setCurrentPage(1);
    // 重置后立即刷新列表
    fetchContracts(1, pageSize, ''); // 重置时清空搜索关键词
  };

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    setCurrentPage(page);
    if (size && size !== pageSize) {
      setPageSize(size);
    }
    fetchContracts(page, newPageSize, searchText); // 分页时保持当前搜索状态
  };

  useEffect(() => {
    // 筛选条件变化时重置到第一页并重新获取数据
    setCurrentPage(1);
    fetchContracts(1, pageSize, searchText);
  }, [statusFilter, typeFilter, dateRange, pageSize, fetchContracts]);

  useEffect(() => {
    // 初始加载
    fetchContracts(1, pageSize, '');
  }, []);

  return (
    <div>
      {/* 页面标题和操作按钮 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold" style={{ color: colors.text }}>合同管理</h1>
          <p className="text-gray-500 mt-1">管理和查看所有合同文档</p>
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />}
          onClick={() => setCreateModalVisible(true)}
          style={{ backgroundColor: colors.primary, borderColor: colors.primary }}
        >
          新建合同
        </Button>
      </div>

      {/* 筛选区域 */}
      <Card className="mb-4" style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <FilterOutlined style={{ color: colors.primary }} />
            <span className="font-medium" style={{ color: colors.text }}>筛选条件</span>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1" style={{ color: colors.text }}>
                合同标题
                <span className="text-xs text-gray-500 ml-2"></span>
              </label>
              <Search
                placeholder=""
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                onSearch={(value) => {
                  // 只在点击搜索按钮或按Enter键时触发搜索
                  console.log('🔍 [前端调试] 执行搜索，关键词:', value);
                  setCurrentPage(1);
                  fetchContracts(1, pageSize, value); // 传递搜索关键词
                }}
                onClear={() => {
                  // 清空搜索时立即刷新列表
                  setSearchText('');
                  setCurrentPage(1);
                  fetchContracts(1, pageSize, ''); // 清空搜索关键词
                }}
                enterButton="搜索"
                allowClear
                style={{ width: '100%' }}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1" style={{ color: colors.text }}>合同状态</label>
              <Select
                placeholder="请选择合同状态"
                value={statusFilter}
                onChange={setStatusFilter}
                allowClear
                style={{ width: '100%' }}
              >
                <Option value="draft">草稿</Option>
                <Option value="reviewing">审查中</Option>
                <Option value="approved">已批准</Option>
                <Option value="rejected">已拒绝</Option>
                <Option value="signed">已签署</Option>
                <Option value="expired">已过期</Option>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1" style={{ color: colors.text }}>合同类型</label>
              <Select
                placeholder="请选择合同类型"
                value={typeFilter}
                onChange={setTypeFilter}
                allowClear
                style={{ width: '100%' }}
              >
                {getContractTypeOptions().map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1" style={{ color: colors.text }}>创建日期范围</label>
              <div className="flex space-x-2">
                <RangePicker
                  value={dateRange}
                  onChange={setDateRange}
                  style={{ flex: 1 }}
                  placeholder={['开始日期', '结束日期']}
                />
                <Tooltip title="重置所有筛选条件">
                  <Button icon={<FilterOutlined />} onClick={handleReset}>
                    重置
                  </Button>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* 合同列表 */}
      <Card style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
        <Table
          columns={columns}
          dataSource={contracts}
          loading={loading}
          rowKey="id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
            onChange: handlePageChange,
            onShowSizeChange: handlePageChange,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 创建合同模态框 */}
      <Modal
        title="新建合同"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreate}
          initialValues={{
            risk_level: 'medium',
            status: 'draft'
          }}
        >
          <Form.Item
            name="title"
            label="合同标题"
            rules={[{ required: true, message: '请输入合同标题' }]}
          >
            <Input placeholder="请输入合同标题" />
          </Form.Item>

          <Form.Item
            name="category"
            label="合同类型"
            rules={[{ required: true, message: '请选择合同类型' }]}
          >
            <Select placeholder="请选择合同类型">
              {getContractTypeOptions().map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="counterparty"
            label="对方当事人"
            rules={[{ required: true, message: '请输入对方当事人' }]}
          >
            <Input placeholder="请输入对方当事人" />
          </Form.Item>

          <Form.Item
            name="amount"
            label="合同金额"
          >
            <Input type="number" placeholder="请输入合同金额" addonBefore="¥" />
          </Form.Item>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              name="start_date"
              label="合同开始日期"
            >
              <DatePicker
                placeholder="请选择开始日期"
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Form.Item>

            <Form.Item
              name="end_date"
              label="合同结束日期"
            >
              <DatePicker
                placeholder="请选择结束日期"
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Form.Item>
          </div>

          <Form.Item
            name="risk_level"
            label="风险等级"
            initialValue="medium"
          >
            <Select placeholder="请选择风险等级">
              <Option value="low">低风险</Option>
              <Option value="medium">中风险</Option>
              <Option value="high">高风险</Option>
              <Option value="critical">严重风险</Option>
            </Select>
          </Form.Item>



          <Form.Item
            label="合同文件"
          >
            <Upload
              fileList={createFileList}
              beforeUpload={(file) => {
                const isValidType = [
                  'application/pdf',
                  'application/msword',
                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                  'text/plain'
                ].includes(file.type);
                
                if (!isValidType) {
                  message.error('只能上传 PDF、Word 或文本文件！');
                  return false;
                }
                
                const isLt10M = file.size / 1024 / 1024 < 10;
                if (!isLt10M) {
                  message.error('文件大小不能超过 10MB！');
                  return false;
                }
                
                return false; // 阻止自动上传
              }}
              onChange={(info) => {
                setCreateFileList(info.fileList.slice(-1)); // 只保留最新的一个文件
              }}
              onRemove={() => {
                setCreateFileList([]);
              }}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
            <p className="text-gray-500 text-sm mt-1">
              支持 PDF、Word、文本文件，文件大小不超过 10MB
            </p>
          </Form.Item>

          <Form.Item className="mb-0">
            <div className="flex justify-end space-x-2">
              <Button onClick={() => {
                setCreateModalVisible(false);
                form.resetFields();
                setCreateFileList([]);
              }}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                style={{ backgroundColor: colors.primary, borderColor: colors.primary }}
              >
                创建
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 文件预览组件 */}
      <FilePreview
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        filePath={previewFile.filePath}
        fileName={previewFile.fileName}
        fileUrl={previewFile.fileUrl}
        mimeType={previewFile.mimeType}
      />
    </div>
  );
};

export default Contracts;