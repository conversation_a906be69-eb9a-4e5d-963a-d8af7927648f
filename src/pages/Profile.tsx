import React from 'react';
import { Card, Typography } from 'antd';
import { useTheme } from '../contexts/ThemeContext';

const { Title } = Typography;

const Profile: React.FC = () => {
  const { colors } = useTheme();

  return (
    <div>
      <Title level={2} style={{ color: colors.text }}>个人资料</Title>
      <Card style={{ backgroundColor: colors.surface, borderColor: colors.border }}>
        <p style={{ color: colors.textSecondary }}>个人资料页面正在开发中...</p>
      </Card>
    </div>
  );
};

export default Profile;