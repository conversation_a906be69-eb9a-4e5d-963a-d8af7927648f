import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Button, 
  Select, 
  Form, 
  Upload, 
  message, 
  Spin, 
  Progress, 
  Tag, 
  Space, 
  Divider,
  Alert,
  List,
  Checkbox
} from 'antd';
import { 
  UploadOutlined, 
  FileTextOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { getContractTypeLabel } from '../constants/contractTypes';

const { Title, Text } = Typography;
const { Option } = Select;

interface Contract {
  id: string;
  title: string;
  category: string; // 修复：使用数据库实际字段名
  status: 'draft' | 'reviewing' | 'approved' | 'rejected' | 'signed' | 'expired' | 'uploaded' | 'processing' | 'reviewed' | 'archived'; // 修复：使用具体的状态类型
  created_at: string;
  user_id: string;
  file_path?: string;
  file_url?: string;
  content?: string;
  ocr_content?: string;
  counterparty?: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  updated_at?: string;
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  
  // 为了向后兼容，保留前端使用的字段名
  type?: string; // 映射到category
}

interface ReviewTask {
  id: string;
  contract_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  risk_level?: string;
  risk_score?: number;
  created_at: string;
}

const Review: React.FC = () => {
  const { colors } = useTheme();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [recentTasks, setRecentTasks] = useState<ReviewTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [selectedContract, setSelectedContract] = useState<string | null>(null);
  
  // 获取合同列表
  useEffect(() => {
    fetchContracts();
    fetchRecentTasks();
  }, []);
  
  const fetchContracts = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/contracts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setContracts(data.contracts || []);
      }
    } catch (error) {
      console.error('获取合同列表失败:', error);
    }
  };
  
  const fetchRecentTasks = async () => {
    try {
      setLoading(true);
      // 这里应该调用获取最近审查任务的API
      // 暂时使用模拟数据
      const mockTasks: ReviewTask[] = [
        {
          id: 'task-1',
          contract_id: 'contract-1',
          status: 'completed',
          progress: 100,
          risk_level: 'medium',
          risk_score: 65,
          created_at: new Date(Date.now() - 86400000).toISOString()
        },
        {
          id: 'task-2', 
          contract_id: 'contract-2',
          status: 'processing',
          progress: 45,
          created_at: new Date(Date.now() - 3600000).toISOString()
        }
      ];
      setRecentTasks(mockTasks);
    } catch (error) {
      console.error('获取审查任务失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleStartReview = async (values: any) => {
    try {
      setSubmitting(true);
      
      const reviewRequest = {
        contract_id: values.contract_id,
        review_type: values.review_type || 'full',
        focus_areas: values.focus_areas || [],
        custom_rules: values.custom_rules || []
      };
      
      const response = await fetch('http://localhost:3001/api/review/start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(reviewRequest)
      });
      
      const data = await response.json();
      
      if (data.success) {
        message.success('智能审查已启动');
        form.resetFields();
        fetchRecentTasks();
        
        // 跳转到审查结果页面
        navigate(`/review-result/${data.task.id}`);
      } else {
        message.error(data.message || '启动审查失败');
      }
    } catch (error) {
      console.error('启动审查失败:', error);
      message.error('启动审查失败');
    } finally {
      setSubmitting(false);
    }
  };
  
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'green';
      case 'medium': return 'orange';
      case 'high': return 'red';
      case 'critical': return 'red';
      default: return 'default';
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'processing': return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
      case 'failed': return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default: return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ color: colors.text, marginBottom: '24px' }}>
        智能审查
      </Title>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '24px' }}>
        {/* 启动新审查 */}
        <Card 
          title="启动新审查"
          style={{ backgroundColor: colors.surface, borderColor: colors.border }}
          headStyle={{ color: colors.text, borderBottomColor: colors.border }}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleStartReview}
          >
            <Form.Item
              name="contract_id"
              label={<span style={{ color: colors.text }}>选择合同</span>}
              rules={[{ required: true, message: '请选择要审查的合同' }]}
            >
              <Select
                placeholder="请选择合同"
                onChange={setSelectedContract}
                style={{ width: '100%' }}
              >
                {contracts.map(contract => (
                  <Option key={contract.id} value={contract.id}>
                    <Space>
                      <FileTextOutlined />
                      {contract.title}
                      <Tag color="blue">{getContractTypeLabel(contract.category || contract.type)}</Tag>
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item
              name="review_type"
              label={<span style={{ color: colors.text }}>审查类型</span>}
              initialValue="full"
            >
              <Select>
                <Option value="full">全面审查</Option>
                <Option value="quick">快速审查</Option>
                <Option value="custom">自定义审查</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="focus_areas"
              label={<span style={{ color: colors.text }}>重点关注领域</span>}
            >
              <Checkbox.Group>
                <Space direction="vertical">
                  <Checkbox value="liability">违约责任</Checkbox>
                  <Checkbox value="payment">付款条款</Checkbox>
                  <Checkbox value="termination">终止条款</Checkbox>
                  <Checkbox value="intellectual_property">知识产权</Checkbox>
                  <Checkbox value="confidentiality">保密条款</Checkbox>
                </Space>
              </Checkbox.Group>
            </Form.Item>
            
            <Form.Item>
              <Button 
                type="primary" 
                htmlType="submit" 
                loading={submitting}
                disabled={!selectedContract}
                block
              >
                开始智能审查
              </Button>
            </Form.Item>
          </Form>
        </Card>
        
        {/* 最近审查任务 */}
        <Card 
          title="最近审查任务"
          style={{ backgroundColor: colors.surface, borderColor: colors.border }}
          headStyle={{ color: colors.text, borderBottomColor: colors.border }}
          extra={
            <Button 
              type="link" 
              onClick={fetchRecentTasks}
              loading={loading}
            >
              刷新
            </Button>
          }
        >
          <Spin spinning={loading}>
            <List
              dataSource={recentTasks}
              renderItem={task => {
                const contract = contracts.find(c => c.id === task.contract_id);
                return (
                  <List.Item
                    actions={[
                      <Button 
                        type="link" 
                        icon={<EyeOutlined />}
                        onClick={() => navigate(`/review-result/${task.id}`)}
                      >
                        查看
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={getStatusIcon(task.status)}
                      title={
                        <Space>
                          <Text style={{ color: colors.text }}>
                            {contract?.title || '未知合同'}
                          </Text>
                          {task.risk_level && (
                            <Tag color={getRiskLevelColor(task.risk_level)}>
                              {task.risk_level === 'low' && '低风险'}
                              {task.risk_level === 'medium' && '中风险'}
                              {task.risk_level === 'high' && '高风险'}
                              {task.risk_level === 'critical' && '严重风险'}
                            </Tag>
                          )}
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary">
                            {new Date(task.created_at).toLocaleString()}
                          </Text>
                          {task.status === 'processing' && (
                            <div style={{ marginTop: '8px' }}>
                              <Progress percent={isNaN(task.progress) ? 0 : task.progress} size="small" />
                            </div>
                          )}
                          {task.risk_score && (
                            <div style={{ marginTop: '4px' }}>
                              <Text type="secondary">风险评分: {task.risk_score}</Text>
                            </div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                );
              }}
              locale={{ emptyText: '暂无审查任务' }}
            />
          </Spin>
        </Card>
      </div>
      
      {/* 审查说明 */}
      <Card 
        title="审查说明"
        style={{ 
          backgroundColor: colors.surface, 
          borderColor: colors.border,
          marginTop: '24px'
        }}
        headStyle={{ color: colors.text, borderBottomColor: colors.border }}
      >
        <Alert
          message="智能审查功能说明"
          description={
            <div style={{ color: colors.textSecondary }}>
              <p>• <strong>全面审查</strong>：对合同进行全方位分析，包括条款完整性、风险识别、合规性检查等</p>
              <p>• <strong>快速审查</strong>：重点检查关键条款和常见风险点，适用于时间紧急的情况</p>
              <p>• <strong>自定义审查</strong>：根据您选择的重点关注领域进行针对性审查</p>
              <Divider style={{ margin: '12px 0' }} />
              <p>审查完成后，系统将生成详细的审查报告，包括风险评估、改进建议和合规性分析。</p>
            </div>
          }
          type="info"
          showIcon
        />
      </Card>
    </div>
  );
};

export default Review;