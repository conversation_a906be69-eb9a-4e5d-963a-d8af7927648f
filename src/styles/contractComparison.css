/**
 * 合同对比模块样式文件
 * 
 * 实现差异高亮样式、响应式布局样式、同步滚动样式
 * 基于参考图片的设计风格
 */

/* ==================== 全局变量 ==================== */
:root {
  /* 差异颜色方案 */
  --color-diff-added: #d4edda;
  --color-diff-added-border: #28a745;
  --color-diff-added-text: #155724;
  
  --color-diff-deleted: #f8d7da;
  --color-diff-deleted-border: #dc3545;
  --color-diff-deleted-text: #721c24;
  
  --color-diff-modified: #fff3cd;
  --color-diff-modified-border: #ffc107;
  --color-diff-modified-text: #856404;
  
  --color-diff-moved: #cce5ff;
  --color-diff-moved-border: #007bff;
  --color-diff-moved-text: #004085;
  
  /* 界面颜色 */
  --color-border: #e9ecef;
  --color-text-primary: #333333;
  --color-text-secondary: #6c757d;
  --color-background: #ffffff;
  --color-background-light: #f8f9fa;
  
  /* 阴影 */
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* ==================== 合同对比容器 ==================== */
.contract-comparison-container {
  min-height: 100vh;
  background-color: var(--color-background-light);
}

.contract-comparison-header {
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
  padding: 16px 24px;
  box-shadow: var(--shadow-light);
}

.contract-comparison-main {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* ==================== 文档上传组件 ==================== */
.document-uploader {
  background: var(--color-background);
  border-radius: 8px;
  padding: 32px;
  box-shadow: var(--shadow-light);
}

.upload-section {
  position: relative;
}

.upload-section .ant-upload-drag {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.upload-section .ant-upload-drag:hover {
  border-color: #40a9ff;
  background-color: #f0f8ff;
}

.upload-section .ant-upload-drag.ant-upload-drag-hover {
  border-color: #1890ff;
  background-color: #e6f7ff;
}

/* 文件信息卡片 */
.file-info-card {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.file-info-card.valid {
  border-color: var(--color-diff-added-border);
  background-color: var(--color-diff-added);
}

.file-info-card.invalid {
  border-color: var(--color-diff-deleted-border);
  background-color: var(--color-diff-deleted);
}

/* ==================== 对比结果展示组件 ==================== */
.comparison-viewer {
  background: var(--color-background);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.comparison-content {
  height: 100%;
}

.comparison-content .ant-card {
  height: 100%;
  border-radius: 0;
}

.comparison-content .ant-card-body {
  padding: 0;
  height: calc(100% - 57px); /* 减去标题高度 */
}

/* 文档行样式 */
.document-line {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.2s ease;
  position: relative;
}

.document-line:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.document-line.selected {
  background-color: rgba(24, 144, 255, 0.1);
  border-left: 3px solid #1890ff;
}

/* 行号样式 */
.line-number {
  color: var(--color-text-secondary);
  font-size: 12px;
  user-select: none;
  text-align: right;
  padding-right: 8px;
  border-right: 1px solid var(--color-border);
  background-color: var(--color-background-light);
}

/* ==================== 差异高亮样式 ==================== */

/* 新增内容 */
.diff-added,
.bg-diff-added {
  background-color: var(--color-diff-added) !important;
  border-left: 4px solid var(--color-diff-added-border);
  color: var(--color-diff-added-text);
}

.diff-highlight.diff-added {
  background-color: var(--color-diff-added-border);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}

/* 删除内容 */
.diff-deleted,
.bg-diff-deleted {
  background-color: var(--color-diff-deleted) !important;
  border-left: 4px solid var(--color-diff-deleted-border);
  color: var(--color-diff-deleted-text);
}

.diff-highlight.diff-deleted {
  background-color: var(--color-diff-deleted-border);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
  text-decoration: line-through;
}

/* 修改内容 */
.diff-modified,
.bg-diff-modified {
  background-color: var(--color-diff-modified) !important;
  border-left: 4px solid var(--color-diff-modified-border);
  color: var(--color-diff-modified-text);
}

.diff-highlight.diff-modified {
  background-color: var(--color-diff-modified-border);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}

/* 移动内容 */
.diff-moved,
.bg-diff-moved {
  background-color: var(--color-diff-moved) !important;
  border-left: 4px solid var(--color-diff-moved-border);
  color: var(--color-diff-moved-text);
}

.diff-highlight.diff-moved {
  background-color: var(--color-diff-moved-border);
  color: white;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 500;
}

/* ==================== 差异导航组件 ==================== */
.difference-navigator {
  background: var(--color-background);
  border-radius: 8px;
  box-shadow: var(--shadow-light);
  height: fit-content;
  max-height: 80vh;
  overflow-y: auto;
}

.difference-item {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.difference-item:hover {
  background-color: var(--color-background-light);
}

.difference-item.active {
  background-color: rgba(24, 144, 255, 0.1);
  border-left: 3px solid #1890ff;
}

.difference-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #1890ff;
}

/* 差异类型标记 */
.difference-type-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.difference-type-badge.added {
  background-color: var(--color-diff-added);
  color: var(--color-diff-added-text);
}

.difference-type-badge.deleted {
  background-color: var(--color-diff-deleted);
  color: var(--color-diff-deleted-text);
}

.difference-type-badge.modified {
  background-color: var(--color-diff-modified);
  color: var(--color-diff-modified-text);
}

.difference-type-badge.moved {
  background-color: var(--color-diff-moved);
  color: var(--color-diff-moved-text);
}

/* ==================== 进度指示器 ==================== */
.processing-indicator {
  text-align: center;
  padding: 48px 24px;
  background: var(--color-background);
  border-radius: 8px;
  box-shadow: var(--shadow-light);
}

.processing-steps {
  margin-top: 24px;
}

.processing-step {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-border);
}

.processing-step:last-child {
  border-bottom: none;
}

.processing-step.active {
  color: #1890ff;
  font-weight: 500;
}

.processing-step.completed {
  color: var(--color-diff-added-text);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1200px) {
  .contract-comparison-main {
    padding: 16px;
  }
  
  .document-uploader {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .contract-comparison-main {
    padding: 12px;
  }
  
  .document-uploader {
    padding: 16px;
  }
  
  .comparison-viewer {
    margin: 0 -12px;
    border-radius: 0;
  }
  
  .comparison-content .ant-col {
    margin-bottom: 16px;
  }
  
  .comparison-content .ant-col:last-child {
    margin-bottom: 0;
  }
  
  /* 移动端单列布局 */
  .comparison-content .ant-row {
    flex-direction: column;
  }
  
  .comparison-content .ant-col {
    width: 100% !important;
    max-width: 100% !important;
  }
}

@media (max-width: 480px) {
  .document-line {
    font-size: 12px;
    padding: 8px;
  }
  
  .line-number {
    font-size: 10px;
    width: 32px;
  }
  
  .difference-navigator {
    max-height: 60vh;
  }
  
  .difference-item {
    padding: 8px 12px;
  }
}

/* ==================== 打印样式 ==================== */
@media print {
  .contract-comparison-container {
    background: white;
  }
  
  .comparison-viewer {
    box-shadow: none;
    border: 1px solid var(--color-border);
  }
  
  .document-line {
    break-inside: avoid;
  }
  
  .difference-navigator {
    display: none;
  }
  
  /* 确保差异高亮在打印时可见 */
  .diff-added,
  .bg-diff-added {
    background-color: #d4edda !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  .diff-deleted,
  .bg-diff-deleted {
    background-color: #f8d7da !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  .diff-modified,
  .bg-diff-modified {
    background-color: #fff3cd !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

/* ==================== 滚动条样式 ==================== */
.comparison-viewer ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.comparison-viewer ::-webkit-scrollbar-track {
  background: var(--color-background-light);
  border-radius: 4px;
}

.comparison-viewer ::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.comparison-viewer ::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* ==================== 动画效果 ==================== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* ==================== 工具提示样式 ==================== */
.ant-tooltip-inner {
  background-color: rgba(0, 0, 0, 0.85);
  border-radius: 4px;
  font-size: 12px;
}

/* ==================== 加载状态 ==================== */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

/* ==================== 无障碍支持 ==================== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --color-diff-added: #00ff00;
    --color-diff-deleted: #ff0000;
    --color-diff-modified: #ffff00;
    --color-diff-moved: #00ffff;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
