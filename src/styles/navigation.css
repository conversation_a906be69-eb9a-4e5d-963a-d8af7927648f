/* 导航模式切换样式 */
.navigation-mode-toggle {
  transition: all 0.3s ease;
}

.navigation-mode-toggle:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.dark .navigation-mode-toggle:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

/* 顶部导航菜单样式 - 强制水平展开 */
.header-navigation-menu {
  border-bottom: none !important;
  display: flex !important;
  align-items: center !important;
  overflow: visible !important;
  height: 48px !important;
  line-height: 48px !important;
  width: 100% !important;
  flex: 1 !important;
  justify-content: flex-start !important;
  min-width: 0 !important;
}

/* 强制重写Ant Design Menu的根容器 */
.header-navigation-menu.ant-menu {
  display: flex !important;
  flex-wrap: nowrap !important;
  overflow: visible !important;
  width: 100% !important;
  background: transparent !important;
  border-bottom: none !important;
  justify-content: flex-start !important;
  flex: 1 !important;
  min-width: 0 !important;
}

/* 强制重写Menu的内部容器 */
.header-navigation-menu .ant-menu {
  display: flex !important;
  flex-wrap: nowrap !important;
  overflow: visible !important;
  width: 100% !important;
  background: transparent !important;
  border-bottom: none !important;
  justify-content: flex-start !important;
  flex: 1 !important;
  min-width: 0 !important;
}

/* 强制重写overflow容器 - 这是关键！ */
.header-navigation-menu .ant-menu-overflow {
  display: flex !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
  justify-content: flex-start !important;
  flex: 1 !important;
  min-width: 0 !important;
  overflow: visible !important;
  max-width: none !important;
}

/* 强制重写根容器 */
.header-navigation-menu .ant-menu-root {
  display: flex !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
  justify-content: flex-start !important;
  flex: 1 !important;
  min-width: 0 !important;
  overflow: visible !important;
}

/* 强制重写菜单项容器 */
.header-navigation-menu .ant-menu-overflow-item {
  display: flex !important;
  flex-shrink: 0 !important;
  flex-wrap: nowrap !important;
  width: auto !important;
  max-width: none !important;
}

.header-navigation-menu .ant-menu-item-group-list {
  display: flex !important;
  flex-shrink: 0 !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
}

/* 强制重写所有可能的内部容器 */
.header-navigation-menu .ant-menu-submenu,
.header-navigation-menu .ant-menu-submenu-title,
.header-navigation-menu .ant-menu-title-content {
  display: flex !important;
  flex-shrink: 0 !important;
  width: auto !important;
  max-width: none !important;
}

/* 终极强制样式 - 使用最高优先级 */
div[data-force-horizontal="true"].ant-menu.ant-menu-root.ant-menu-horizontal {
  display: flex !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
  justify-content: flex-start !important;
  flex: 1 !important;
  min-width: 0 !important;
  overflow: visible !important;
  max-width: none !important;
}

div[data-force-horizontal="true"].ant-menu .ant-menu-overflow {
  display: flex !important;
  flex-wrap: nowrap !important;
  width: 100% !important;
  justify-content: flex-start !important;
  flex: 1 !important;
  min-width: 0 !important;
  overflow: visible !important;
  max-width: none !important;
}

div[data-force-horizontal="true"].ant-menu .ant-menu-overflow-item {
  display: flex !important;
  flex-shrink: 0 !important;
  flex-wrap: nowrap !important;
  width: auto !important;
  max-width: none !important;
}

/* 强制所有菜单项水平排列 */
.header-navigation-menu[data-force-horizontal="true"] li.ant-menu-item {
  display: inline-flex !important;
  float: none !important;
  width: auto !important;
  max-width: none !important;
  flex-shrink: 0 !important;
}

/* 终极解决方案：完全重写Menu的布局逻辑 */
.header-navigation-menu.ant-menu-horizontal {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  align-items: center !important;
  width: 100% !important;
  overflow: visible !important;
}

.header-navigation-menu.ant-menu-horizontal > li {
  display: flex !important;
  flex-shrink: 0 !important;
  position: relative !important;
  float: none !important;
}

.header-navigation-menu.ant-menu-horizontal .ant-menu-overflow {
  display: contents !important;
}

.header-navigation-menu.ant-menu-horizontal .ant-menu-overflow-item {
  display: flex !important;
  flex-shrink: 0 !important;
}

/* 强制禁用Ant Design的响应式收缩 */
.header-navigation-menu .ant-menu-overflow-item-rest {
  display: none !important;
}

.header-navigation-menu .ant-menu-overflow-item-suffix {
  display: none !important;
}

/* 确保所有菜单项都显示 */
.header-navigation-menu .ant-menu-item,
.header-navigation-menu .ant-menu-submenu {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  flex-shrink: 0 !important;
}

.header-navigation-menu .ant-menu-item {
  border-bottom: 2px solid transparent !important;
  margin: 0 4px !important;
  padding: 0 16px !important;
  height: 48px !important;
  line-height: 48px !important;
  transition: all 0.3s ease;
  border-radius: 6px 6px 0 0;
  position: relative;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
  display: flex !important;
  align-items: center !important;
  min-width: fit-content !important;
}

.header-navigation-menu .ant-menu-item:first-child {
  margin-left: 0;
}

.header-navigation-menu .ant-menu-item:hover {
  border-bottom-color: var(--primary-color) !important;
  background-color: rgba(24, 144, 255, 0.06) !important;
  color: var(--primary-color) !important;
}

.header-navigation-menu .ant-menu-item-selected {
  border-bottom-color: var(--primary-color) !important;
  background-color: rgba(24, 144, 255, 0.1) !important;
  color: var(--primary-color) !important;
  font-weight: 500;
}

.header-navigation-menu .ant-menu-item-selected::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px 1px 0 0;
}

/* 响应式设计 - 与Layout组件的isMobile逻辑保持一致 */
@media (max-width: 575px) {
  .header-navigation-menu {
    display: none !important;
  }
}

/* 确保桌面端显示 */
@media (min-width: 576px) {
  .header-navigation-menu {
    display: flex !important;
  }
}

/* 导航模式切换动画 */
.layout-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 侧边栏折叠动画优化 */
.ant-layout-sider {
  transition: all 0.2s ease-in-out !important;
}

/* 顶部导航模式下的内容区域调整 */
.header-mode .ant-layout-content {
  margin-top: 0;
}

/* 侧边栏模式下的内容区域调整 */
.sidebar-mode .ant-layout-content {
  margin-left: 0;
}

/* 侧边导航样式 */
.side-navigation {
  height: 100%;
  overflow-y: auto;
}

.side-navigation-menu {
  border-right: none !important;
  height: 100% !important;
}

.side-navigation-menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  height: 40px;
  line-height: 40px;
  display: flex;
  align-items: center;
}

.side-navigation-menu .ant-menu-item:hover {
  background-color: rgba(24, 144, 255, 0.06) !important;
  color: var(--primary-color) !important;
}

.side-navigation-menu .ant-menu-item-selected {
  background-color: rgba(24, 144, 255, 0.1) !important;
  color: var(--primary-color) !important;
  font-weight: 500;
  position: relative;
}

.side-navigation-menu .ant-menu-item-selected::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 1.5px 0 0 1.5px;
}

.side-navigation-menu .ant-menu-item .anticon {
  font-size: 16px;
  margin-right: 12px;
}

/* 移动端适配 */
@media (max-width: 576px) {
  .navigation-mode-toggle {
    display: none;
  }
}