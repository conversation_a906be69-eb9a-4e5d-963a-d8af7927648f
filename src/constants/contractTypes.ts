/**
 * 合同类型统一配置
 * 消除重复定义，确保所有组件使用一致的合同类型选项
 */

// 合同类型配置接口
export interface ContractTypeConfig {
  value: string;
  label: string;
  description?: string;
}

// 统一的合同类型配置（与后端保持一致）
export const CONTRACT_TYPES: ContractTypeConfig[] = [
  {
    value: 'service',
    label: '服务合同',
    description: '提供服务的合同'
  },
  {
    value: 'purchase',
    label: '采购合同',
    description: '采购货物或设备的合同'
  },
  {
    value: 'sales',
    label: '销售合同',
    description: '销售商品的合同'
  },
  {
    value: 'lease',
    label: '租赁合同',
    description: '租赁房屋或设备的合同'
  },
  {
    value: 'employment',
    label: '劳动合同',
    description: '雇佣关系的合同'
  },
  {
    value: 'general',
    label: '通用合同',
    description: '其他类型的合同'
  }
];

// 获取所有合同类型值的数组（用于验证）
export const CONTRACT_TYPE_VALUES = CONTRACT_TYPES.map(type => type.value);

// 获取合同类型显示文本的映射
export const CONTRACT_TYPE_LABELS: Record<string, string> = CONTRACT_TYPES.reduce(
  (acc, type) => {
    acc[type.value] = type.label;
    return acc;
  },
  {} as Record<string, string>
);

// 根据值获取标签
export const getContractTypeLabel = (value: string): string => {
  return CONTRACT_TYPE_LABELS[value] || value;
};

// 生成Ant Design Select组件的选项
export const getContractTypeOptions = () => {
  return CONTRACT_TYPES.map(type => ({
    value: type.value,
    label: type.label
  }));
};

// 验证合同类型是否有效
export const isValidContractType = (type: string): boolean => {
  return CONTRACT_TYPE_VALUES.includes(type);
};