import React from 'react';
import { Menu } from 'antd';
import {
  HomeOutlined,
  FileTextOutlined,
  SearchOutlined,
  SwapOutlined,
  EditOutlined,
  BookOutlined,
  DiffOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';

const SideNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { theme } = useTheme();

  // 菜单项配置（与HeaderNavigation完全一致，保持功能完整性）
  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/contracts',
      icon: <FileTextOutlined />,
      label: '合同管理',
    },
    {
      key: '/review',
      icon: <SearchOutlined />,
      label: '智能审查',
    },
    {
      key: '/compare',
      icon: <SwapOutlined />,
      label: '条款对比',
    },
    {
      key: '/draft',
      icon: <EditOutlined />,
      label: '合同起草',
    },
    {
      key: '/knowledge',
      icon: <BookOutlined />,
      label: '知识库',
    },
    {
      key: '/file-compare',
      icon: <DiffOutlined />,
      label: '文件对比',
    },
    {
      key: '/contract-comparison',
      icon: <DiffOutlined />,
      label: '合同对比',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <div className="side-navigation">
      <Menu
        theme={theme === 'dark' ? 'dark' : 'light'}
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{
          height: '100%',
          borderRight: 0,
          backgroundColor: 'transparent',
        }}
        className="side-navigation-menu"
      />
    </div>
  );
};

export default SideNavigation;