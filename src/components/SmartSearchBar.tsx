import React, { useState, useEffect, useRef } from 'react';
import { 
  Input, 
  AutoComplete, 
  Button, 
  Dropdown, 
  Space, 
  Tag, 
  Tooltip,
  Modal,
  Form,
  Select,
  DatePicker,
  InputNumber
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  HistoryOutlined,
  SettingOutlined,
  ClearOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { useTheme } from '../contexts/ThemeContext';
import dayjs from 'dayjs';
import { CONTRACT_TYPES } from '../constants/contractTypes';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

export interface SearchField {
  key: string;
  label: string;
  type: 'text' | 'select' | 'date' | 'number' | 'dateRange';
  options?: { value: string; label: string }[];
  placeholder?: string;
}

export interface SearchCondition {
  field: string;
  operator: 'contains' | 'equals' | 'gt' | 'lt' | 'between' | 'in';
  value: any;
  label?: string;
}

export interface SearchQuery {
  keyword?: string;
  conditions: SearchCondition[];
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface SmartSearchBarProps {
  searchFields: SearchField[];
  onSearch: (query: SearchQuery) => void;
  onClear: () => void;
  placeholder?: string;
  showAdvanced?: boolean;
  showHistory?: boolean;
  savedSearches?: Array<{ id: string; name: string; query: SearchQuery }>;
  onSaveSearch?: (name: string, query: SearchQuery) => void;
  onLoadSearch?: (query: SearchQuery) => void;
}

const SmartSearchBar: React.FC<SmartSearchBarProps> = ({
  searchFields,
  onSearch,
  onClear,
  placeholder = "搜索合同...",
  showAdvanced = true,
  showHistory = true,
  savedSearches = [],
  onSaveSearch,
  onLoadSearch
}) => {
  const { colors } = useTheme();
  const [keyword, setKeyword] = useState('');
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showAdvancedModal, setShowAdvancedModal] = useState(false);
  const [conditions, setConditions] = useState<SearchCondition[]>([]);
  const [form] = Form.useForm();
  const searchInputRef = useRef<any>(null);

  // 搜索建议数据
  const searchSuggestions = [
    ...CONTRACT_TYPES.map(type => type.label),
    '已签署', '审查中', '已批准', '草稿',
    '高风险', '中风险', '低风险',
    '本月', '上月', '本年'
  ];

  // 加载搜索历史
  useEffect(() => {
    const history = localStorage.getItem('contract_search_history');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
  }, []);

  // 保存搜索历史
  const saveToHistory = (searchTerm: string) => {
    if (!searchTerm.trim()) return;
    
    const newHistory = [searchTerm, ...searchHistory.filter(item => item !== searchTerm)].slice(0, 10);
    setSearchHistory(newHistory);
    localStorage.setItem('contract_search_history', JSON.stringify(newHistory));
  };

  // 处理搜索建议
  const handleSearch = (value: string) => {
    const filtered = searchSuggestions.filter(item => 
      item.toLowerCase().includes(value.toLowerCase())
    );
    setSuggestions(filtered);
  };

  // 执行搜索
  const executeSearch = (searchKeyword?: string) => {
    const finalKeyword = searchKeyword || keyword;
    
    if (finalKeyword) {
      saveToHistory(finalKeyword);
    }

    const query: SearchQuery = {
      keyword: finalKeyword,
      conditions
    };

    onSearch(query);
  };

  // 清除搜索
  const handleClear = () => {
    setKeyword('');
    setConditions([]);
    form.resetFields();
    onClear();
  };

  // 添加高级搜索条件
  const addCondition = (values: any) => {
    const newCondition: SearchCondition = {
      field: values.field,
      operator: values.operator,
      value: values.value,
      label: `${searchFields.find(f => f.key === values.field)?.label} ${getOperatorLabel(values.operator)} ${formatValue(values.value)}`
    };

    setConditions([...conditions, newCondition]);
    form.resetFields();
  };

  // 移除搜索条件
  const removeCondition = (index: number) => {
    setConditions(conditions.filter((_, i) => i !== index));
  };

  // 获取操作符标签
  const getOperatorLabel = (operator: string) => {
    const labels = {
      contains: '包含',
      equals: '等于',
      gt: '大于',
      lt: '小于',
      between: '介于',
      in: '属于'
    };
    return labels[operator as keyof typeof labels] || operator;
  };

  // 格式化值显示
  const formatValue = (value: any) => {
    if (Array.isArray(value)) {
      return value.join(', ');
    }
    if (dayjs.isDayjs(value)) {
      return value.format('YYYY-MM-DD');
    }
    return String(value);
  };

  // 搜索历史下拉菜单
  const historyMenu = {
    items: searchHistory.map((item, index) => ({
      key: index,
      label: (
        <div className="flex items-center justify-between">
          <span>{item}</span>
          <HistoryOutlined className="text-gray-400" />
        </div>
      ),
      onClick: () => {
        setKeyword(item);
        executeSearch(item);
      }
    }))
  };

  // 保存的搜索下拉菜单
  const savedSearchMenu = {
    items: savedSearches.map((search) => ({
      key: search.id,
      label: (
        <div className="flex items-center justify-between">
          <span>{search.name}</span>
          <SaveOutlined className="text-gray-400" />
        </div>
      ),
      onClick: () => {
        if (onLoadSearch) {
          onLoadSearch(search.query);
          setKeyword(search.query.keyword || '');
          setConditions(search.query.conditions);
        }
      }
    }))
  };

  return (
    <div className="space-y-3">
      {/* 主搜索栏 */}
      <div className="flex items-center space-x-2">
        <div className="flex-1">
          <AutoComplete
            value={keyword}
            options={suggestions.map(item => ({ value: item }))}
            onSearch={handleSearch}
            onChange={setKeyword}
            onSelect={(value) => {
              setKeyword(value);
              executeSearch(value as string);
            }}
          >
            <Search
              ref={searchInputRef}
              placeholder={placeholder}
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={executeSearch}
              allowClear
            />
          </AutoComplete>
        </div>

        {/* 搜索历史 */}
        {showHistory && searchHistory.length > 0 && (
          <Dropdown menu={historyMenu} placement="bottomRight">
            <Button icon={<HistoryOutlined />} size="large">
              历史
            </Button>
          </Dropdown>
        )}

        {/* 保存的搜索 */}
        {savedSearches.length > 0 && (
          <Dropdown menu={savedSearchMenu} placement="bottomRight">
            <Button icon={<SaveOutlined />} size="large">
              已保存
            </Button>
          </Dropdown>
        )}

        {/* 高级搜索 */}
        {showAdvanced && (
          <Button 
            icon={<FilterOutlined />} 
            size="large"
            onClick={() => setShowAdvancedModal(true)}
          >
            高级
          </Button>
        )}

        {/* 清除 */}
        <Button 
          icon={<ClearOutlined />} 
          size="large"
          onClick={handleClear}
        >
          清除
        </Button>
      </div>

      {/* 搜索条件标签 */}
      {conditions.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {conditions.map((condition, index) => (
            <Tag
              key={index}
              closable
              onClose={() => removeCondition(index)}
              color="blue"
            >
              {condition.label}
            </Tag>
          ))}
        </div>
      )}

      {/* 高级搜索模态框 */}
      <Modal
        title="高级搜索"
        open={showAdvancedModal}
        onCancel={() => setShowAdvancedModal(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={addCondition}
        >
          <div className="grid grid-cols-3 gap-4">
            <Form.Item
              name="field"
              label="搜索字段"
              rules={[{ required: true, message: '请选择搜索字段' }]}
            >
              <Select placeholder="选择字段">
                {searchFields.map(field => (
                  <Option key={field.key} value={field.key}>
                    {field.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="operator"
              label="条件"
              rules={[{ required: true, message: '请选择条件' }]}
            >
              <Select placeholder="选择条件">
                <Option value="contains">包含</Option>
                <Option value="equals">等于</Option>
                <Option value="gt">大于</Option>
                <Option value="lt">小于</Option>
                <Option value="between">介于</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="value"
              label="值"
              rules={[{ required: true, message: '请输入值' }]}
            >
              <Input placeholder="输入搜索值" />
            </Form.Item>
          </div>

          <div className="flex justify-end space-x-2">
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
            <Button type="primary" htmlType="submit">
              添加条件
            </Button>
          </div>
        </Form>

        <div className="mt-4 flex justify-end">
          <Button 
            type="primary" 
            onClick={() => {
              executeSearch();
              setShowAdvancedModal(false);
            }}
          >
            执行搜索
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default SmartSearchBar;
