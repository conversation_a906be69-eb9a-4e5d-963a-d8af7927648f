/* 文档对比结果视图样式 */
.comparison-result-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 文档内容区域样式 */
.document-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.8;
  color: #333;
}

/* HTML内容样式 - 表格和格式支持 */
.document-content .prose,
.document-content .html-content {
  max-width: none;
  font-family: inherit;
  line-height: inherit;
}

/* 改进的HTML内容渲染样式 */
.html-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 确保HTML标签正确渲染而不是显示为文本 */
.html-content * {
  font-family: inherit;
  line-height: inherit;
}

.document-content .prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  font-size: 14px;
  background-color: #fff;
  border: 2px solid #374151;
}

.document-content .prose table th,
.document-content .prose table td {
  border: 1px solid #374151;
  padding: 8px 12px;
  text-align: left;
  vertical-align: top;
}

.document-content .prose table th {
  background-color: #f3f4f6;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #374151;
}

.document-content .prose table tr:nth-child(even) {
  background-color: #f9fafb;
}

.document-content .prose table tr:hover {
  background-color: #f3f4f6;
}

/* 表格内的文本样式 */
.document-content .prose table p {
  margin: 0.25em 0;
}

/* 表格标题行样式 */
.document-content .prose table tr:first-child th {
  background-color: #e5e7eb;
  font-weight: 700;
}

.document-content .prose p {
  margin: 12px 0;
  line-height: 1.8;
}

.document-content .prose h1,
.document-content .prose h2,
.document-content .prose h3,
.document-content .prose h4,
.document-content .prose h5,
.document-content .prose h6 {
  margin: 20px 0 12px 0;
  font-weight: 600;
  color: #262626;
}

.document-content .prose ul,
.document-content .prose ol {
  margin: 12px 0;
  padding-left: 24px;
}

.document-content .prose li {
  margin: 4px 0;
  line-height: 1.6;
}

.document-content .prose strong,
.html-content strong {
  font-weight: 600;
  color: #262626;
}

.document-content .prose em,
.html-content em {
  font-style: italic;
  color: #595959;
}

/* 扩展HTML元素样式支持 */
.html-content span {
  display: inline;
}

.html-content div {
  margin: 0.5em 0;
}

.html-content br {
  line-height: 1.8;
}

/* 确保mammoth生成的HTML样式正确显示 */
.html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  font-size: 14px;
  background-color: #fff;
  border: 2px solid #374151;
}

.html-content table th,
.html-content table td {
  border: 1px solid #374151;
  padding: 8px 12px;
  text-align: left;
  vertical-align: top;
}

.html-content table th {
  background-color: #f3f4f6;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #374151;
}

.html-content table tr:nth-child(even) {
  background-color: #f9fafb;
}

.html-content table tr:hover {
  background-color: #f3f4f6;
}

.html-content p {
  margin: 12px 0;
  line-height: 1.8;
}

.html-content h1,
.html-content h2,
.html-content h3,
.html-content h4,
.html-content h5,
.html-content h6 {
  margin: 20px 0 12px 0;
  font-weight: 600;
  color: #262626;
}

.html-content ul,
.html-content ol {
  margin: 12px 0;
  padding-left: 24px;
}

.html-content li {
  margin: 4px 0;
  line-height: 1.6;
}

/* 表单样式 */
.document-content .prose input[type="text"], 
.document-content .prose input[type="email"], 
.document-content .prose input[type="tel"] {
  border: none;
  border-bottom: 1px solid #374151;
  background: transparent;
  padding: 2px 4px;
  width: 100%;
}

/* 下划线样式 */
.document-content .prose u {
  text-decoration: underline;
  text-decoration-thickness: 1px;
}

/* 强调文本 */
.document-content .prose strong, 
.document-content .prose b {
  font-weight: 600;
}

/* 文档标题样式 */
.document-title {
  font-size: 1.2em;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 0.8em;
  padding: 0.5em 0;
  border-bottom: 2px solid #f0f0f0;
}

/* 文档章节标题样式 */
.document-section-title {
  font-size: 1.1em;
  font-weight: 600;
  color: #2f54eb;
  margin: 1.5em 0 0.8em 0;
  padding-left: 1em;
  border-left: 4px solid #2f54eb;
  background-color: #f6f8ff;
  padding: 0.5em 0 0.5em 1em;
}

/* 文档条款样式 */
.document-clause {
  margin: 1em 0;
  padding: 0.8em;
  background-color: #fafafa;
  border-left: 3px solid #d9d9d9;
  border-radius: 4px;
}

/* 文档列表样式 */
.document-list-item {
  margin: 0.5em 0;
  padding-left: 1.5em;
  position: relative;
}

.document-list-item::before {
  content: "•";
  color: #1890ff;
  font-weight: bold;
  position: absolute;
  left: 0.5em;
}

/* 文档编号列表样式 */
.document-numbered-item {
  margin: 0.5em 0;
  padding-left: 2em;
  position: relative;
}

/* 左右分栏对比布局 */
.comparison-container {
  display: flex;
  height: 100%;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.comparison-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  position: relative;
}

.comparison-pane:first-child {
  border-right: 1px solid #d9d9d9;
}

.comparison-header {
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #d9d9d9;
  font-weight: 600;
  color: #262626;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.comparison-header .file-name {
  font-size: 14px;
  color: #1890ff;
}

.comparison-header .file-info {
  font-size: 12px;
  color: #8c8c8c;
}

.comparison-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

/* 行号和内容布局 */
.line-container {
  display: flex;
  min-height: 100%;
}

.line-numbers {
  background: #f8f9fa;
  border-right: 1px solid #e1e4e8;
  padding: 8px 0;
  min-width: 50px;
  text-align: right;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #6a737d;
  user-select: none;
  flex-shrink: 0;
}

.line-number {
  padding: 0 8px;
  line-height: 20px;
  height: 20px;
  display: block;
}

.line-content {
  flex: 1;
  padding: 8px 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.content-line {
  line-height: 20px;
  min-height: 20px;
  padding: 0;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  position: relative;
}

/* 逐行对比中的HTML内容样式 */
.content-line div {
  display: inline;
  margin: 0;
  padding: 0;
}

.content-line p {
  display: inline;
  margin: 0;
  padding: 0;
}

.content-line strong {
  font-weight: 600;
  color: #262626;
}

.content-line em {
  font-style: italic;
}

.content-line table {
  display: table;
  width: 100%;
  border-collapse: collapse;
  margin: 4px 0;
  font-size: 12px;
  background-color: #fff;
}

.content-line table th,
.content-line table td {
  border: 1px solid #d0d7de;
  padding: 6px 8px;
  text-align: left;
  vertical-align: top;
  font-size: 12px;
  line-height: 1.4;
}

.content-line table th {
  background-color: #f6f8fa;
  font-weight: 600;
  color: #24292f;
}

.content-line table tr:nth-child(even) {
  background-color: #f6f8fa;
}

.content-line:hover {
  background-color: #f6f8fa;
}

/* 差异高亮样式 - 类似Git diff */
.line-added {
  background-color: #e6ffed;
  border-left: 3px solid #28a745;
}

.line-added .line-number {
  background-color: #cdffd8;
  color: #28a745;
}

.line-removed {
  background-color: #ffeef0;
  border-left: 3px solid #d73a49;
}

.line-removed .line-number {
  background-color: #ffdce0;
  color: #d73a49;
}

.line-modified {
  background-color: #fff8e1;
  border-left: 3px solid #ffa726;
}

.line-modified .line-number {
  background-color: #ffe0b2;
  color: #ffa726;
}

.line-context {
  background-color: #fff;
}

/* 内联差异高亮 */
.inline-added {
  background-color: #acf2bd;
  color: #22863a;
  padding: 0 2px;
  border-radius: 2px;
}

.inline-removed {
  background-color: #fdb8c0;
  color: #cb2431;
  padding: 0 2px;
  border-radius: 2px;
  text-decoration: line-through;
}

.inline-modified {
  background-color: #ffdf5d;
  color: #b08800;
  padding: 0 2px;
  border-radius: 2px;
}

/* 行内差异高亮 - 按严重程度分类 */
.inline-high {
  background-color: #fdb8c0;
  color: #cb2431;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: 600;
  cursor: pointer;
}

.inline-medium {
  background-color: #ffdf5d;
  color: #b08800;
  padding: 0 2px;
  border-radius: 2px;
  cursor: pointer;
}

.inline-low {
  background-color: #acf2bd;
  color: #22863a;
  padding: 0 2px;
  border-radius: 2px;
  cursor: pointer;
}

.inline-similarity {
  background-color: #e6f7ff;
  color: #1890ff;
  padding: 0 2px;
  border-radius: 2px;
  cursor: pointer;
  border-bottom: 1px dotted #1890ff;
}

/* 行内高亮悬停效果 */
.inline-high:hover,
.inline-medium:hover,
.inline-low:hover,
.inline-similarity:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 块级对比样式 */
.block-container {
  display: flex;
  width: 100%;
}

.block-numbers {
  width: 50px;
  background-color: #f6f8fa;
  border-right: 1px solid #d0d7de;
  padding: 8px 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.45;
  color: #656d76;
  text-align: right;
  user-select: none;
}

.block-number {
  display: block;
  padding: 0 8px;
  min-height: 20px;
}

.block-content {
  flex: 1;
  padding: 8px 12px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.content-block {
  margin: 4px 0;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid transparent;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.content-block:hover {
  background-color: #f6f8fa;
}

/* 块类型样式 */
.block-added {
  background-color: #e6ffed;
  border-left-color: #28a745;
}

.block-removed {
  background-color: #ffeef0;
  border-left-color: #d73a49;
}

.block-modified {
  background-color: #fff8e1;
  border-left-color: #ffa726;
}

.block-context {
  background-color: #fff;
  border-left-color: transparent;
}

.block-empty {
  background-color: #f8f9fa;
  border-left-color: #dee2e6;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-block-placeholder {
  color: #6c757d;
  font-style: italic;
  font-size: 12px;
}

.block-type-indicator {
  font-size: 16px;
  line-height: 1;
  margin-top: 2px;
  flex-shrink: 0;
}

.block-content-wrapper {
  flex: 1;
  min-width: 0;
}

/* 块内容样式 */
.plain-text-block {
  line-height: 1.6;
  word-wrap: break-word;
}

/* 表格在块级显示中的优化 */
.content-block table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  font-size: 13px;
  background-color: #fff;
  border: 1px solid #d0d7de;
  border-radius: 4px;
  overflow: hidden;
}

.content-block table th,
.content-block table td {
  border: 1px solid #d0d7de;
  padding: 8px 12px;
  text-align: left;
  vertical-align: top;
  line-height: 1.4;
}

.content-block table th {
  background-color: #f6f8fa;
  font-weight: 600;
  color: #24292f;
}

.content-block table tr:nth-child(even) {
  background-color: #f6f8fa;
}

.content-block table tr:hover {
  background-color: #f1f8ff;
}

/* 逐行对比的行级样式 */
.content-line {
  margin: 4px 0;
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid transparent;
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  min-height: 40px;
}

.content-line:hover {
  background-color: #f6f8fa;
}

.line-type-indicator {
  font-size: 16px;
  line-height: 1;
  margin-top: 2px;
  flex-shrink: 0;
}

.line-content-wrapper {
  flex: 1;
  min-width: 0;
}

/* 空行占位符 */
.line-empty {
  background-color: #f8f9fa;
  border-left-color: #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-line-placeholder {
  color: #6c757d;
  font-style: italic;
  font-size: 12px;
}

/* 确保HTML内容在逐行显示中正确渲染 */
.line-content-wrapper .html-content-renderer {
  width: 100%;
}

.line-content-wrapper .html-content-renderer table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  font-size: 13px;
  background-color: #fff;
  border: 1px solid #d0d7de;
  border-radius: 4px;
  overflow: hidden;
}

.line-content-wrapper .html-content-renderer table th,
.line-content-wrapper .html-content-renderer table td {
  border: 1px solid #d0d7de;
  padding: 8px 12px;
  text-align: left;
  vertical-align: top;
  line-height: 1.4;
}

.line-content-wrapper .html-content-renderer table th {
  background-color: #f6f8fa;
  font-weight: 600;
  color: #24292f;
}

.line-content-wrapper .html-content-renderer table tr:nth-child(even) {
  background-color: #f6f8fa;
}

.line-content-wrapper .html-content-renderer table tr:hover {
  background-color: #f1f8ff;
}

/* 确保段落和其他HTML元素正确显示 */
.line-content-wrapper .html-content-renderer p {
  margin: 4px 0;
  line-height: 1.6;
}

.line-content-wrapper .html-content-renderer h1,
.line-content-wrapper .html-content-renderer h2,
.line-content-wrapper .html-content-renderer h3,
.line-content-wrapper .html-content-renderer h4,
.line-content-wrapper .html-content-renderer h5,
.line-content-wrapper .html-content-renderer h6 {
  margin: 8px 0 4px 0;
  font-weight: 600;
}

.line-content-wrapper .html-content-renderer ul,
.line-content-wrapper .html-content-renderer ol {
  margin: 4px 0;
  padding-left: 20px;
}

.line-content-wrapper .html-content-renderer li {
  margin: 2px 0;
}

/* 差异统计 */
.diff-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.diff-stat {
  display: flex;
  align-items: center;
  gap: 4px;
}

.diff-stat-added {
  color: #28a745;
}

.diff-stat-removed {
  color: #d73a49;
}

.diff-stat-modified {
  color: #ffa726;
}

/* 差异导航 */
.diff-navigation {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 4px;
  z-index: 10;
}

.diff-nav-button {
  width: 24px;
  height: 24px;
  border: 1px solid #d9d9d9;
  background: #fff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  color: #595959;
  transition: all 0.2s;
}

.diff-nav-button:hover {
  background: #f5f5f5;
  border-color: #40a9ff;
  color: #1890ff;
}

.diff-nav-button:active {
  background: #e6f7ff;
}

/* 同步滚动指示器 */
.sync-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #52c41a;
  opacity: 0;
  transition: opacity 0.3s;
}

.sync-indicator.active {
  opacity: 1;
}

/* 差异高亮样式（保持原有功能） */
.highlight-difference {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 2px;
  padding: 0 2px;
  margin: 0 1px;
}

.highlight-difference.severity-high {
  background-color: #fff1f0;
  border-color: #ff7875;
}

.highlight-difference.severity-medium {
  background-color: #fff7e6;
  border-color: #ffa940;
}

.highlight-difference.severity-low {
  background-color: #f6ffed;
  border-color: #95de64;
}

/* 相似项高亮样式 */
.highlight-similarity {
  background-color: #e6f7ff;
  border-bottom: 2px solid #1890ff;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 2px;
  padding: 0 2px;
  margin: 0 1px;
}

.highlight-similarity:hover {
  background-color: #bae7ff;
  transform: translateY(-1px);
}

.highlight-similarity.high-similarity {
  background-color: #d9f7be;
  border-bottom-color: #52c41a;
}

.highlight-similarity.high-similarity:hover {
  background-color: #b7eb8f;
}

/* 选中状态样式 */
.highlight-selected {
  box-shadow: 0 0 0 2px #1890ff;
  z-index: 1;
  position: relative;
}

/* 滚动条样式 */
.document-content::-webkit-scrollbar {
  width: 8px;
}

.document-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.document-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.document-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comparison-result-view .w-64 {
    width: 240px;
  }
  
  .document-content {
    font-size: 14px;
    padding: 1rem;
  }
  
  .document-title {
    font-size: 1.1em;
  }
  
  .document-section-title {
    font-size: 1em;
  }
}

@media (max-width: 480px) {
  .comparison-result-view .w-64 {
    width: 200px;
  }
  
  .document-content {
    font-size: 13px;
    padding: 0.5rem;
  }
}

/* 全屏模式样式 */
.comparison-result-view.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: white;
}

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

/* 工具栏样式 */
.comparison-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  gap: 16px;
}

.view-mode-selector {
  display: flex;
  gap: 4px;
  margin-right: 12px;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background: #dee2e6;
  margin: 0 8px;
}

.comparison-toolbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.toolbar-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-button {
  transition: all 0.2s ease;
}

.toolbar-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 侧边栏样式优化 */
.comparison-sidebar {
  background: #fafafa;
  border-right: 1px solid #d9d9d9;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.comparison-sidebar-header {
  background: white;
  border-bottom: 1px solid #d9d9d9;
  padding: 12px 16px;
  flex-shrink: 0;
}

.comparison-sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.sidebar-sections {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sidebar-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sidebar-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.sidebar-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.stat-value {
  font-weight: 600;
  color: #374151;
}

.file-info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-info-item {
  padding: 8px;
  border-radius: 6px;
  background-color: #f9fafb;
}

.file-name {
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-name.primary {
  color: #2563eb;
}

.file-name.secondary {
  color: #059669;
}

.file-size {
  font-size: 12px;
  color: #6b7280;
}

.differences-list {
  max-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.difference-item {
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
}

.difference-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.difference-item.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
  box-shadow: 0 0 0 1px #3b82f6;
}

.difference-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.difference-type {
  font-size: 11px;
  color: #6b7280;
}

.difference-description {
  font-size: 12px;
  line-height: 1.4;
  color: #374151;
}

.differences-more {
  text-align: center;
  padding: 8px;
  color: #6b7280;
  font-size: 12px;
}

/* 文档预览区域样式 */
.document-preview-area {
  flex: 1;
  display: flex;
  min-width: 0;
}

.document-preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  border-right: 1px solid #d9d9d9;
}

.document-preview-panel:last-child {
  border-right: none;
}

.document-preview-header {
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.document-preview-content {
  flex: 1;
  overflow: auto;
  background: white;
}