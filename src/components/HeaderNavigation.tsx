import React from 'react';
import { Menu } from 'antd';
import {
  HomeOutlined,
  FileTextOutlined,
  SearchOutlined,
  SwapOutlined,
  EditOutlined,
  BookOutlined,
  DiffOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTheme } from '../contexts/ThemeContext';

const HeaderNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { theme, colors } = useTheme();

  // 菜单项配置（与Layout组件保持一致）
  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/contracts',
      icon: <FileTextOutlined />,
      label: '合同管理',
    },
    {
      key: '/review',
      icon: <SearchOutlined />,
      label: '智能审查',
    },
    {
      key: '/compare',
      icon: <SwapOutlined />,
      label: '条款对比',
    },
    {
      key: '/draft',
      icon: <EditOutlined />,
      label: '合同起草',
    },
    {
      key: '/knowledge',
      icon: <BookOutlined />,
      label: '知识库',
    },
    {
      key: '/file-compare',
      icon: <DiffOutlined />,
      label: '文件对比',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <div style={{ 
      flex: 1, 
      minWidth: 0, 
      display: 'flex',
      width: '100%',
      justifyContent: 'flex-start',
      alignItems: 'center',
      overflow: 'visible'
    }}>
      <Menu
        mode="horizontal"
        selectedKeys={[location.pathname]}
        onClick={handleMenuClick}
        items={menuItems}
        style={{
          flex: 1,
          minWidth: 0,
          backgroundColor: 'transparent',
          borderBottom: 'none',
          width: '100%',
          justifyContent: 'flex-start',
          overflow: 'visible',
          display: 'flex',
          flexWrap: 'nowrap',
          maxWidth: 'none'
        }}
        className="header-navigation-menu"
        overflowedIndicator={null}
        triggerSubMenuAction="click"
        expandIcon={null}
        {...{
          'data-force-horizontal': 'true'
        }}
      />
    </div>
  );
};

export default HeaderNavigation;