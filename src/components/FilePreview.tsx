/**
 * File Preview Component
 * 文件预览组件 - 支持PDF、Word文档和文本文件预览
 */
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spin, message } from 'antd';
import { FileTextOutlined, DownloadOutlined, EyeOutlined, CloseOutlined, FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';

// 全屏模式样式
const fullscreenStyles = `
  .fullscreen-modal .ant-modal {
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
    height: 100vh !important;
  }
  .fullscreen-modal .ant-modal-content {
    height: 100vh !important;
    border-radius: 0 !important;
  }
  .fullscreen-modal .ant-modal-body {
    height: calc(100vh - 110px) !important;
    padding: 16px !important;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleId = 'file-preview-fullscreen-styles';
  if (!document.getElementById(styleId)) {
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = fullscreenStyles;
    document.head.appendChild(style);
  }
}

interface FilePreviewProps {
  visible: boolean;
  onClose: () => void;
  filePath?: string;
  fileName?: string;
  fileUrl?: string;
  mimeType?: string;
}

interface FilePreviewData {
  content?: string;
  url?: string;
  error?: string;
}

const FilePreview: React.FC<FilePreviewProps> = ({
  visible,
  onClose,
  filePath,
  fileName,
  fileUrl,
  mimeType
}) => {
  const [loading, setLoading] = useState(false);
  const [iframeLoading, setIframeLoading] = useState(false);
  const [previewData, setPreviewData] = useState<FilePreviewData>({});
  const [activeTab, setActiveTab] = useState('preview');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (visible && (filePath || fileUrl)) {
      loadFilePreview();
    }
  }, [visible, filePath, fileUrl]);

  // 清理超时定时器
  useEffect(() => {
    return () => {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  }, [loadingTimeout]);

  const loadFilePreview = async () => {
    console.log('🔍 [FilePreview] loadFilePreview 开始执行');
    console.log('🔍 [FilePreview] 传入参数:', {
      filePath,
      fileName,
      fileUrl,
      mimeType,
      visible
    });
    
    if (!filePath && !fileUrl) {
      console.warn('⚠️ [FilePreview] 没有文件路径或URL，退出预览');
      return;
    }
    
    setLoading(true);
    try {
      // 如果有直接的URL，使用它；否则根据文件类型选择合适的URL获取方式
      let previewUrl = fileUrl;
      console.log('🔍 [FilePreview] 初始预览URL:', previewUrl);
      
      if (!previewUrl && filePath) {
        // 对于Word文档，使用公共URL以确保Office在线预览服务可以访问
        const isWordDocument = mimeType === 'application/msword' || 
                              mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        
        console.log('🔍 [FilePreview] 文件类型检测:', {
          mimeType,
          isWordDocument
        });
        
        const urlEndpoint = isWordDocument ? 'public-url' : 'signed-url';
        const apiUrl = `/api/files/${urlEndpoint}/${encodeURIComponent(filePath)}`;
        
        console.log('🔍 [FilePreview] 准备请求API:', {
          endpoint: urlEndpoint,
          encodedFilePath: encodeURIComponent(filePath),
          fullApiUrl: apiUrl,
          token: localStorage.getItem('token') ? '存在' : '不存在'
        });
        
        const response = await fetch(apiUrl, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });
        
        console.log('🔍 [FilePreview] API响应状态:', {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
          headers: Object.fromEntries(response.headers.entries())
        });
        
        if (response.ok) {
          const result = await response.json();
          console.log('🔍 [FilePreview] API响应数据:', result);
          
          if (result.success) {
            previewUrl = isWordDocument ? result.data.publicUrl : result.data.signedUrl;
            console.log('✅ [FilePreview] 成功获取预览URL:', previewUrl);
          } else {
            console.error('❌ [FilePreview] API返回失败:', result.message);
            throw new Error(result.message || '获取文件链接失败');
          }
        } else {
          const errorText = await response.text();
          console.error('❌ [FilePreview] HTTP请求失败:', {
            status: response.status,
            statusText: response.statusText,
            responseText: errorText
          });
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      }
      
      if (previewUrl) {
        console.log('✅ [FilePreview] 设置预览数据:', { url: previewUrl });
        setPreviewData({ url: previewUrl });
        
        // 如果获取到了预览URL，开始iframe加载状态
        setIframeLoading(true);
        
        // 设置加载超时（30秒）
        const timeout = setTimeout(() => {
          console.warn('⚠️ [FilePreview] iframe加载超时');
          setIframeLoading(false);
        }, 30000);
        setLoadingTimeout(timeout);
      } else {
        console.error('❌ [FilePreview] 无法获取预览URL');
        setPreviewData({ error: '无法获取文件预览链接' });
      }
    } catch (error) {
      console.error('❌ [FilePreview] 加载文件预览异常:', error);
      console.error('❌ [FilePreview] 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
      const errorMessage = error instanceof Error ? error.message : '加载文件预览失败';
      setPreviewData({ error: errorMessage });
      message.error(errorMessage);
    } finally {
      setLoading(false);
      console.log('🔍 [FilePreview] loadFilePreview 执行完成');
    }
  };

  const handleDownload = async () => {
    if (!filePath) {
      message.error('文件路径不存在');
      return;
    }

    try {
      const response = await fetch(`/api/files/download/${encodeURIComponent(filePath)}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName || 'download';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        message.success('文件下载成功');
      } else {
        message.error('文件下载失败');
      }
    } catch (error) {
      console.error('Download file error:', error);
      message.error('文件下载失败');
    }
  };

  const handleIframeLoad = () => {
    console.log('✅ [FilePreview] iframe 加载成功');
    setIframeLoading(false);
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }
  };

  const handleIframeError = (e: any) => {
    console.error('❌ [FilePreview] iframe 加载失败:', e);
    setIframeLoading(false);
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }
  };

  const renderPreviewContent = () => {
    // API请求加载状态
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center h-96 space-y-4">
          <Spin size="large" />
          <div className="text-center">
            <p className="text-lg font-medium text-gray-700">正在获取文件链接...</p>
            <p className="text-sm text-gray-500 mt-2">
              {mimeType === 'application/msword' || mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
                ? '正在准备Word文档预览链接...' 
                : mimeType === 'application/pdf' 
                ? '正在获取PDF文档链接...' 
                : '正在处理文件链接...'}
            </p>
          </div>
        </div>
      );
    }

    // iframe加载状态
    if (iframeLoading && previewData.url) {
      const isWordDocument = mimeType === 'application/msword' || mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      return (
        <div className="flex flex-col items-center justify-center h-96 space-y-4">
          <Spin size="large" />
          <div className="text-center">
            <p className="text-lg font-medium text-gray-700">
              {isWordDocument ? '正在加载Word文档预览...' 
                : mimeType === 'application/pdf' ? '正在加载PDF文档...' 
                : '正在加载文件预览...'}
            </p>
            <p className="text-sm text-gray-500 mt-2">
              {isWordDocument 
                ? 'Office在线预览服务可能需要较长时间，请耐心等待...' 
                : '文档正在渲染中，请稍候...'}
            </p>
          </div>
        </div>
      );
    }

    if (previewData.error) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-gray-500">
          <FileTextOutlined className="text-4xl mb-4 text-red-400" />
          <p className="text-lg font-medium text-gray-700 mb-2">文件预览失败</p>
          <p className="text-sm text-gray-500 mb-4 text-center max-w-md">{previewData.error}</p>
          <div className="flex gap-2">
            <Button 
              type="primary" 
              icon={<EyeOutlined />} 
              onClick={loadFilePreview}
              loading={loading}
            >
              重试预览
            </Button>
            <Button 
              icon={<DownloadOutlined />} 
              onClick={handleDownload}
              disabled={!filePath}
            >
              下载文件
            </Button>
          </div>
        </div>
      );
    }

    if (!previewData.url) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-gray-500">
          <FileTextOutlined className="text-4xl mb-4" />
          <p>暂无预览内容</p>
        </div>
      );
    }

    // 根据文件类型渲染不同的预览内容
    const previewHeight = isFullscreen ? 'calc(100vh - 200px)' : '24rem';
    
    if (mimeType === 'application/pdf') {
      return (
        <div className="w-full" style={{ height: previewHeight }}>
          <iframe
            src={previewData.url}
            className="w-full h-full border-0"
            title="PDF预览"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
          />
        </div>
      );
    }

    if (mimeType === 'text/plain') {
      return (
        <div className="w-full overflow-auto" style={{ height: previewHeight }}>
          <iframe
            src={previewData.url}
            className="w-full h-full border-0"
            title="文本预览"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
          />
        </div>
      );
    }

    // Word文档预览支持
    if (mimeType === 'application/msword' || mimeType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      const officePreviewUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(previewData.url)}`;
      console.log('🔍 [FilePreview] Word文档预览URL:', {
        originalUrl: previewData.url,
        encodedUrl: encodeURIComponent(previewData.url),
        finalOfficeUrl: officePreviewUrl
      });
      
      return (
        <div className="w-full" style={{ height: previewHeight }}>
          <iframe
            src={officePreviewUrl}
            className="w-full h-full border-0"
            title="Word文档预览"
            onLoad={handleIframeLoad}
            onError={handleIframeError}
          />
        </div>
      );
    }

    // 其他文件类型
    return (
      <div className="flex flex-col items-center justify-center h-96 text-gray-500">
        <FileTextOutlined className="text-4xl mb-4" />
        <p>此文件类型不支持在线预览</p>
        <p className="text-sm mt-2">请下载文件查看完整内容</p>
        <Button 
          type="primary" 
          icon={<DownloadOutlined />} 
          onClick={handleDownload}
          className="mt-4"
        >
          下载文件
        </Button>
      </div>
    );
  };

  const renderFileInfo = () => {
    return (
      <div className="p-4 bg-gray-50 rounded">
        <h4 className="font-medium mb-3">文件信息</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">文件名：</span>
            <span>{fileName || '未知'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">文件类型：</span>
            <span>{mimeType || '未知'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">文件路径：</span>
            <span className="truncate max-w-xs" title={filePath}>
              {filePath || '未知'}
            </span>
          </div>
        </div>
        
        <div className="mt-4 flex gap-2">
          <Button 
            icon={<DownloadOutlined />} 
            onClick={handleDownload}
            disabled={!filePath}
          >
            下载
          </Button>
      
        </div>
      </div>
    );
  };

  const tabItems: TabsProps['items'] = [
    {
      key: 'preview',
      label: '文件预览',
      children: renderPreviewContent()
    },
    {
      key: 'info',
      label: '文件信息',
      children: renderFileInfo()
    }
  ];

  return (
    <Modal
      title={
        <div className="flex items-center justify-between">
          <span>文件预览 - {fileName}</span>
          <div className="flex items-center gap-2">
            <Button 
              type="text" 
              icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} 
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="text-gray-500 hover:text-gray-700"
              title={isFullscreen ? '退出全屏' : '全屏显示'}
            />
            <Button 
              type="text" 
              icon={<CloseOutlined />} 
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            />
          </div>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={isFullscreen ? '100vw' : 800}
      style={isFullscreen ? { top: 0, paddingBottom: 0, maxWidth: '100vw' } : {}}
      className={`file-preview-modal ${isFullscreen ? 'fullscreen-modal' : ''}`}
      closeIcon={null}
      centered={!isFullscreen}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        className="mt-4"
      />
    </Modal>
  );
};

export default FilePreview;