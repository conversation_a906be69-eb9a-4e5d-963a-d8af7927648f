/**
 * 差异导航组件
 * 提供差异列表展示、快速导航、统计信息显示和导出功能
 * 专为文件对比功能设计，与现有功能完全独立
 */
import React, { useState, useCallback, useMemo } from 'react';
import {
  Card,
  List,
  Tag,
  Button,
  Space,
  Typography,
  Tooltip,
  Badge,
  Dropdown,
  Input,
  Select,
  Divider,
  Progress,
  Empty,
  Statistic,
  Row,
  Col,
  Alert
} from 'antd';
import type { MenuProps } from 'antd';
import {
  DownloadOutlined,
  FilterOutlined,
  SearchOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  BarChartOutlined,
  EyeOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons';
import type {
  FileComparisonResult,
  DifferenceItem,
  DifferenceSeverity,
  DifferenceType,
  ClauseType
} from '../types/fileCompare';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;

interface DiffNavigatorProps {
  /** 对比结果数据 */
  result: FileComparisonResult | null;
  /** 当前选中的差异项ID */
  selectedDifferenceId?: string | null;
  /** 是否显示统计信息 */
  showStatistics?: boolean;
  /** 是否显示导出功能 */
  showExport?: boolean;
  /** 是否紧凑模式 */
  compact?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 差异项点击回调 */
  onDifferenceClick?: (difference: DifferenceItem) => void;
  /** 导出回调 */
  onExport?: (format: 'pdf' | 'excel' | 'word', options?: ExportOptions) => void;
  /** 过滤变化回调 */
  onFilterChange?: (filters: DiffFilters) => void;
}

interface ExportOptions {
  /** 是否包含统计信息 */
  includeStatistics: boolean;
  /** 是否包含详细描述 */
  includeDetails: boolean;
  /** 是否包含建议 */
  includeSuggestions: boolean;
  /** 过滤的严重程度 */
  severityFilter?: DifferenceSeverity[];
  /** 过滤的类型 */
  typeFilter?: DifferenceType[];
}

interface DiffFilters {
  /** 搜索关键词 */
  searchText: string;
  /** 严重程度过滤 */
  severityFilter: DifferenceSeverity[];
  /** 类型过滤 */
  typeFilter: DifferenceType[];
  /** 条款类型过滤 */
  clauseTypeFilter: ClauseType[];
  /** 排序方式 */
  sortBy: 'severity' | 'type' | 'position' | 'impact';
  /** 排序方向 */
  sortOrder: 'asc' | 'desc';
}

interface NavigatorState {
  /** 当前过滤器 */
  filters: DiffFilters;
  /** 是否显示过滤器 */
  showFilters: boolean;
  /** 导出选项 */
  exportOptions: ExportOptions;
  /** 是否显示导出选项 */
  showExportOptions: boolean;
}

const DiffNavigator: React.FC<DiffNavigatorProps> = ({
  result,
  selectedDifferenceId,
  showStatistics = true,
  showExport = true,
  compact = false,
  className,
  onDifferenceClick,
  onExport,
  onFilterChange
}) => {
  const [state, setState] = useState<NavigatorState>({
    filters: {
      searchText: '',
      severityFilter: [],
      typeFilter: [],
      clauseTypeFilter: [],
      sortBy: 'severity',
      sortOrder: 'desc'
    },
    showFilters: false,
    exportOptions: {
      includeStatistics: true,
      includeDetails: true,
      includeSuggestions: true
    },
    showExportOptions: false
  });

  // 严重程度配置
  const severityConfig = {
    critical: { 
      color: 'red', 
      text: '严重', 
      icon: <ExclamationCircleOutlined />,
      priority: 4 
    },
    major: { 
      color: 'orange', 
      text: '重要', 
      icon: <WarningOutlined />,
      priority: 3 
    },
    minor: { 
      color: 'yellow', 
      text: '轻微', 
      icon: <InfoCircleOutlined />,
      priority: 2 
    },
    info: { 
      color: 'blue', 
      text: '信息', 
      icon: <CheckCircleOutlined />,
      priority: 1 
    }
  };

  // 过滤和排序差异项
  const filteredDifferences = useMemo(() => {
    if (!result?.differences) return [];

    let filtered = result.differences.filter(diff => {
      // 搜索过滤
      if (state.filters.searchText) {
        const searchLower = state.filters.searchText.toLowerCase();
        const matchesSearch = 
          diff.description.toLowerCase().includes(searchLower) ||
          diff.primaryText?.toLowerCase().includes(searchLower) ||
          diff.secondaryText?.toLowerCase().includes(searchLower) ||
          diff.suggestion?.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // 严重程度过滤
      if (state.filters.severityFilter.length > 0) {
        if (!state.filters.severityFilter.includes(diff.severity)) return false;
      }

      // 类型过滤
      if (state.filters.typeFilter.length > 0) {
        if (!state.filters.typeFilter.includes(diff.type)) return false;
      }

      // 条款类型过滤
      if (state.filters.clauseTypeFilter.length > 0 && diff.clauseType) {
        if (!state.filters.clauseTypeFilter.includes(diff.clauseType)) return false;
      }

      return true;
    });

    // 排序
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (state.filters.sortBy) {
        case 'severity':
          const bConfig = severityConfig[b.severity];
          const aConfig = severityConfig[a.severity];
          comparison = (bConfig?.priority || 0) - (aConfig?.priority || 0);
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        case 'position':
          const aPos = a.primaryPosition?.startLine || 0;
          const bPos = b.primaryPosition?.startLine || 0;
          comparison = aPos - bPos;
          break;
        case 'impact':
          const impactOrder = { high: 3, medium: 2, low: 1 };
          comparison = (impactOrder[b.impact] || 0) - (impactOrder[a.impact] || 0);
          break;
      }

      return state.filters.sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [result?.differences, state.filters, severityConfig]);

  // 统计信息
  const statistics = useMemo(() => {
    if (!result?.differences) return null;

    const total = result.differences.length;
    const severityCount = {
      critical: 0,
      major: 0,
      minor: 0,
      info: 0
    };

    const typeCount: Record<string, number> = {};

    result.differences.forEach(diff => {
      severityCount[diff.severity]++;
      typeCount[diff.type] = (typeCount[diff.type] || 0) + 1;
    });

    return {
      total,
      severityCount,
      typeCount,
      filtered: filteredDifferences.length
    };
  }, [result?.differences, filteredDifferences.length]);

  // 处理过滤器变化
  const handleFilterChange = useCallback((newFilters: Partial<DiffFilters>) => {
    const updatedFilters = { ...state.filters, ...newFilters };
    setState(prev => ({ ...prev, filters: updatedFilters }));
    onFilterChange?.(updatedFilters);
  }, [state.filters, onFilterChange]);

  // 处理差异项点击
  const handleDifferenceClick = useCallback((difference: DifferenceItem) => {
    onDifferenceClick?.(difference);
  }, [onDifferenceClick]);

  // 处理导出
  const handleExport = useCallback((format: 'pdf' | 'excel' | 'word') => {
    onExport?.(format, state.exportOptions);
  }, [onExport, state.exportOptions]);

  // 渲染严重程度标签
  const renderSeverityTag = (severity: DifferenceSeverity, showIcon = true) => {
    const config = severityConfig[severity];
    if (!config) {
      // 如果配置不存在，使用默认配置
      return (
        <Tag color="default">
          {severity || '未知'}
        </Tag>
      );
    }
    return (
      <Tag color={config.color} icon={showIcon ? config.icon : undefined}>
        {config.text}
      </Tag>
    );
  };

  // 渲染统计信息
  const renderStatistics = () => {
    if (!showStatistics || !statistics) return null;

    return (
      <Card title="差异统计" size="small" className="mb-4">
        <Row gutter={16} className="mb-4">
          <Col span={12}>
            <Statistic
              title="总差异数"
              value={isNaN(statistics.total) || statistics.total === null || statistics.total === undefined ? 0 : statistics.total}
              prefix={<FileTextOutlined />}
            />
          </Col>
          <Col span={12}>
            <Statistic
              title="当前显示"
              value={isNaN(statistics.filtered) || statistics.filtered === null || statistics.filtered === undefined ? 0 : statistics.filtered}
              prefix={<EyeOutlined />}
            />
          </Col>
        </Row>

        <div className="space-y-3">
          <div>
            <Text type="secondary" className="block mb-2">按严重程度分布</Text>
            <div className="space-y-2">
              {Object.entries(statistics.severityCount).map(([severity, count]) => {
                const config = severityConfig[severity as DifferenceSeverity];
                return (
                  <div key={severity} className="flex justify-between items-center">
                    {renderSeverityTag(severity as DifferenceSeverity, false)}
                    <Badge count={isNaN(count) || count === null || count === undefined ? 0 : count} style={{ backgroundColor: config?.color || '#d9d9d9' }} />
                  </div>
                );
              })}
            </div>
          </div>

          <Divider />

          <div>
            <Text type="secondary" className="block mb-2">按类型分布</Text>
            <div className="space-y-1">
              {Object.entries(statistics.typeCount).map(([type, count]) => (
                <div key={type} className="flex justify-between items-center text-sm">
                  <Text>{type}</Text>
                  <Badge count={isNaN(count) || count === null || count === undefined ? 0 : count} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>
    );
  };

  // 渲染过滤器
  const renderFilters = () => {
    if (!state.showFilters) return null;

    return (
      <Card title="过滤选项" size="small" className="mb-4">
        <div className="space-y-3">
          <div>
            <Text type="secondary" className="block mb-1">搜索</Text>
            <Search
              placeholder="搜索差异描述、内容或建议"
              value={state.filters.searchText}
              onChange={(e) => handleFilterChange({ searchText: e.target.value })}
              allowClear
            />
          </div>

          <div>
            <Text type="secondary" className="block mb-1">严重程度</Text>
            <Select
              mode="multiple"
              placeholder="选择严重程度"
              value={state.filters.severityFilter}
              onChange={(value) => handleFilterChange({ severityFilter: value })}
              className="w-full"
            >
              {Object.entries(severityConfig).map(([severity, config]) => (
                <Option key={severity} value={severity}>
                  <Tag color={config?.color || 'default'} className="mr-1">{config?.text || severity}</Tag>
                </Option>
              ))}
            </Select>
          </div>

          <div>
            <Text type="secondary" className="block mb-1">排序方式</Text>
            <div className="flex space-x-2">
              <Select
                value={state.filters.sortBy}
                onChange={(value) => handleFilterChange({ sortBy: value })}
                className="flex-1"
              >
                <Option value="severity">按严重程度</Option>
                <Option value="type">按类型</Option>
                <Option value="position">按位置</Option>
                <Option value="impact">按影响程度</Option>
              </Select>
              <Button
                icon={state.filters.sortOrder === 'asc' ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                onClick={() => handleFilterChange({ 
                  sortOrder: state.filters.sortOrder === 'asc' ? 'desc' : 'asc' 
                })}
              />
            </div>
          </div>
        </div>
      </Card>
    );
  };

  // 渲染导出选项
  const renderExportMenu = (): MenuProps => ({
    items: [
      {
        key: 'pdf',
        label: 'PDF报告',
        icon: <FileTextOutlined />,
        onClick: () => handleExport('pdf')
      },
      {
        key: 'excel',
        label: 'Excel表格',
        icon: <BarChartOutlined />,
        onClick: () => handleExport('excel')
      },
      {
        key: 'word',
        label: 'Word文档',
        icon: <FileTextOutlined />,
        onClick: () => handleExport('word')
      }
    ]
  });

  // 渲染差异项
  const renderDifferenceItem = (difference: DifferenceItem) => {
    const isSelected = selectedDifferenceId === difference.id;
    
    return (
      <List.Item
        key={difference.id}
        className={`cursor-pointer transition-colors ${
          isSelected ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
        }`}
        onClick={() => handleDifferenceClick(difference)}
      >
        <div className="w-full">
          <div className="flex justify-between items-start mb-2">
            <div className="flex items-center space-x-2">
              {renderSeverityTag(difference.severity)}
              <Tag color="blue">{difference.type}</Tag>
              {difference.clauseType && (
                <Tag color="green">{difference.clauseType}</Tag>
              )}
            </div>
            {difference.primaryPosition && (
              <Text type="secondary" className="text-xs">
                行 {difference.primaryPosition.startLine}
              </Text>
            )}
          </div>
          
          <Paragraph 
            className="mb-2 text-sm" 
            ellipsis={{ rows: compact ? 1 : 2, expandable: !compact }}
          >
            {difference.description}
          </Paragraph>
          
          {difference.suggestion && !compact && (
            <Alert
              message={difference.suggestion}
              type="info"
              showIcon
              className="mt-2"
            />
          )}
        </div>
      </List.Item>
    );
  };

  // 无数据状态
  if (!result) {
    return (
      <div className={`diff-navigator ${className || ''}`}>
        <Empty description="暂无对比结果" />
      </div>
    );
  }

  return (
    <div className={`diff-navigator ${className || ''}`}>
      {/* 工具栏 */}
      <div className="flex justify-between items-center mb-4">
        <Title level={5} className="mb-0">
          差异导航 
          {statistics && (
            <Badge
              count={isNaN(statistics.filtered) || statistics.filtered === null || statistics.filtered === undefined ? 0 : statistics.filtered}
              style={{ backgroundColor: '#1890ff', marginLeft: 8 }}
            />
          )}
        </Title>
        
        <Space>
          <Tooltip title="过滤选项">
            <Button
              type={state.showFilters ? 'primary' : 'default'}
              icon={<FilterOutlined />}
              onClick={() => setState(prev => ({ ...prev, showFilters: !prev.showFilters }))}
            />
          </Tooltip>
          
          {showExport && (
            <Dropdown menu={renderExportMenu()} placement="bottomRight">
              <Button icon={<DownloadOutlined />}>
                导出
              </Button>
            </Dropdown>
          )}
        </Space>
      </div>

      {/* 统计信息 */}
      {renderStatistics()}

      {/* 过滤器 */}
      {renderFilters()}

      {/* 差异列表 */}
      <Card title="差异列表" size="small">
        {filteredDifferences.length === 0 ? (
          <Empty 
            description={
              state.filters.searchText || 
              state.filters.severityFilter.length > 0 || 
              state.filters.typeFilter.length > 0
                ? "没有符合条件的差异项"
                : "暂无差异项"
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <List
            dataSource={filteredDifferences}
            renderItem={renderDifferenceItem}
            size={compact ? 'small' : 'default'}
            pagination={filteredDifferences.length > 10 ? {
              pageSize: compact ? 5 : 10,
              size: 'small',
              showSizeChanger: false,
              showQuickJumper: true
            } : false}
          />
        )}
      </Card>
    </div>
  );
};

export default DiffNavigator;