import React, { useMemo } from 'react';
import { DifferenceItem, SimilarityItem } from '../types/fileCompare';

interface HighlightInfo {
  start: number;
  end: number;
  type: 'difference' | 'similarity';
  id: string;
  severity?: string;
  similarity?: number;
}

interface HtmlContentRendererProps {
  /** HTML内容 */
  htmlContent: string;
  /** 纯文本内容（用于高亮计算） */
  plainTextContent: string;
  /** 高亮信息 */
  highlights: HighlightInfo[];
  /** 是否显示差异 */
  showDifferences: boolean;
  /** 是否显示相似项 */
  showSimilarities: boolean;
  /** 选中的差异ID */
  selectedDifferenceId?: string | null;
  /** 选中的相似项ID */
  selectedSimilarityId?: string | null;
  /** 差异点击回调 */
  onDifferenceClick?: (id: string) => void;
  /** 相似项点击回调 */
  onSimilarityClick?: (id: string) => void;
}

/**
 * HTML内容渲染器 - 双轨制架构
 * 安全地渲染HTML内容并叠加高亮效果
 */
export const HtmlContentRenderer: React.FC<HtmlContentRendererProps> = ({
  htmlContent,
  plainTextContent,
  highlights,
  showDifferences,
  showSimilarities,
  selectedDifferenceId,
  selectedSimilarityId,
  onDifferenceClick,
  onSimilarityClick
}) => {
  
  // 生成带高亮的HTML内容
  const highlightedHtml = useMemo(() => {
    console.log('🎨 [HtmlContentRenderer] 开始生成高亮HTML:', {
      htmlLength: htmlContent.length,
      plainTextLength: plainTextContent.length,
      highlightsCount: highlights.length,
      showDifferences,
      showSimilarities
    });

    if (!htmlContent || highlights.length === 0) {
      return htmlContent;
    }

    // 过滤需要显示的高亮
    const visibleHighlights = highlights.filter(highlight => {
      if (highlight.type === 'difference' && !showDifferences) return false;
      if (highlight.type === 'similarity' && !showSimilarities) return false;
      return true;
    });

    if (visibleHighlights.length === 0) {
      return htmlContent;
    }

    // 简单的高亮实现：在HTML中插入高亮标记
    // 注意：这是一个简化版本，实际应用中需要更复杂的HTML解析和处理
    let result = htmlContent;
    
    // 按位置倒序排列，避免插入标记时位置偏移
    const sortedHighlights = [...visibleHighlights].sort((a, b) => b.start - a.start);
    
    sortedHighlights.forEach(highlight => {
      const className = getHighlightClassName(highlight);
      const isSelected = (highlight.type === 'difference' && highlight.id === selectedDifferenceId) ||
                        (highlight.type === 'similarity' && highlight.id === selectedSimilarityId);
      
      const highlightClass = `${className} ${isSelected ? 'selected' : ''}`;
      const onClick = highlight.type === 'difference' ? 
        `onclick="window.handleDifferenceClick?.('${highlight.id}')"` :
        `onclick="window.handleSimilarityClick?.('${highlight.id}')"`;
      
      // 简单的文本替换高亮（实际应用中需要更精确的HTML处理）
      const textToHighlight = plainTextContent.substring(highlight.start, highlight.end);
      if (textToHighlight && result.includes(textToHighlight)) {
        const highlightedText = `<span class="${highlightClass}" ${onClick} data-highlight-id="${highlight.id}">${textToHighlight}</span>`;
        result = result.replace(textToHighlight, highlightedText);
      }
    });

    console.log('🎨 [HtmlContentRenderer] 高亮HTML生成完成:', {
      originalLength: htmlContent.length,
      resultLength: result.length,
      appliedHighlights: sortedHighlights.length
    });

    return result;
  }, [htmlContent, plainTextContent, highlights, showDifferences, showSimilarities, selectedDifferenceId, selectedSimilarityId]);

  // 设置全局回调函数
  React.useEffect(() => {
    (window as any).handleDifferenceClick = onDifferenceClick;
    (window as any).handleSimilarityClick = onSimilarityClick;
    
    return () => {
      delete (window as any).handleDifferenceClick;
      delete (window as any).handleSimilarityClick;
    };
  }, [onDifferenceClick, onSimilarityClick]);

  return (
    <div 
      className="html-content-renderer"
      dangerouslySetInnerHTML={{ __html: highlightedHtml }}
    />
  );
};

/**
 * 获取高亮样式类名
 */
function getHighlightClassName(highlight: HighlightInfo): string {
  if (highlight.type === 'difference') {
    const severity = highlight.severity || 'medium';
    return `highlight-difference highlight-${severity}`;
  } else {
    const level = highlight.similarity && highlight.similarity > 0.8 ? 'high' : 
                  highlight.similarity && highlight.similarity > 0.6 ? 'medium' : 'low';
    return `highlight-similarity highlight-similarity-${level}`;
  }
}

export default HtmlContentRenderer;
