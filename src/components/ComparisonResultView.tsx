/**
 * 文件对比结果展示组件
 * 实现左右分栏布局、差异高亮显示、同步滚动机制
 * 专为文件对比功能设计，与现有功能完全独立
 */
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import './ComparisonResultView.css';
import {
  Card,
  Row,
  Col,
  Typography,
  Tag,
  Button,
  Tooltip,
  Space,
  Divider,
  Progress,
  Alert,
  Spin,
  Empty,
  Switch,
  Slider,
  Badge
} from 'antd';
import HtmlContentRenderer from './HtmlContentRenderer';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  SyncOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseOutlined
} from '@ant-design/icons';
import type { 
  FileComparisonResult, 
  DifferenceItem, 
  SimilarityItem,
  DifferenceType,
  DifferenceSeverity,
  TextPosition
} from '../types/fileCompare';

const { Title, Text, Paragraph } = Typography;

interface ComparisonResultViewProps {
  /** 对比结果数据 */
  result: FileComparisonResult | null;
  /** 是否正在加载 */
  loading?: boolean;
  /** 错误信息 */
  error?: string;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 差异项点击回调 */
  onDifferenceClick?: (difference: DifferenceItem) => void;
  /** 相似项点击回调 */
  onSimilarityClick?: (similarity: SimilarityItem) => void;
  /** 导出回调 */
  onExport?: (format: 'pdf' | 'excel') => void;
}

interface ViewState {
  /** 是否全屏模式 */
  isFullscreen: boolean;
  /** 是否启用同步滚动 */
  syncScroll: boolean;
  /** 是否显示差异高亮 */
  showDifferences: boolean;
  /** 是否显示相似项高亮 */
  showSimilarities: boolean;
  /** 缩放级别 */
  zoomLevel: number;
  /** 当前选中的差异项 */
  selectedDifference: string | null;
  /** 当前选中的相似项 */
  selectedSimilarity: string | null;
  /** 滚动位置同步状态 */
  scrollSyncing: boolean;
  /** 是否显示详细信息侧边栏 */
  showDetails: boolean;
  /** 对比视图模式 */
  viewMode: 'line-by-line' | 'document' | 'unified';
}

// 双轨制高亮文本接口
interface DualTrackText {
  /** 显示文本（HTML格式） */
  displayText: string;
  /** 对比文本（纯文本格式） */
  compareText: string;
  /** 高亮区域 */
  highlights: Array<{
    start: number;
    end: number;
    type: 'difference' | 'similarity';
    id: string;
    severity?: DifferenceSeverity;
    similarity?: number;
  }>;
  /** 是否为HTML内容 */
  isHtml: boolean;
}

const ComparisonResultView: React.FC<ComparisonResultViewProps> = ({
  result,
  loading = false,
  error,
  showDetails = true,
  className,
  onDifferenceClick,
  onSimilarityClick,
  onExport
}) => {
  const [viewState, setViewState] = useState<ViewState>({
    isFullscreen: false,
    syncScroll: true,
    showDifferences: true,
    showSimilarities: true,
    zoomLevel: 100,
    selectedDifference: null,
    selectedSimilarity: null,
    scrollSyncing: false,
    showDetails: showDetails,
    viewMode: 'line-by-line'
  });

  const primaryContentRef = useRef<HTMLDivElement>(null);
  const secondaryContentRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 处理同步滚动
  const handleScroll = useCallback((source: 'primary' | 'secondary') => {
    if (!viewState.syncScroll || viewState.scrollSyncing) return;

    setViewState(prev => ({ ...prev, scrollSyncing: true }));

    const sourceRef = source === 'primary' ? primaryContentRef : secondaryContentRef;
    const targetRef = source === 'primary' ? secondaryContentRef : primaryContentRef;

    if (sourceRef.current && targetRef.current) {
      const sourceScrollTop = sourceRef.current.scrollTop;
      const sourceScrollHeight = sourceRef.current.scrollHeight;
      const sourceClientHeight = sourceRef.current.clientHeight;
      
      const targetScrollHeight = targetRef.current.scrollHeight;
      const targetClientHeight = targetRef.current.clientHeight;
      
      // 计算滚动比例
      const scrollRatio = sourceScrollTop / (sourceScrollHeight - sourceClientHeight);
      const targetScrollTop = scrollRatio * (targetScrollHeight - targetClientHeight);
      
      targetRef.current.scrollTop = targetScrollTop;
    }

    // 延迟重置同步状态，避免循环触发
    setTimeout(() => {
      setViewState(prev => ({ ...prev, scrollSyncing: false }));
    }, 50);
  }, [viewState.syncScroll, viewState.scrollSyncing]);

  // 处理全屏切换
  const toggleFullscreen = useCallback(() => {
    setViewState(prev => ({ ...prev, isFullscreen: !prev.isFullscreen }));
  }, []);

  // 处理缩放
  const handleZoom = useCallback((delta: number) => {
    setViewState(prev => ({
      ...prev,
      zoomLevel: Math.max(50, Math.min(200, prev.zoomLevel + delta))
    }));
  }, []);

  // 处理差异项点击
  const handleDifferenceClick = useCallback((difference: DifferenceItem) => {
    setViewState(prev => ({ 
      ...prev, 
      selectedDifference: prev.selectedDifference === difference.id ? null : difference.id 
    }));
    onDifferenceClick?.(difference);
  }, [onDifferenceClick]);

  // 处理相似项点击
  const handleSimilarityClick = useCallback((similarity: SimilarityItem) => {
    setViewState(prev => ({ 
      ...prev, 
      selectedSimilarity: prev.selectedSimilarity === similarity.id ? null : similarity.id 
    }));
    onSimilarityClick?.(similarity);
  }, [onSimilarityClick]);

  // 生成高亮文本 - 双轨制架构
  const generateHighlightedText = useMemo(() => {
    console.log('🔍 [ComparisonResultView] generateHighlightedText 开始处理（双轨制）:', {
      hasResult: !!result,
      resultKeys: result ? Object.keys(result) : [],
      timestamp: new Date().toISOString()
    });

    if (!result) {
      console.log('🔍 [ComparisonResultView] 没有result数据，返回null');
      return { primary: null, secondary: null };
    }

    // 详细检查result结构
    console.log('🔍 [ComparisonResultView] 详细result结构检查:', {
      result: result,
      primaryContent: result.primaryContent,
      secondaryContent: result.secondaryContent,
      primaryFile: result.primaryFile,
      secondaryFile: result.secondaryFile,
      differences: result.differences?.length || 0,
      similarities: result.similarities?.length || 0
    });

    // 更详细的内容检查
    console.log('🔍 [ComparisonResultView] 内容详细检查:', {
      primaryContentExists: !!result.primaryContent,
      secondaryContentExists: !!result.secondaryContent,
      primaryContentKeys: result.primaryContent ? Object.keys(result.primaryContent) : [],
      secondaryContentKeys: result.secondaryContent ? Object.keys(result.secondaryContent) : [],
      primaryText: result.primaryContent?.text,
      primaryPlainText: result.primaryContent?.plainText,
      secondaryText: result.secondaryContent?.text,
      secondaryPlainText: result.secondaryContent?.plainText,
      primaryTextType: typeof result.primaryContent?.text,
      primaryPlainTextType: typeof result.primaryContent?.plainText,
      secondaryTextType: typeof result.secondaryContent?.text,
      secondaryPlainTextType: typeof result.secondaryContent?.plainText
    });

    // 双轨制数据提取
    let primaryDisplayText = '';
    let secondaryDisplayText = '';
    let primaryCompareText = '';
    let secondaryCompareText = '';
    let primaryIsHtml = false;
    let secondaryIsHtml = false;

    try {
      console.log('🔍 [ComparisonResultView] 检查双轨制API响应结构:', {
        hasPrimaryContent: !!(result.primaryContent),
        hasSecondaryContent: !!(result.secondaryContent),
        primaryContentKeys: result.primaryContent ? Object.keys(result.primaryContent) : [],
        secondaryContentKeys: result.secondaryContent ? Object.keys(result.secondaryContent) : []
      });

      // 提取双轨制数据（适配用户修改的接口）
      if (result.primaryContent) {
        console.log('🔍 [ComparisonResultView] 主文件原始数据:', {
          primaryContent: result.primaryContent,
          text: result.primaryContent.text,
          plainText: result.primaryContent.plainText,
          textType: typeof result.primaryContent.text,
          plainTextType: typeof result.primaryContent.plainText
        });

        primaryDisplayText = String(result.primaryContent.text || ''); // 显示内容（可能是HTML）
        primaryCompareText = String(result.primaryContent.plainText || primaryDisplayText); // 对比内容（纯文本）

        console.log('🔍 [ComparisonResultView] 主文件数据提取成功:', {
          displayTextLength: primaryDisplayText.length,
          compareTextLength: primaryCompareText.length,
          hasPlainText: !!result.primaryContent.plainText,
          isHtmlContent: primaryDisplayText.includes('<') && primaryDisplayText.includes('>'),
          displayPreview: primaryDisplayText.substring(0, 200),
          comparePreview: primaryCompareText.substring(0, 200)
        });
      } else {
        console.log('🔍 [ComparisonResultView] 没有主文件内容 (result.primaryContent)');
      }

      if (result.secondaryContent) {
        console.log('🔍 [ComparisonResultView] 副文件原始数据:', {
          secondaryContent: result.secondaryContent,
          text: result.secondaryContent.text,
          plainText: result.secondaryContent.plainText,
          textType: typeof result.secondaryContent.text,
          plainTextType: typeof result.secondaryContent.plainText
        });

        secondaryDisplayText = String(result.secondaryContent.text || '');
        secondaryCompareText = String(result.secondaryContent.plainText || secondaryDisplayText);

        console.log('🔍 [ComparisonResultView] 副文件数据提取成功:', {
          displayTextLength: secondaryDisplayText.length,
          compareTextLength: secondaryCompareText.length,
          hasPlainText: !!result.secondaryContent.plainText,
          isHtmlContent: secondaryDisplayText.includes('<') && secondaryDisplayText.includes('>'),
          displayPreview: secondaryDisplayText.substring(0, 200),
          comparePreview: secondaryCompareText.substring(0, 200)
        });
      } else {
        console.log('🔍 [ComparisonResultView] 没有副文件内容 (result.secondaryContent)');
      }

      // 双轨制架构：数据验证和清理
      if (!primaryDisplayText && !primaryCompareText) {
        primaryDisplayText = '暂无主文件内容或内容解析失败。请检查文件格式是否支持。';
        primaryCompareText = primaryDisplayText;
      }
      if (!secondaryDisplayText && !secondaryCompareText) {
        secondaryDisplayText = '暂无副文件内容或内容解析失败。请检查文件格式是否支持。';
        secondaryCompareText = secondaryDisplayText;
      }

      // 数据清理 - 处理NaN值
      primaryDisplayText = String(primaryDisplayText || '').replace(/NaN/g, '数值错误');
      secondaryDisplayText = String(secondaryDisplayText || '').replace(/NaN/g, '数值错误');
      primaryCompareText = String(primaryCompareText || '').replace(/NaN/g, '数值错误');
      secondaryCompareText = String(secondaryCompareText || '').replace(/NaN/g, '数值错误');

      // 双轨制架构：确定渲染策略
      primaryIsHtml = primaryDisplayText.includes('<') && primaryDisplayText.includes('>');
      secondaryIsHtml = secondaryDisplayText.includes('<') && secondaryDisplayText.includes('>');

      console.log('🔍 [ComparisonResultView] 渲染策略决策:', {
        primaryIsHtml,
        secondaryIsHtml,
        primaryDisplayLength: primaryDisplayText.length,
        secondaryDisplayLength: secondaryDisplayText.length,
        primaryCompareLength: primaryCompareText.length,
        secondaryCompareLength: secondaryCompareText.length
      });

    } catch (error) {
      console.error('🔍 [ComparisonResultView] 双轨制数据提取出错:', error);
      // 错误回退
      return { primary: null, secondary: null };
    }

    // 双轨制架构：生成高亮区域
    const primaryHighlights: Array<{
      start: number;
      end: number;
      type: 'difference' | 'similarity';
      id: string;
      severity?: DifferenceSeverity;
      similarity?: number;
    }> = [];

    const secondaryHighlights: Array<{
      start: number;
      end: number;
      type: 'difference' | 'similarity';
      id: string;
      severity?: DifferenceSeverity;
      similarity?: number;
    }> = [];

    console.log('🔍 [ComparisonResultView] 开始生成高亮区域:', {
      differencesCount: result.differences?.length || 0,
      similaritiesCount: result.similarities?.length || 0,
      showDifferences: viewState.showDifferences,
      showSimilarities: viewState.showSimilarities
    });

    // 双轨制架构：使用对比文本进行高亮位置计算
    const primaryTextForHighlight = primaryCompareText;
    const secondaryTextForHighlight = secondaryCompareText;

    // 添加差异高亮
    if (viewState.showDifferences && result.differences) {
      result.differences.forEach(diff => {
        if (diff.position && diff.primaryText) {
          // 使用position信息或者文本搜索
          let start = diff.position.start;
          let end = diff.position.end;

          // 如果position超出范围，尝试文本搜索
          if (start >= primaryTextForHighlight.length) {
            start = primaryTextForHighlight.indexOf(diff.primaryText);
            end = start !== -1 ? start + diff.primaryText.length : 0;
          }

          if (start !== -1 && start < primaryTextForHighlight.length) {
            primaryHighlights.push({
              start,
              end: end,
              type: 'difference',
              id: diff.id,
              severity: diff.severity
            });
          }
        }

        if (diff.secondaryText) {
          // 对副文件进行相同的处理
          let start = diff.position?.start || 0;
          let end = diff.position?.end || 0;

          if (start >= secondaryTextForHighlight.length) {
            start = secondaryTextForHighlight.indexOf(diff.secondaryText);
            end = start !== -1 ? start + diff.secondaryText.length : 0;
          }

          if (start !== -1 && start < secondaryTextForHighlight.length) {
            secondaryHighlights.push({
              start,
              end: end,
              type: 'difference',
              id: diff.id,
              severity: diff.severity
            });
          }
        }
      });
    }

    // 添加相似项高亮
    if (viewState.showSimilarities && result.similarities) {
      result.similarities.forEach(sim => {
        if (sim.text) {
          const primaryStart = primaryTextForHighlight.indexOf(sim.text);
          const secondaryStart = secondaryTextForHighlight.indexOf(sim.text);

          if (primaryStart !== -1) {
            primaryHighlights.push({
              start: primaryStart,
              end: primaryStart + sim.text.length,
              type: 'similarity',
              id: sim.id,
              similarity: sim.similarity
            });
          }

          if (secondaryStart !== -1) {
            secondaryHighlights.push({
              start: secondaryStart,
              end: secondaryStart + sim.text.length,
              type: 'similarity',
              id: sim.id,
              similarity: sim.similarity
            });
          }
        }
      });
    }

    // 双轨制架构：返回完整的双轨制数据
    console.log('🔍 [ComparisonResultView] 双轨制高亮数据生成完成:', {
      primaryDisplayLength: primaryDisplayText.length,
      secondaryDisplayLength: secondaryDisplayText.length,
      primaryCompareLength: primaryCompareText.length,
      secondaryCompareLength: secondaryCompareText.length,
      primaryHighlights: primaryHighlights.length,
      secondaryHighlights: secondaryHighlights.length,
      primaryIsHtml,
      secondaryIsHtml
    });

    const finalResult = {
      primary: {
        displayText: primaryDisplayText,    // HTML格式显示内容
        compareText: primaryCompareText,    // 纯文本对比内容
        highlights: primaryHighlights,
        isHtml: primaryIsHtml
      },
      secondary: {
        displayText: secondaryDisplayText,  // HTML格式显示内容
        compareText: secondaryCompareText,  // 纯文本对比内容
        highlights: secondaryHighlights,
        isHtml: secondaryIsHtml
      }
    };

    console.log('🔍 [ComparisonResultView] 最终返回数据:', finalResult);
    return finalResult;
  }, [
    result?.primaryContent?.text,
    result?.primaryContent?.plainText,
    result?.secondaryContent?.text,
    result?.secondaryContent?.plainText,
    result?.differences,
    result?.similarities,
    viewState.showDifferences,
    viewState.showSimilarities,
    viewState.selectedDifference,
    viewState.selectedSimilarity
  ]);

  // 双轨制渲染函数
  const renderDualTrackContent = useCallback((content: DualTrackText, side: 'primary' | 'secondary') => {
    console.log(`🎨 [ComparisonResultView] 渲染${side}内容:`, {
      isHtml: content.isHtml,
      displayTextLength: content.displayText.length,
      compareTextLength: content.compareText.length,
      highlightsCount: content.highlights.length
    });

    if (content.isHtml) {
      // 使用HTML渲染器
      return (
        <HtmlContentRenderer
          htmlContent={content.displayText}
          plainTextContent={content.compareText}
          highlights={content.highlights}
          showDifferences={viewState.showDifferences}
          showSimilarities={viewState.showSimilarities}
          selectedDifferenceId={viewState.selectedDifference?.id}
          selectedSimilarityId={viewState.selectedSimilarity?.id}
          onDifferenceClick={(id) => {
            const diff = result?.differences?.find(d => d.id === id);
            if (diff) handleDifferenceClick(diff);
          }}
          onSimilarityClick={(id) => {
            const sim = result?.similarities?.find(s => s.id === id);
            if (sim) handleSimilarityClick(sim);
          }}
        />
      );
    } else {
      // 使用传统的文本渲染
      return renderPlainTextWithHighlights(content);
    }
  }, [viewState.showDifferences, viewState.showSimilarities, viewState.selectedDifference, viewState.selectedSimilarity, result]);

  // 纯文本高亮渲染（向后兼容）
  const renderPlainTextWithHighlights = useCallback((content: DualTrackText) => {
    const textToRender = content.displayText; // 对于纯文本，显示内容和对比内容相同

    if (!content.highlights || content.highlights.length === 0) {
      return <pre className="plain-text-content">{textToRender}</pre>;
    }

    // 过滤需要显示的高亮
    const visibleHighlights = content.highlights.filter(highlight => {
      if (highlight.type === 'difference' && !viewState.showDifferences) return false;
      if (highlight.type === 'similarity' && !viewState.showSimilarities) return false;
      return true;
    });

    if (visibleHighlights.length === 0) {
      return <pre className="plain-text-content">{textToRender}</pre>;
    }

    // 按位置排序高亮区域
    const sortedHighlights = [...visibleHighlights].sort((a, b) => a.start - b.start);

    const parts = [];
    let lastIndex = 0;

    sortedHighlights.forEach((highlight, index) => {
      // 添加高亮前的普通文本
      if (highlight.start > lastIndex) {
        parts.push(
          <span key={`text-${index}`}>
            {textToRender.substring(lastIndex, highlight.start)}
          </span>
        );
      }

      // 添加高亮文本
      const highlightText = textToRender.substring(highlight.start, highlight.end);
      const className = highlight.type === 'difference'
        ? `highlight-difference ${highlight.severity ? `highlight-${highlight.severity}` : ''}`
        : `highlight-similarity ${highlight.similarity && highlight.similarity >= 0.8 ? 'highlight-similarity-high' : 'highlight-similarity-medium'}`;

      const isSelected = (highlight.type === 'difference' && highlight.id === viewState.selectedDifference?.id) ||
                        (highlight.type === 'similarity' && highlight.id === viewState.selectedSimilarity?.id);

      parts.push(
        <span
          key={`highlight-${index}`}
          className={`${className} ${isSelected ? 'selected' : ''}`}
          onClick={() => {
            if (highlight.type === 'difference') {
              const diff = result?.differences?.find(d => d.id === highlight.id);
              if (diff) handleDifferenceClick(diff);
            } else {
              const sim = result?.similarities?.find(s => s.id === highlight.id);
              if (sim) handleSimilarityClick(sim);
            }
          }}
          title={highlight.type === 'difference'
            ? `差异 (${highlight.severity || 'medium'})`
            : `相似项 (${highlight.similarity && !isNaN(highlight.similarity) ? (highlight.similarity * 100).toFixed(1) : '0'}%)`
          }
        >
          {highlightText}
        </span>
      );

      lastIndex = highlight.end;
    });

    // 添加最后的普通文本
    if (lastIndex < textToRender.length) {
      parts.push(
        <span key="text-end">
          {textToRender.substring(lastIndex)}
        </span>
      );
    }

    return <pre className="plain-text-content">{parts}</pre>;
  }, [viewState.showDifferences, viewState.showSimilarities, viewState.selectedDifference, viewState.selectedSimilarity, result]);

  // 双轨制架构：不再需要HTML检测，后端已经确定内容类型

  // 渲染差异严重程度标签
  const renderSeverityTag = (severity: DifferenceSeverity) => {
    const severityConfig = {
      critical: { color: 'red', text: '严重' },
      major: { color: 'orange', text: '重要' },
      minor: { color: 'yellow', text: '轻微' },
      info: { color: 'blue', text: '信息' }
    };

    const config = severityConfig[severity];
    if (!config) {
      // 如果配置不存在，使用默认配置
      return <Tag color="default">{severity || '未知'}</Tag>;
    }
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染相似度标签
  const renderSimilarityTag = (similarity: number) => {
    const getColor = (score: number) => {
      if (score >= 90) return 'green';
      if (score >= 70) return 'blue';
      if (score >= 50) return 'orange';
      return 'red';
    };

    const safeSimilarity = isNaN(similarity) || similarity === null || similarity === undefined ? 0 : similarity;

    return (
      <Tag color={getColor(safeSimilarity)}>
        相似度 {safeSimilarity.toFixed(1)}%
      </Tag>
    );
  };

  // 渲染文件信息
  const renderFileInfo = (file: any, title: string) => {
    // 空值检查，防止访问undefined对象的属性
    if (!file) {
      return (
        <Card size="small" title={title} className="mb-4">
          <div className="space-y-2 text-sm">
            <Text type="secondary">文件信息暂不可用</Text>
          </div>
        </Card>
      );
    }

    return (
      <Card size="small" title={title} className="mb-4">
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <Text type="secondary">文件名：</Text>
            <Text>{file.name || '未知'}</Text>
          </div>
          <div className="flex justify-between">
            <Text type="secondary">文件大小：</Text>
            <Text>{file.size ? (isNaN(file.size / 1024) ? '0' : (file.size / 1024).toFixed(1)) + ' KB' : '未知'}</Text>
          </div>
          <div className="flex justify-between">
            <Text type="secondary">文件类型：</Text>
            <Text>{file.mimeType || '未知'}</Text>
          </div>
          {file.pageCount && (
            <div className="flex justify-between">
              <Text type="secondary">页数：</Text>
              <Text>{file.pageCount}</Text>
            </div>
          )}
        </div>
      </Card>
    );
  };

  // 渲染统计信息
  const renderStatistics = () => {
    if (!result?.statistics) return null;

    return (
      <Card title="对比统计" size="small" className="mb-4">
        <div className="space-y-3">
          <div>
            <div className="flex justify-between mb-1">
              <Text type="secondary">整体相似度</Text>
              <Text strong>{(isNaN(result.overallSimilarity) || result.overallSimilarity === null || result.overallSimilarity === undefined ? 0 : result.overallSimilarity).toFixed(1)}%</Text>
            </div>
            <Progress
              percent={isNaN(result.overallSimilarity) || result.overallSimilarity === null || result.overallSimilarity === undefined ? 0 : result.overallSimilarity}
              size="small"
              strokeColor={{
                '0%': '#ff4d4f',
                '50%': '#faad14',
                '100%': '#52c41a',
              }}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="text-center">
              <div className="text-lg font-bold text-red-500">
                {result.differences.length}
              </div>
              <div className="text-gray-500">差异项</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-500">
                {result.similarities.length}
              </div>
              <div className="text-gray-500">相似项</div>
            </div>
          </div>
        </div>
      </Card>
    );
  };

  // 渲染工具栏
  const renderToolbar = () => (
    <div className="comparison-toolbar-content">
      <div className="flex items-center space-x-4">
        <Title level={5} className="mb-0">文件对比结果</Title>
        {result && (
          <Badge 
            count={result.differences.length} 
            style={{ backgroundColor: '#ff4d4f' }}
            title="差异数量"
          />
        )}
      </div>
      
      <div className="toolbar-buttons">
        <div className="view-mode-selector">
          <Tooltip title="逐行对比">
            <Button
              type={viewState.viewMode === 'line-by-line' ? 'primary' : 'default'}
              onClick={() => setViewState(prev => ({ ...prev, viewMode: 'line-by-line' }))}
              className="toolbar-button"
            >
              逐行对比
            </Button>
          </Tooltip>
          <Tooltip title="文档视图">
            <Button
              type={viewState.viewMode === 'document' ? 'primary' : 'default'}
              onClick={() => setViewState(prev => ({ ...prev, viewMode: 'document' }))}
              className="toolbar-button"
            >
              文档视图
            </Button>
          </Tooltip>
        </div>
        
        <Tooltip title={viewState.showDetails ? "隐藏详情" : "显示详情"}>
          <Button
            type={viewState.showDetails ? 'primary' : 'default'}
            icon={<InfoCircleOutlined />}
            onClick={() => setViewState(prev => ({ ...prev, showDetails: !prev.showDetails }))}
            className="toolbar-button"
          />
        </Tooltip>
        
        <Tooltip title="同步滚动">
          <Button
            type={viewState.syncScroll ? 'primary' : 'default'}
            icon={<SyncOutlined />}
            onClick={() => setViewState(prev => ({ ...prev, syncScroll: !prev.syncScroll }))}
            className="toolbar-button"
          />
        </Tooltip>
        
        <Tooltip title="显示差异">
          <Button
            type={viewState.showDifferences ? 'primary' : 'default'}
            icon={<EyeOutlined />}
            onClick={() => setViewState(prev => ({ ...prev, showDifferences: !prev.showDifferences }))}
            className="toolbar-button"
          />
        </Tooltip>
        
        <Tooltip title="显示相似项">
          <Button
            type={viewState.showSimilarities ? 'primary' : 'default'}
            icon={<EyeOutlined />}
            onClick={() => setViewState(prev => ({ ...prev, showSimilarities: !prev.showSimilarities }))}
            className="toolbar-button"
          />
        </Tooltip>
        
        <Tooltip title="缩小">
          <Button
            icon={<ZoomOutOutlined />}
            onClick={() => handleZoom(-10)}
            disabled={viewState.zoomLevel <= 50}
            className="toolbar-button"
          />
        </Tooltip>
        
        <Text className="text-sm">{viewState.zoomLevel}%</Text>
        
        <Tooltip title="放大">
          <Button
            icon={<ZoomInOutlined />}
            onClick={() => handleZoom(10)}
            disabled={viewState.zoomLevel >= 200}
            className="toolbar-button"
          />
        </Tooltip>
        
        <Tooltip title={viewState.isFullscreen ? '退出全屏' : '全屏显示'}>
          <Button
            icon={viewState.isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
            onClick={toggleFullscreen}
            className="toolbar-button"
          />
        </Tooltip>
        
        {onExport && (
          <Button
            icon={<DownloadOutlined />}
            onClick={() => onExport('pdf')}
            className="toolbar-button"
          >
            导出
          </Button>
        )}
      </div>
    </div>
  );

  // 基于现有成功方案的逐行对比实现
  const createLineByLineView = useCallback(() => {
    if (!result) return { primaryLines: [], secondaryLines: [], diffStats: { added: 0, removed: 0, modified: 0 } };

    const highlightedContent = generateHighlightedText;

    console.log('🔍 [逐行对比] 开始创建逐行视图:', {
      hasPrimary: !!highlightedContent.primary,
      hasSecondary: !!highlightedContent.secondary,
      primaryIsHtml: highlightedContent.primary?.isHtml,
      secondaryIsHtml: highlightedContent.secondary?.isHtml
    });

    // 智能分割：保持HTML结构完整性
    const intelligentSplit = (content: DualTrackText): Array<{
      displayContent: string;
      compareContent: string;
      highlights: any[];
      isHtml: boolean;
      type: 'paragraph' | 'table' | 'heading';
    }> => {
      if (!content) return [];

      const { displayText, compareText, highlights, isHtml } = content;

      if (isHtml) {
        // HTML内容：按逻辑块分割，保持表格完整
        const blocks = [];
        let lastIndex = 0;

        // 1. 提取表格块
        const tableRegex = /<table[\s\S]*?<\/table>/gi;
        let match;

        while ((match = tableRegex.exec(displayText)) !== null) {
          // 添加表格前的内容
          if (match.index > lastIndex) {
            const beforeContent = displayText.substring(lastIndex, match.index).trim();
            if (beforeContent) {
              // 按段落分割
              const paragraphs = beforeContent.split(/<\/p>\s*<p[^>]*>/i);
              paragraphs.forEach((para, index) => {
                if (para.trim()) {
                  // 补全p标签
                  let fullPara = para;
                  if (index === 0 && !para.startsWith('<p')) {
                    fullPara = '<p>' + para;
                  }
                  if (index === paragraphs.length - 1 && !para.endsWith('</p>')) {
                    fullPara = fullPara + '</p>';
                  }

                  blocks.push({
                    displayContent: fullPara.trim(),
                    compareContent: para.replace(/<[^>]*>/g, '').trim(),
                    highlights: [], // 稍后计算
                    isHtml: true,
                    type: para.includes('<h') ? 'heading' : 'paragraph'
                  });
                }
              });
            }
          }

          // 添加表格块
          blocks.push({
            displayContent: match[0],
            compareContent: match[0].replace(/<[^>]*>/g, '').trim(),
            highlights: [], // 稍后计算
            isHtml: true,
            type: 'table'
          });

          lastIndex = match.index + match[0].length;
        }

        // 添加剩余内容
        if (lastIndex < displayText.length) {
          const remainingContent = displayText.substring(lastIndex).trim();
          if (remainingContent) {
            const paragraphs = remainingContent.split(/<\/p>\s*<p[^>]*>/i);
            paragraphs.forEach((para, index) => {
              if (para.trim()) {
                let fullPara = para;
                if (index === 0 && !para.startsWith('<p')) {
                  fullPara = '<p>' + para;
                }
                if (index === paragraphs.length - 1 && !para.endsWith('</p>')) {
                  fullPara = fullPara + '</p>';
                }

                blocks.push({
                  displayContent: fullPara.trim(),
                  compareContent: para.replace(/<[^>]*>/g, '').trim(),
                  highlights: [], // 稍后计算
                  isHtml: true,
                  type: para.includes('<h') ? 'heading' : 'paragraph'
                });
              }
            });
          }
        }

        // 如果没有找到任何块，将整个内容作为一个块
        if (blocks.length === 0) {
          blocks.push({
            displayContent: displayText,
            compareContent: compareText,
            highlights: highlights || [],
            isHtml: true,
            type: 'paragraph'
          });
        }

        return blocks;
      } else {
        // 纯文本内容：按段落分割
        const paragraphs = displayText.split(/\n\s*\n/).filter(p => p.trim());
        return paragraphs.map(para => ({
          displayContent: para.trim(),
          compareContent: para.trim(),
          highlights: highlights || [],
          isHtml: false,
          type: 'paragraph' as const
        }));
      }
    };

    const primaryLines = intelligentSplit(highlightedContent.primary);
    const secondaryLines = intelligentSplit(highlightedContent.secondary);

    console.log('🔍 [逐行对比] 分割完成:', {
      primaryLines: primaryLines.length,
      secondaryLines: secondaryLines.length,
      primaryTables: primaryLines.filter(l => l.type === 'table').length,
      secondaryTables: secondaryLines.filter(l => l.type === 'table').length
    });

    // 计算差异统计
    const maxLines = Math.max(primaryLines.length, secondaryLines.length);
    let added = 0, removed = 0, modified = 0;

    for (let i = 0; i < maxLines; i++) {
      const primaryLine = primaryLines[i];
      const secondaryLine = secondaryLines[i];

      if (!primaryLine && secondaryLine) {
        added++;
      } else if (primaryLine && !secondaryLine) {
        removed++;
      } else if (primaryLine && secondaryLine && primaryLine.compareContent !== secondaryLine.compareContent) {
        modified++;
      }
    }

    return {
      primaryLines,
      secondaryLines,
      diffStats: { added, removed, modified }
    };
  }, [result, generateHighlightedText]);

  // 简化的逐行文档处理系统
  const processDocumentLines = useMemo(() => {
    return createLineByLineView();
  }, [createLineByLineView]);

  // 基于现有成功方案的逐行渲染系统
  const renderLineByLineContent = (
    lines: any[],
    title: string,
    isSecondary = false,
    stats: any
  ) => {
    const fileInfo = isSecondary
      ? (result?.files?.secondary || result?.secondaryFile)
      : (result?.files?.primary || result?.primaryFile);

    return (
      <div className="comparison-pane">
        <div className="comparison-header">
          <div>
            <div className="file-name">{title}</div>
            <div className="file-info">
              {fileInfo?.size ? `${(isNaN(fileInfo.size / 1024) || fileInfo.size === null || fileInfo.size === undefined ? '0' : (fileInfo.size / 1024).toFixed(1))} KB` : ''} • {lines.length} 段
            </div>
          </div>
          <div className="diff-stats">
            <div className="diff-stat diff-stat-added">
              +{stats.added}
            </div>
            <div className="diff-stat diff-stat-removed">
              -{stats.removed}
            </div>
            <div className="diff-stat diff-stat-modified">
              ~{stats.modified}
            </div>
          </div>
        </div>

        <div
          className="comparison-content"
          ref={isSecondary ? secondaryContentRef : primaryContentRef}
          onScroll={() => handleScroll(isSecondary ? 'secondary' : 'primary')}
        >
          <div className="line-container">
            <div className="line-numbers">
              {lines.map((_, index) => (
                <span key={index} className="line-number">
                  {index + 1}
                </span>
              ))}
            </div>
            <div className="line-content">
              {lines.map((line, index) => {
                // 确定对应的行
                const otherLines = isSecondary ? processDocumentLines.primaryLines : processDocumentLines.secondaryLines;
                const otherLine = otherLines[index];

                // 确定差异类型
                let lineClass = 'content-line';
                if (!line && otherLine) {
                  lineClass += ' line-removed'; // 当前侧没有，对方有 = 删除
                } else if (line && !otherLine) {
                  lineClass += ' line-added'; // 当前侧有，对方没有 = 新增
                } else if (line && otherLine && line.compareContent !== otherLine.compareContent) {
                  lineClass += ' line-modified'; // 都有但内容不同 = 修改
                } else {
                  lineClass += ' line-context'; // 相同或都没有
                }

                // 如果当前行不存在，显示空白占位
                if (!line) {
                  return (
                    <div key={index} className="content-line line-empty">
                      <div className="empty-line-placeholder">
                        (此处内容已删除)
                      </div>
                    </div>
                  );
                }

                // 创建DualTrackText对象来复用现有渲染逻辑
                const dualTrackContent: DualTrackText = {
                  displayText: line.displayContent,
                  compareText: line.compareContent,
                  highlights: line.highlights || [],
                  isHtml: line.isHtml
                };

                return (
                  <div key={index} className={lineClass}>
                    <div className="line-type-indicator">
                      {line.type === 'table' && '📊'}
                      {line.type === 'heading' && '📝'}
                      {line.type === 'paragraph' && '📄'}
                    </div>
                    <div className="line-content-wrapper">
                      {/* 复用现有的成功渲染方案 */}
                      {renderDualTrackContent(dualTrackContent, isSecondary ? 'secondary' : 'primary')}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 同步滚动指示器 */}
          <div className={`sync-indicator ${viewState.scrollSyncing ? 'active' : ''}`} />
        </div>
      </div>
    );
  };

  // 双轨制架构：旧的renderDocumentContent函数已被新的双轨制渲染逻辑替代

  // 加载状态
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-96 space-y-4">
        <Spin size="large" />
        <div className="text-center">
          <Text className="text-lg">正在分析文件对比结果...</Text>
          <div className="text-sm text-gray-500 mt-2">
            请稍候，这可能需要一些时间
          </div>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Alert
        message="对比结果加载失败"
        description={error}
        type="error"
        showIcon
        className="m-4"
      />
    );
  }

  // 无结果状态
  if (!result) {
    return (
      <Empty
        description="暂无对比结果"
        className="m-8"
      />
    );
  }

  const highlightedContent = generateHighlightedText;

  return (
    <div 
      ref={containerRef}
      className={`comparison-result-view ${className || ''} ${viewState.isFullscreen ? 'fullscreen' : ''}`}
    >
      {/* 工具栏 */}
      <div className="comparison-toolbar">
        {renderToolbar()}
      </div>
      
      <div className="flex h-full">
        {/* 侧边栏 - 统计和文件信息 */}
        {viewState.showDetails && (
          <div className="w-64 comparison-sidebar">
            {/* 侧边栏头部 */}
            <div className="comparison-sidebar-header">
              <div className="flex justify-between items-center">
                <Text strong className="text-sm">对比详情</Text>
                <Button 
                  type="text" 
                  size="small"
                  icon={<CloseOutlined />}
                  onClick={() => setViewState(prev => ({ ...prev, showDetails: false }))}
                  className="text-gray-400 hover:text-gray-600"
                />
              </div>
            </div>
            
            {/* 侧边栏内容 */}
            <div className="comparison-sidebar-content space-y-3">
              {/* 简化的统计信息 */}
              <div className="sidebar-section">
                <h3 className="sidebar-section-title">对比统计</h3>
                <div className="sidebar-stats">
                  <div className="stat-item">
                    <span>相似度:</span>
                    <span className="stat-value text-green-600">
                      {(isNaN(result.overallSimilarity) || result.overallSimilarity === null || result.overallSimilarity === undefined ? 0 : result.overallSimilarity).toFixed(1)}%
                    </span>
                  </div>
                  <div className="stat-item">
                    <span>差异数:</span>
                    <span className="stat-value text-red-600">
                      {result.differences.length}
                    </span>
                  </div>
                  <div className="stat-item">
                    <span>相似项:</span>
                    <span className="stat-value text-blue-600">
                      {result.similarities.length}
                    </span>
                  </div>
                </div>
              </div>

              {/* 文件信息 */}
              <div className="sidebar-section">
                <h3 className="sidebar-section-title">文件信息</h3>
                <div className="file-info-list">
                  <div className="file-info-item">
                    <div className="file-name primary" title={result.files?.primary?.name || result.primaryFile?.name}>
                      {result.files?.primary?.name || result.primaryFile?.name || '主文件'}
                    </div>
                    <div className="file-size">
                      {(isNaN((result.files?.primary?.size || result.primaryFile?.size || 0) / 1024) ? 0 : ((result.files?.primary?.size || result.primaryFile?.size || 0) / 1024)).toFixed(1)} KB
                    </div>
                  </div>
                  <div className="file-info-item">
                    <div className="file-name secondary" title={result.files?.secondary?.name || result.secondaryFile?.name}>
                      {result.files?.secondary?.name || result.secondaryFile?.name || '副文件'}
                    </div>
                    <div className="file-size">
                      {(isNaN((result.files?.secondary?.size || result.secondaryFile?.size || 0) / 1024) ? 0 : ((result.files?.secondary?.size || result.secondaryFile?.size || 0) / 1024)).toFixed(1)} KB
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 差异列表 */}
              <div className="sidebar-section">
                <h3 className="sidebar-section-title">主要差异</h3>
                <div className="differences-list">
                  {result.differences.slice(0, 10).map((diff) => (
                    <div
                      key={diff.id}
                      className={`difference-item ${
                        viewState.selectedDifference === diff.id ? 'selected' : ''
                      }`}
                      onClick={() => handleDifferenceClick(diff)}
                    >
                      <div className="difference-header">
                        {renderSeverityTag(diff.severity)}
                        <Text type="secondary" className="difference-type">
                          {diff.type}
                        </Text>
                      </div>
                      <Text className="difference-description">
                        {diff.description.length > 60 ? diff.description.substring(0, 60) + '...' : diff.description}
                      </Text>
                    </div>
                  ))}
                  {result.differences.length > 10 && (
                    <div className="differences-more">
                      <Text type="secondary">
                        还有 {result.differences.length - 10} 个差异...
                      </Text>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* 主要内容区域 - 左右分栏对比 */}
        <div className="document-preview-area">
          {viewState.viewMode === 'line-by-line' ? (
            /* 逐行对比视图 - 复用现有成功方案 */
            <div className="comparison-container">
              {renderLineByLineContent(
                processDocumentLines.primaryLines,
                (result.files?.primary?.name || result.primaryFile?.name || '主文件'),
                false,
                processDocumentLines.diffStats
              )}
              {renderLineByLineContent(
                processDocumentLines.secondaryLines,
                (result.files?.secondary?.name || result.secondaryFile?.name || '副文件'),
                true,
                processDocumentLines.diffStats
              )}
            </div>
          ) : (
            /* 双轨制文档视图 */
            <div className="document-preview-panels">
              <div className="document-preview-panel">
                <div className="document-content-container">
                  <div className="document-header">
                    <Text strong className="text-base">
                      {result.primaryFile?.name || '主文件'}
                    </Text>
                    <div className="document-stats">
                      <span>{Math.round((highlightedContent.primary?.displayText?.length || 0) / 1000)}k 字符</span>
                      <span>•</span>
                      <span>{highlightedContent.primary?.isHtml ? 'HTML' : '文本'}</span>
                    </div>
                  </div>
                  <div
                    ref={primaryContentRef}
                    className="document-content"
                    style={{ fontSize: `${viewState.zoomLevel}%` }}
                    onScroll={() => handleScroll('primary')}
                  >
                    <div className="p-6">
                      {(() => {
                        console.log('🎨 [ComparisonResultView] 主文件渲染检查:', {
                          hasHighlightedContent: !!highlightedContent,
                          hasPrimary: !!highlightedContent?.primary,
                          primaryData: highlightedContent?.primary
                        });

                        return highlightedContent.primary ?
                          renderDualTrackContent(highlightedContent.primary, 'primary') :
                          <Empty description="暂无主文件内容" />;
                      })()}
                    </div>
                  </div>
                </div>
              </div>
              <div className="document-preview-panel">
                <div className="document-content-container">
                  <div className="document-header">
                    <Text strong className="text-base">
                      {result.secondaryFile?.name || '副文件'}
                    </Text>
                    <div className="document-stats">
                      <span>{Math.round((highlightedContent.secondary?.displayText?.length || 0) / 1000)}k 字符</span>
                      <span>•</span>
                      <span>{highlightedContent.secondary?.isHtml ? 'HTML' : '文本'}</span>
                    </div>
                  </div>
                  <div
                    ref={secondaryContentRef}
                    className="document-content"
                    style={{ fontSize: `${viewState.zoomLevel}%` }}
                    onScroll={() => handleScroll('secondary')}
                  >
                    <div className="p-6">
                      {(() => {
                        console.log('🎨 [ComparisonResultView] 副文件渲染检查:', {
                          hasHighlightedContent: !!highlightedContent,
                          hasSecondary: !!highlightedContent?.secondary,
                          secondaryData: highlightedContent?.secondary
                        });

                        return highlightedContent.secondary ?
                          renderDualTrackContent(highlightedContent.secondary, 'secondary') :
                          <Empty description="暂无副文件内容" />;
                      })()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ComparisonResultView;