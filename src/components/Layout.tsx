import React, { useState, ReactNode } from 'react';
import { Layout as AntLayout, Menu, Avatar, Dropdown, <PERSON><PERSON>, <PERSON>ge, Drawer } from 'antd';
import {
  HomeOutlined,
  FileTextOutlined,
  SearchOutlined,
  SwapOutlined,
  EditOutlined,
  BookOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
  BellOutlined,
  MenuOutlined,
  MoonOutlined,
  SunOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import { useNavigation } from '../contexts/NavigationContext';
import HeaderNavigation from './HeaderNavigation';
import SideNavigation from './SideNavigation';
import NavigationModeToggle from './NavigationModeToggle';

const { Header, Sider, Content } = AntLayout;

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { theme, toggleTheme, colors } = useTheme();
  const { navigationMode, isMobile } = useNavigation();

  // 菜单项配置
  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/contracts',
      icon: <FileTextOutlined />,
      label: '合同管理',
    },
    {
      key: '/review',
      icon: <SearchOutlined />,
      label: '智能审查',
    },
    {
      key: '/compare',
      icon: <SwapOutlined />,
      label: '条款对比',
    },
    {
      key: '/draft',
      icon: <EditOutlined />,
      label: '合同起草',
    },
    {
      key: '/knowledge',
      icon: <BookOutlined />,
      label: '知识库',
    },
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => navigate('/settings'),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
    setMobileMenuVisible(false);
  };

  const sidebarContent = (
    <Menu
      theme={theme === 'dark' ? 'dark' : 'light'}
      mode="inline"
      selectedKeys={[location.pathname]}
      items={menuItems}
      onClick={handleMenuClick}
      style={{
        backgroundColor: theme === 'dark' ? colors.surface : colors.surface,
        borderRight: 'none'
      }}
    />
  );

  // 根据导航模式渲染不同的布局
  if (navigationMode === 'header') {
    // 顶部导航模式
    return (
      <AntLayout style={{ minHeight: '100vh', backgroundColor: colors.background }}>
        {/* 移动端抽屉菜单 */}
        <Drawer
          title="导航菜单"
          placement="left"
          onClose={() => setMobileMenuVisible(false)}
          open={mobileMenuVisible}
          bodyStyle={{ padding: 0 }}
          width={250}
        >
          <Menu
            theme={theme === 'dark' ? 'dark' : 'light'}
            mode="inline"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ height: '100%', borderRight: 0 }}
          />
        </Drawer>

        {/* 顶部导航栏 */}
         <Header style={{ 
           padding: '0 16px', 
           background: colors.surface,
           display: 'flex',
           alignItems: 'center',
           justifyContent: 'space-between',
           borderBottom: `1px solid ${colors.border}`,
           height: '64px'
         }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            flex: 1,
            minWidth: 0,
            overflow: 'visible'
          }}>
            {/* 移动端菜单按钮 */}
            {isMobile && (
              <Button
                type="text"
                icon={<MenuOutlined />}
                onClick={() => setMobileMenuVisible(true)}
                style={{ marginRight: 16, color: colors.text }}
              />
            )}
            
            {/* Logo */}
            <h1 style={{ 
              margin: 0, 
              color: colors.text, 
              fontSize: '16px',
              fontWeight: 'bold',
              marginRight: '24px',
              whiteSpace: 'nowrap',
              minWidth: 'fit-content',
              flexShrink: 0
            }}>
              合同助手
            </h1>

            {/* 顶部导航菜单 - 仅在非移动端显示 */}
            {!isMobile && (
              <div style={{ 
                flex: 1, 
                minWidth: 0,
                display: 'flex',
                alignItems: 'center',
                overflow: 'visible',
                width: '100%'
              }}>
                <HeaderNavigation />
              </div>
            )}
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* 导航模式切换按钮 */}
            <NavigationModeToggle />
            
            {/* 主题切换按钮 */}
            <Button
              type="text"
              icon={theme === 'dark' ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleTheme}
              style={{ color: colors.text }}
            />
            
            {/* 通知铃铛 */}
            <Badge count={3} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ color: colors.text }}
              />
            </Badge>

            {/* 用户菜单 */}
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '6px',
                transition: 'background-color 0.2s'
              }}>
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />} 
                  style={{ marginRight: 8 }}
                />
                <span style={{ color: colors.text }}>
                  {user?.username || '用户'}
                </span>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 主内容区域 */}
         <Content style={{ 
           margin: '16px',
           padding: '24px',
           background: colors.surface,
           borderRadius: '8px',
           minHeight: 'calc(100vh - 96px)',
           overflow: 'auto'
         }}>
          {children}
        </Content>
      </AntLayout>
    );
  }

  // 侧边栏导航模式
  return (
    <AntLayout style={{ minHeight: '100vh', backgroundColor: colors.background }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={250}
        style={{
          background: colors.surface,
          borderRight: `1px solid ${colors.border}`,
        }}
        breakpoint="lg"
        collapsedWidth={isMobile ? 0 : 80}
        onBreakpoint={(broken) => {
          if (broken && !collapsed) {
            setCollapsed(true);
          }
        }}
      >
        {/* 侧边栏Logo */}
        <div style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start',
          padding: collapsed ? '0' : '0 16px',
          borderBottom: `1px solid ${colors.border}`,
          transition: 'all 0.2s'
        }}>
          <h1 style={{
            margin: 0,
            color: colors.text,
            fontSize: collapsed ? '14px' : '16px',
            fontWeight: 'bold',
            transition: 'all 0.2s',
            overflow: 'hidden',
            whiteSpace: 'nowrap'
          }}>
            {collapsed ? '合同' : '合同助手'}
          </h1>
        </div>

        {/* 侧边导航菜单 */}
        <SideNavigation />
      </Sider>

      {/* 右侧布局 */}
      <AntLayout>
        {/* 顶部Header */}
        <Header style={{
          padding: '0 16px',
          background: colors.surface,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: `1px solid ${colors.border}`,
          height: '64px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {/* 折叠按钮 */}
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ marginRight: 16, color: colors.text }}
            />
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            {/* 导航模式切换按钮 */}
            <NavigationModeToggle />
            
            {/* 主题切换按钮 */}
            <Button
              type="text"
              icon={theme === 'dark' ? <SunOutlined /> : <MoonOutlined />}
              onClick={toggleTheme}
              style={{ color: colors.text }}
            />
            
            {/* 通知铃铛 */}
            <Badge count={3} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ color: colors.text }}
              />
            </Badge>

            {/* 用户菜单 */}
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
              <div style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '6px',
                transition: 'background-color 0.2s'
              }}>
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  style={{ marginRight: 8 }}
                />
                <span style={{ color: colors.text }}>
                  {user?.username || '用户'}
                </span>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 主内容区域 */}
        <Content style={{
          margin: '16px',
          padding: '24px',
          background: colors.surface,
          borderRadius: '8px',
          minHeight: 'calc(100vh - 96px)',
          overflow: 'auto'
        }}>
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;