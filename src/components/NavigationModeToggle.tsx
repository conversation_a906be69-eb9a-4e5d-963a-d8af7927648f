import React from 'react';
import { But<PERSON>, Tooltip } from 'antd';
import { MenuOutlined, AppstoreOutlined } from '@ant-design/icons';
import { useNavigation } from '../contexts/NavigationContext';
import { useTheme } from '../contexts/ThemeContext';

const NavigationModeToggle: React.FC = () => {
  const { navigationMode, toggleNavigationMode, isMobile } = useNavigation();
  const { colors } = useTheme();

  // 移动端不显示切换按钮
  if (isMobile) {
    return null;
  }

  const isSidebarMode = navigationMode === 'sidebar';
  const tooltipTitle = isSidebarMode ? '切换到顶部导航' : '切换到侧边导航';
  const icon = isSidebarMode ? <AppstoreOutlined /> : <MenuOutlined />;

  return (
    <Tooltip title={tooltipTitle} placement="bottom">
      <Button
        type="text"
        icon={icon}
        onClick={toggleNavigationMode}
        style={{ 
          color: colors.text,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
        className="navigation-mode-toggle"
      />
    </Tooltip>
  );
};

export default NavigationModeToggle;