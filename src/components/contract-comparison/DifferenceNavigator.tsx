/**
 * 差异导航组件
 * 
 * 实现差异列表侧边栏、差异筛选功能、快速定位功能
 */

import React, { useState, useMemo, useCallback } from 'react';
import {
  Card,
  List,
  Typography,
  Button,
  Space,
  Select,
  Input,
  Badge,
  Tag,
  Tooltip,
  Divider,
  Empty,
  Checkbox,
  Row,
  Col
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EyeOutlined,
  ClearOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useContractComparisonStore, useFilteredDifferences } from '../../stores/contractComparisonStore';
import type {
  DifferenceItem,
  DifferenceType,
  DifferenceSeverity
} from '../../types/contractComparison';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Search } = Input;

/**
 * 差异导航组件属性
 */
interface DifferenceNavigatorProps {
  /** 自定义样式类名 */
  className?: string;
  /** 是否显示筛选器 */
  showFilters?: boolean;
  /** 是否显示统计信息 */
  showStatistics?: boolean;
}

/**
 * 差异类型配置
 */
const DIFFERENCE_TYPE_CONFIG = {
  added: { label: '新增', color: 'green', icon: '➕' },
  deleted: { label: '删除', color: 'red', icon: '➖' },
  modified: { label: '修改', color: 'orange', icon: '✏️' },
  moved: { label: '移动', color: 'blue', icon: '🔄' }
};

/**
 * 差异严重程度配置
 */
const SEVERITY_CONFIG = {
  high: { label: '高', color: 'red' },
  medium: { label: '中', color: 'orange' },
  low: { label: '低', color: 'green' }
};

/**
 * 差异导航组件
 */
const DifferenceNavigator: React.FC<DifferenceNavigatorProps> = ({
  className = '',
  showFilters = true,
  showStatistics = true
}) => {
  const {
    comparisonResult,
    selectedDifference,
    setSelectedDifference,
    differenceFilter,
    setDifferenceFilter
  } = useContractComparisonStore();

  const filteredDifferences = useFilteredDifferences();

  // 本地状态
  const [searchText, setSearchText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  /**
   * 搜索过滤后的差异列表
   */
  const searchFilteredDifferences = useMemo(() => {
    if (!searchText.trim()) {
      return filteredDifferences;
    }

    const searchLower = searchText.toLowerCase();
    return filteredDifferences.filter(diff => 
      diff.description.toLowerCase().includes(searchLower) ||
      diff.primaryContent?.toLowerCase().includes(searchLower) ||
      diff.secondaryContent?.toLowerCase().includes(searchLower)
    );
  }, [filteredDifferences, searchText]);

  /**
   * 差异统计信息
   */
  const statistics = useMemo(() => {
    if (!comparisonResult) return null;

    const stats = comparisonResult.statistics;
    return {
      total: stats.totalDifferences,
      added: stats.addedCount,
      deleted: stats.deletedCount,
      modified: stats.modifiedCount,
      moved: stats.movedCount,
      filtered: searchFilteredDifferences.length
    };
  }, [comparisonResult, searchFilteredDifferences]);

  /**
   * 处理差异选择
   */
  const handleDifferenceSelect = useCallback((difference: DifferenceItem, index: number) => {
    setSelectedDifference(difference);
    setCurrentIndex(index);
  }, [setSelectedDifference]);

  /**
   * 导航到上一个差异
   */
  const navigateToPrevious = useCallback(() => {
    if (searchFilteredDifferences.length === 0) return;
    
    const newIndex = currentIndex > 0 ? currentIndex - 1 : searchFilteredDifferences.length - 1;
    const difference = searchFilteredDifferences[newIndex];
    handleDifferenceSelect(difference, newIndex);
  }, [searchFilteredDifferences, currentIndex, handleDifferenceSelect]);

  /**
   * 导航到下一个差异
   */
  const navigateToNext = useCallback(() => {
    if (searchFilteredDifferences.length === 0) return;
    
    const newIndex = currentIndex < searchFilteredDifferences.length - 1 ? currentIndex + 1 : 0;
    const difference = searchFilteredDifferences[newIndex];
    handleDifferenceSelect(difference, newIndex);
  }, [searchFilteredDifferences, currentIndex, handleDifferenceSelect]);

  /**
   * 处理类型筛选
   */
  const handleTypeFilterChange = useCallback((types: string[]) => {
    setDifferenceFilter({
      ...differenceFilter,
      types
    });
  }, [differenceFilter, setDifferenceFilter]);

  /**
   * 处理严重程度筛选
   */
  const handleSeverityFilterChange = useCallback((severities: string[]) => {
    setDifferenceFilter({
      ...differenceFilter,
      severities
    });
  }, [differenceFilter, setDifferenceFilter]);

  /**
   * 清除所有筛选
   */
  const clearAllFilters = useCallback(() => {
    setDifferenceFilter({ types: [], severities: [] });
    setSearchText('');
  }, [setDifferenceFilter]);

  /**
   * 渲染差异项
   */
  const renderDifferenceItem = useCallback((difference: DifferenceItem, index: number) => {
    const isSelected = selectedDifference?.id === difference.id;
    const typeConfig = DIFFERENCE_TYPE_CONFIG[difference.type];
    const severityConfig = SEVERITY_CONFIG[difference.severity];

    return (
      <List.Item
        key={difference.id}
        className={`
          difference-item cursor-pointer transition-all duration-200
          ${isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50'}
        `}
        onClick={() => handleDifferenceSelect(difference, index)}
      >
        <div className="w-full">
          {/* 差异头部 */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <span className="text-lg">{typeConfig.icon}</span>
              <Tag color={typeConfig.color} size="small">
                {typeConfig.label}
              </Tag>
              <Tag color={severityConfig.color} size="small">
                {severityConfig.label}
              </Tag>
            </div>
            
            <Text type="secondary" className="text-xs">
              #{index + 1}
            </Text>
          </div>

          {/* 差异描述 */}
          <Paragraph 
            className="mb-2 text-sm"
            ellipsis={{ rows: 2, tooltip: difference.description }}
          >
            {difference.description}
          </Paragraph>

          {/* 差异内容预览 */}
          {(difference.primaryContent || difference.secondaryContent) && (
            <div className="text-xs bg-gray-50 p-2 rounded border-l-2 border-gray-300">
              {difference.primaryContent && (
                <div className="mb-1">
                  <Text type="secondary">原文: </Text>
                  <Text className="font-mono">
                    {difference.primaryContent.length > 50 
                      ? `${difference.primaryContent.substring(0, 50)}...`
                      : difference.primaryContent
                    }
                  </Text>
                </div>
              )}
              {difference.secondaryContent && (
                <div>
                  <Text type="secondary">新文: </Text>
                  <Text className="font-mono">
                    {difference.secondaryContent.length > 50 
                      ? `${difference.secondaryContent.substring(0, 50)}...`
                      : difference.secondaryContent
                    }
                  </Text>
                </div>
              )}
            </div>
          )}

          {/* 位置信息 */}
          <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
            <div className="flex space-x-4">
              {difference.primaryPosition && (
                <span>主文档: 第{difference.primaryPosition.lineNumber}行</span>
              )}
              {difference.secondaryPosition && (
                <span>副文档: 第{difference.secondaryPosition.lineNumber}行</span>
              )}
            </div>
          </div>
        </div>
      </List.Item>
    );
  }, [selectedDifference, handleDifferenceSelect]);

  if (!comparisonResult) {
    return (
      <Card className={`difference-navigator ${className}`}>
        <Empty 
          description="暂无对比结果"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <Card 
      className={`difference-navigator ${className}`}
      title={
        <div className="flex items-center justify-between">
          <span>差异导航</span>
          {statistics && (
            <Badge count={statistics.filtered} showZero>
              <InfoCircleOutlined className="text-gray-400" />
            </Badge>
          )}
        </div>
      }
      size="small"
    >
      {/* 统计信息 */}
      {showStatistics && statistics && (
        <div className="mb-4 p-3 bg-gray-50 rounded">
          <Row gutter={8}>
            <Col span={12}>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">{statistics.total}</div>
                <div className="text-xs text-gray-500">总差异</div>
              </div>
            </Col>
            <Col span={12}>
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">{statistics.filtered}</div>
                <div className="text-xs text-gray-500">已筛选</div>
              </div>
            </Col>
          </Row>
          
          <Divider className="my-2" />
          
          <Row gutter={8}>
            <Col span={6}>
              <div className="text-center">
                <div className="text-sm font-medium text-green-600">{statistics.added}</div>
                <div className="text-xs text-gray-500">新增</div>
              </div>
            </Col>
            <Col span={6}>
              <div className="text-center">
                <div className="text-sm font-medium text-red-600">{statistics.deleted}</div>
                <div className="text-xs text-gray-500">删除</div>
              </div>
            </Col>
            <Col span={6}>
              <div className="text-center">
                <div className="text-sm font-medium text-orange-600">{statistics.modified}</div>
                <div className="text-xs text-gray-500">修改</div>
              </div>
            </Col>
            <Col span={6}>
              <div className="text-center">
                <div className="text-sm font-medium text-blue-600">{statistics.moved}</div>
                <div className="text-xs text-gray-500">移动</div>
              </div>
            </Col>
          </Row>
        </div>
      )}

      {/* 搜索和导航 */}
      <div className="mb-4">
        <Search
          placeholder="搜索差异内容..."
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          className="mb-3"
          allowClear
        />
        
        <div className="flex items-center justify-between">
          <Space>
            <Button
              size="small"
              icon={<ArrowUpOutlined />}
              onClick={navigateToPrevious}
              disabled={searchFilteredDifferences.length === 0}
              title="上一个差异"
            />
            <Button
              size="small"
              icon={<ArrowDownOutlined />}
              onClick={navigateToNext}
              disabled={searchFilteredDifferences.length === 0}
              title="下一个差异"
            />
            
            {searchFilteredDifferences.length > 0 && (
              <Text type="secondary" className="text-xs">
                {currentIndex + 1} / {searchFilteredDifferences.length}
              </Text>
            )}
          </Space>
          
          {showFilters && (
            <Button
              size="small"
              icon={<FilterOutlined />}
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              type={showAdvancedFilters ? 'primary' : 'default'}
            >
              筛选
            </Button>
          )}
        </div>
      </div>

      {/* 高级筛选 */}
      {showFilters && showAdvancedFilters && (
        <div className="mb-4 p-3 border border-gray-200 rounded">
          <div className="mb-3">
            <Text strong className="text-xs">差异类型:</Text>
            <Checkbox.Group
              value={differenceFilter.types}
              onChange={handleTypeFilterChange}
              className="mt-1"
            >
              <Row>
                {Object.entries(DIFFERENCE_TYPE_CONFIG).map(([type, config]) => (
                  <Col span={12} key={type}>
                    <Checkbox value={type} className="text-xs">
                      <span className="mr-1">{config.icon}</span>
                      {config.label}
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </div>
          
          <div className="mb-3">
            <Text strong className="text-xs">严重程度:</Text>
            <Checkbox.Group
              value={differenceFilter.severities}
              onChange={handleSeverityFilterChange}
              className="mt-1"
            >
              <Row>
                {Object.entries(SEVERITY_CONFIG).map(([severity, config]) => (
                  <Col span={8} key={severity}>
                    <Checkbox value={severity} className="text-xs">
                      {config.label}
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </div>
          
          <Button
            size="small"
            icon={<ClearOutlined />}
            onClick={clearAllFilters}
            type="link"
            className="p-0 h-auto text-xs"
          >
            清除筛选
          </Button>
        </div>
      )}

      {/* 差异列表 */}
      <div className="difference-list" style={{ maxHeight: '60vh', overflowY: 'auto' }}>
        {searchFilteredDifferences.length > 0 ? (
          <List
            dataSource={searchFilteredDifferences}
            renderItem={renderDifferenceItem}
            size="small"
            split={false}
          />
        ) : (
          <Empty 
            description={
              searchText ? "未找到匹配的差异" : 
              differenceFilter.types.length > 0 || differenceFilter.severities.length > 0 ? 
              "当前筛选条件下无差异" : "暂无差异"
            }
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="py-8"
          />
        )}
      </div>

      {/* 快捷操作 */}
      {searchFilteredDifferences.length > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <Space size="small" className="w-full justify-center">
            <Tooltip title="查看所有差异">
              <Button 
                size="small" 
                icon={<EyeOutlined />}
                onClick={() => {
                  clearAllFilters();
                  if (searchFilteredDifferences.length > 0) {
                    handleDifferenceSelect(searchFilteredDifferences[0], 0);
                  }
                }}
              >
                查看全部
              </Button>
            </Tooltip>
          </Space>
        </div>
      )}
    </Card>
  );
};

export default DifferenceNavigator;
