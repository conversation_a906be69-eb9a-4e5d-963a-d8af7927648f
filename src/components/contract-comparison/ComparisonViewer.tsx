/**
 * 对比结果展示组件
 * 
 * 实现左右分栏布局、差异高亮显示、同步滚动功能
 * 支持虚拟滚动优化大文档性能
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import {
  Card,
  Row,
  Col,
  Typography,
  Button,
  Space,
  Tooltip,
  Switch,
  Divider,
  Tag,
  Progress,
  Alert
} from 'antd';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  SyncOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import { useContractComparisonStore } from '../../stores/contractComparisonStore';
import type {
  ComparisonResult,
  DifferenceItem,
  DifferenceType
} from '../../types/contractComparison';

const { Title, Text } = Typography;

/**
 * 对比视图组件属性
 */
interface ComparisonViewerProps {
  /** 自定义样式类名 */
  className?: string;
  /** 是否显示工具栏 */
  showToolbar?: boolean;
}

/**
 * 文档行数据接口
 */
interface DocumentLineData {
  lineNumber: number;
  content: string;
  differences: DifferenceItem[];
  type: 'normal' | 'added' | 'deleted' | 'modified';
}

/**
 * 对比结果展示组件
 */
const ComparisonViewer: React.FC<ComparisonViewerProps> = ({
  className = '',
  showToolbar = true
}) => {
  const {
    comparisonResult,
    selectedDifference,
    setSelectedDifference,
    viewMode,
    setViewMode,
    showOnlyDifferences,
    setShowOnlyDifferences
  } = useContractComparisonStore();

  // 组件状态
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [syncScroll, setSyncScroll] = useState(true);
  const [scrollPosition, setScrollPosition] = useState(0);

  // 引用
  const primaryListRef = useRef<List>(null);
  const secondaryListRef = useRef<List>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  /**
   * 处理文档数据
   */
  const { primaryLines, secondaryLines } = useMemo(() => {
    if (!comparisonResult) {
      return { primaryLines: [], secondaryLines: [] };
    }

    const primaryContent = comparisonResult.primaryDocument.content.plainText;
    const secondaryContent = comparisonResult.secondaryDocument.content.plainText;
    const differences = comparisonResult.differences;

    // 将文档内容分割成行
    const primaryTextLines = primaryContent.split('\n');
    const secondaryTextLines = secondaryContent.split('\n');

    // 创建行数据
    const primaryLines: DocumentLineData[] = primaryTextLines.map((content, index) => ({
      lineNumber: index + 1,
      content,
      differences: differences.filter(diff => 
        diff.primaryPosition?.lineNumber === index + 1
      ),
      type: 'normal'
    }));

    const secondaryLines: DocumentLineData[] = secondaryTextLines.map((content, index) => ({
      lineNumber: index + 1,
      content,
      differences: differences.filter(diff => 
        diff.secondaryPosition?.lineNumber === index + 1
      ),
      type: 'normal'
    }));

    // 标记差异行的类型
    differences.forEach(diff => {
      if (diff.primaryPosition) {
        const lineIndex = diff.primaryPosition.lineNumber - 1;
        if (primaryLines[lineIndex]) {
          primaryLines[lineIndex].type = diff.type as any;
        }
      }
      if (diff.secondaryPosition) {
        const lineIndex = diff.secondaryPosition.lineNumber - 1;
        if (secondaryLines[lineIndex]) {
          secondaryLines[lineIndex].type = diff.type as any;
        }
      }
    });

    return { primaryLines, secondaryLines };
  }, [comparisonResult]);

  /**
   * 过滤后的行数据
   */
  const filteredLines = useMemo(() => {
    if (!showOnlyDifferences) {
      return { primary: primaryLines, secondary: secondaryLines };
    }

    // 只显示有差异的行
    const primaryFiltered = primaryLines.filter(line => line.type !== 'normal');
    const secondaryFiltered = secondaryLines.filter(line => line.type !== 'normal');

    return { primary: primaryFiltered, secondary: secondaryFiltered };
  }, [primaryLines, secondaryLines, showOnlyDifferences]);

  /**
   * 渲染文档行
   */
  const renderDocumentLine = useCallback((
    { index, style }: { index: number; style: React.CSSProperties },
    lines: DocumentLineData[],
    side: 'primary' | 'secondary'
  ) => {
    const line = lines[index];
    if (!line) return null;

    const isSelected = selectedDifference && 
      line.differences.some(diff => diff.id === selectedDifference.id);

    const getDifferenceTypeClass = (type: string) => {
      switch (type) {
        case 'added':
          return 'bg-green-100 border-l-4 border-green-500';
        case 'deleted':
          return 'bg-red-100 border-l-4 border-red-500';
        case 'modified':
          return 'bg-yellow-100 border-l-4 border-yellow-500';
        default:
          return '';
      }
    };

    const highlightDifferences = (content: string, differences: DifferenceItem[]) => {
      if (differences.length === 0) return content;

      let highlightedContent = content;
      differences.forEach(diff => {
        const diffContent = side === 'primary' ? diff.primaryContent : diff.secondaryContent;
        if (diffContent && highlightedContent.includes(diffContent)) {
          const className = `diff-highlight diff-${diff.type}`;
          highlightedContent = highlightedContent.replace(
            diffContent,
            `<span class="${className}">${diffContent}</span>`
          );
        }
      });

      return highlightedContent;
    };

    return (
      <div
        style={{
          ...style,
          fontSize: `${zoomLevel}%`
        }}
        className={`
          document-line p-2 border-b border-gray-100 cursor-pointer
          ${getDifferenceTypeClass(line.type)}
          ${isSelected ? 'ring-2 ring-blue-500' : ''}
          hover:bg-gray-50
        `}
        onClick={() => {
          if (line.differences.length > 0) {
            setSelectedDifference(line.differences[0]);
          }
        }}
      >
        <div className="flex">
          <div className="w-12 text-xs text-gray-400 mr-3 flex-shrink-0">
            {line.lineNumber}
          </div>
          <div 
            className="flex-1 whitespace-pre-wrap break-words"
            dangerouslySetInnerHTML={{
              __html: highlightDifferences(line.content, line.differences)
            }}
          />
        </div>
        
        {/* 差异标记 */}
        {line.differences.length > 0 && (
          <div className="mt-1 flex flex-wrap gap-1">
            {line.differences.map(diff => (
              <Tag
                key={diff.id}
                size="small"
                color={
                  diff.type === 'added' ? 'green' :
                  diff.type === 'deleted' ? 'red' :
                  diff.type === 'modified' ? 'orange' : 'blue'
                }
              >
                {diff.type}
              </Tag>
            ))}
          </div>
        )}
      </div>
    );
  }, [selectedDifference, setSelectedDifference, zoomLevel]);

  /**
   * 同步滚动处理
   */
  const handleScroll = useCallback((scrollTop: number, source: 'primary' | 'secondary') => {
    if (!syncScroll) return;

    setScrollPosition(scrollTop);
    
    if (source === 'primary' && secondaryListRef.current) {
      secondaryListRef.current.scrollTo(scrollTop);
    } else if (source === 'secondary' && primaryListRef.current) {
      primaryListRef.current.scrollTo(scrollTop);
    }
  }, [syncScroll]);

  /**
   * 缩放控制
   */
  const handleZoom = useCallback((direction: 'in' | 'out' | 'reset') => {
    setZoomLevel(prev => {
      switch (direction) {
        case 'in':
          return Math.min(prev + 10, 200);
        case 'out':
          return Math.max(prev - 10, 50);
        case 'reset':
          return 100;
        default:
          return prev;
      }
    });
  }, []);

  /**
   * 全屏切换
   */
  const toggleFullscreen = useCallback(() => {
    if (!isFullscreen && containerRef.current) {
      containerRef.current.requestFullscreen?.();
    } else if (document.fullscreenElement) {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  /**
   * 跳转到差异
   */
  const scrollToDifference = useCallback((difference: DifferenceItem) => {
    const primaryLineNumber = difference.primaryPosition?.lineNumber;
    const secondaryLineNumber = difference.secondaryPosition?.lineNumber;

    if (primaryLineNumber && primaryListRef.current) {
      const index = filteredLines.primary.findIndex(line => 
        line.lineNumber === primaryLineNumber
      );
      if (index !== -1) {
        primaryListRef.current.scrollToItem(index, 'center');
      }
    }

    if (secondaryLineNumber && secondaryListRef.current) {
      const index = filteredLines.secondary.findIndex(line => 
        line.lineNumber === secondaryLineNumber
      );
      if (index !== -1) {
        secondaryListRef.current.scrollToItem(index, 'center');
      }
    }
  }, [filteredLines]);

  // 监听选中差异变化
  useEffect(() => {
    if (selectedDifference) {
      scrollToDifference(selectedDifference);
    }
  }, [selectedDifference, scrollToDifference]);

  if (!comparisonResult) {
    return (
      <div className="text-center py-12">
        <Alert
          message="暂无对比结果"
          description="请先上传文档并执行对比分析"
          type="info"
          showIcon
        />
      </div>
    );
  }

  const itemHeight = Math.round(50 * (zoomLevel / 100));
  const containerHeight = isFullscreen ? window.innerHeight - 100 : 600;

  return (
    <div 
      ref={containerRef}
      className={`comparison-viewer ${className} ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}
    >
      {/* 工具栏 */}
      {showToolbar && (
        <Card size="small" className="mb-4">
          <div className="flex items-center justify-between">
            <Space>
              <Text strong>对比视图</Text>
              <Divider type="vertical" />
              
              <Tooltip title="同步滚动">
                <Switch
                  checked={syncScroll}
                  onChange={setSyncScroll}
                  checkedChildren={<SyncOutlined />}
                  unCheckedChildren={<SyncOutlined />}
                />
              </Tooltip>
              
              <Tooltip title="只显示差异">
                <Switch
                  checked={showOnlyDifferences}
                  onChange={setShowOnlyDifferences}
                  checkedChildren={<EyeOutlined />}
                  unCheckedChildren={<EyeInvisibleOutlined />}
                />
              </Tooltip>
            </Space>
            
            <Space>
              <Tooltip title="缩小">
                <Button
                  size="small"
                  icon={<ZoomOutOutlined />}
                  onClick={() => handleZoom('out')}
                  disabled={zoomLevel <= 50}
                />
              </Tooltip>
              
              <Text className="text-xs">{zoomLevel}%</Text>
              
              <Tooltip title="放大">
                <Button
                  size="small"
                  icon={<ZoomInOutlined />}
                  onClick={() => handleZoom('in')}
                  disabled={zoomLevel >= 200}
                />
              </Tooltip>
              
              <Tooltip title={isFullscreen ? "退出全屏" : "全屏显示"}>
                <Button
                  size="small"
                  icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                  onClick={toggleFullscreen}
                />
              </Tooltip>
            </Space>
          </div>
        </Card>
      )}

      {/* 对比统计 */}
      <Card size="small" className="mb-4">
        <Row gutter={16}>
          <Col span={6}>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {comparisonResult.similarity}%
              </div>
              <div className="text-xs text-gray-500">相似度</div>
            </div>
          </Col>
          <Col span={6}>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {comparisonResult.statistics.addedCount}
              </div>
              <div className="text-xs text-gray-500">新增</div>
            </div>
          </Col>
          <Col span={6}>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {comparisonResult.statistics.deletedCount}
              </div>
              <div className="text-xs text-gray-500">删除</div>
            </div>
          </Col>
          <Col span={6}>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {comparisonResult.statistics.modifiedCount}
              </div>
              <div className="text-xs text-gray-500">修改</div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 文档对比视图 */}
      <Row gutter={1} className="comparison-content">
        <Col span={12}>
          <Card 
            title={
              <div className="flex items-center justify-between">
                <span>主文档</span>
                <Tag color="blue">{comparisonResult.primaryDocument.originalName}</Tag>
              </div>
            }
            size="small"
            className="h-full"
          >
            <List
              ref={primaryListRef}
              height={containerHeight}
              itemCount={filteredLines.primary.length}
              itemSize={itemHeight}
              onScroll={({ scrollTop }) => handleScroll(scrollTop, 'primary')}
            >
              {({ index, style }) => renderDocumentLine(
                { index, style },
                filteredLines.primary,
                'primary'
              )}
            </List>
          </Card>
        </Col>
        
        <Col span={12}>
          <Card 
            title={
              <div className="flex items-center justify-between">
                <span>副文档</span>
                <Tag color="green">{comparisonResult.secondaryDocument.originalName}</Tag>
              </div>
            }
            size="small"
            className="h-full"
          >
            <List
              ref={secondaryListRef}
              height={containerHeight}
              itemCount={filteredLines.secondary.length}
              itemSize={itemHeight}
              onScroll={({ scrollTop }) => handleScroll(scrollTop, 'secondary')}
            >
              {({ index, style }) => renderDocumentLine(
                { index, style },
                filteredLines.secondary,
                'secondary'
              )}
            </List>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ComparisonViewer;
