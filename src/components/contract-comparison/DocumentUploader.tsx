/**
 * 文档上传组件
 * 
 * 提供拖拽上传功能、文件选择和预览、上传进度显示
 * 专为合同对比模块设计
 */

import React, { useState, useCallback, useRef } from 'react';
import {
  Upload,
  Card,
  Button,
  Progress,
  Alert,
  Typography,
  Space,
  Row,
  Col,
  Divider,
  Tag,
  Tooltip,
  message
} from 'antd';
import {
  InboxOutlined,
  FileTextOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { useContractComparisonStore } from '../../stores/contractComparisonStore';
import type {
  FileValidationResult,
  FileUploadStatus
} from '../../types/contractComparison';

const { Dragger } = Upload;
const { Title, Text, Paragraph } = Typography;

/**
 * 文档上传组件属性
 */
interface DocumentUploaderProps {
  /** 是否禁用上传 */
  disabled?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 支持的文件类型配置
 */
const SUPPORTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
};

const SUPPORTED_EXTENSIONS = ['.pdf', '.doc', '.docx'];
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

/**
 * 文档上传组件
 */
const DocumentUploader: React.FC<DocumentUploaderProps> = ({
  disabled = false,
  className = ''
}) => {
  const {
    uploadedFiles,
    uploadStatus,
    validationResults,
    setFile,
    setUploadStatus,
    setValidationResult,
    clearFile,
    error,
    setError
  } = useContractComparisonStore();

  const [dragOver, setDragOver] = useState<{ primary: boolean; secondary: boolean }>({
    primary: false,
    secondary: false
  });

  const primaryInputRef = useRef<HTMLInputElement>(null);
  const secondaryInputRef = useRef<HTMLInputElement>(null);

  /**
   * 验证文件
   */
  const validateFile = useCallback((file: File): FileValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查文件大小
    if (file.size === 0) {
      errors.push('文件为空');
    } else if (file.size > MAX_FILE_SIZE) {
      errors.push(`文件过大，最大支持 ${MAX_FILE_SIZE / 1024 / 1024}MB`);
    }

    // 检查文件类型
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!SUPPORTED_EXTENSIONS.includes(extension)) {
      errors.push(`不支持的文件格式，仅支持: ${SUPPORTED_EXTENSIONS.join(', ')}`);
    }

    // 检查文件名
    if (file.name.length > 255) {
      errors.push('文件名过长');
    }

    // 大文件警告
    if (file.size > 10 * 1024 * 1024) {
      warnings.push('文件较大，处理时间可能较长');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      fileInfo: {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: new Date(file.lastModified),
        extension: extension
      }
    };
  }, []);

  /**
   * 处理文件选择
   */
  const handleFileSelect = useCallback((file: File, type: 'primary' | 'secondary') => {
    console.log(`📁 [文件上传] 选择${type === 'primary' ? '主' : '副'}文件:`, file.name);

    // 设置上传状态
    setUploadStatus(FileUploadStatus.VALIDATING, type);

    // 验证文件
    const validation = validateFile(file);
    setValidationResult(validation, type);

    if (validation.isValid) {
      // 设置文件
      setFile(file, type);
      setUploadStatus(FileUploadStatus.READY, type);
      message.success(`${type === 'primary' ? '主' : '副'}文件上传成功`);
    } else {
      setUploadStatus(FileUploadStatus.ERROR, type);
      message.error(`文件验证失败: ${validation.errors.join(', ')}`);
    }
  }, [validateFile, setFile, setUploadStatus, setValidationResult]);

  /**
   * 处理文件删除
   */
  const handleFileRemove = useCallback((type: 'primary' | 'secondary') => {
    clearFile(type);
    message.info(`已移除${type === 'primary' ? '主' : '副'}文件`);
  }, [clearFile]);

  /**
   * 创建上传属性
   */
  const createUploadProps = useCallback((type: 'primary' | 'secondary'): UploadProps => ({
    name: 'file',
    multiple: false,
    accept: SUPPORTED_EXTENSIONS.join(','),
    showUploadList: false,
    beforeUpload: (file) => {
      handleFileSelect(file, type);
      return false; // 阻止自动上传
    },
    onDrop: (e) => {
      setDragOver(prev => ({ ...prev, [type]: false }));
      console.log(`📁 [拖拽上传] ${type}文件拖拽完成`);
    },
    onDragEnter: () => {
      setDragOver(prev => ({ ...prev, [type]: true }));
    },
    onDragLeave: () => {
      setDragOver(prev => ({ ...prev, [type]: false }));
    },
    disabled: disabled || uploadStatus[type] === FileUploadStatus.UPLOADING
  }), [handleFileSelect, disabled, uploadStatus, setDragOver]);

  /**
   * 渲染文件信息
   */
  const renderFileInfo = useCallback((type: 'primary' | 'secondary') => {
    const file = uploadedFiles[type];
    const validation = validationResults[type];
    const status = uploadStatus[type];

    if (!file) return null;

    const fileSize = (file.size / 1024 / 1024).toFixed(2);
    const isValid = validation?.isValid ?? false;

    return (
      <Card 
        size="small" 
        className={`mt-4 ${isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FileTextOutlined className={isValid ? 'text-green-600' : 'text-red-600'} />
            <div>
              <div className="font-medium text-sm">{file.name}</div>
              <div className="text-xs text-gray-500">
                {fileSize} MB • {file.type || '未知类型'}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {isValid ? (
              <CheckCircleOutlined className="text-green-600" />
            ) : (
              <ExclamationCircleOutlined className="text-red-600" />
            )}
            
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => handleFileRemove(type)}
              className="text-gray-400 hover:text-red-500"
            />
          </div>
        </div>
        
        {/* 显示验证错误 */}
        {validation && !validation.isValid && (
          <Alert
            message="文件验证失败"
            description={validation.errors.join('; ')}
            type="error"
            size="small"
            className="mt-2"
          />
        )}
        
        {/* 显示警告 */}
        {validation && validation.warnings.length > 0 && (
          <Alert
            message="注意"
            description={validation.warnings.join('; ')}
            type="warning"
            size="small"
            className="mt-2"
          />
        )}
      </Card>
    );
  }, [uploadedFiles, validationResults, uploadStatus, handleFileRemove]);

  /**
   * 渲染上传区域
   */
  const renderUploadArea = useCallback((type: 'primary' | 'secondary') => {
    const file = uploadedFiles[type];
    const status = uploadStatus[type];
    const isDragOver = dragOver[type];
    const title = type === 'primary' ? '主文件' : '副文件';
    const description = type === 'primary' ? '选择作为对比基准的文件' : '选择要与主文件对比的文件';

    if (file && status === FileUploadStatus.READY) {
      return (
        <div className="text-center p-8 border-2 border-dashed border-green-300 bg-green-50 rounded-lg">
          <CheckCircleOutlined className="text-4xl text-green-600 mb-4" />
          <Title level={4} className="text-green-800 mb-2">{title}已就绪</Title>
          <Text type="secondary">文件已成功上传并验证</Text>
        </div>
      );
    }

    return (
      <Dragger
        {...createUploadProps(type)}
        className={`
          ${isDragOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'}
          ${status === FileUploadStatus.ERROR ? 'border-red-400 bg-red-50' : ''}
          transition-all duration-200
        `}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined className={isDragOver ? 'text-blue-500' : 'text-gray-400'} />
        </p>
        <p className="ant-upload-text text-lg font-medium">
          上传{title}
        </p>
        <p className="ant-upload-hint text-gray-500">
          {description}
        </p>
        <p className="ant-upload-hint text-xs text-gray-400 mt-2">
          支持 PDF、Word 文档，最大 50MB
        </p>
      </Dragger>
    );
  }, [uploadedFiles, uploadStatus, dragOver, createUploadProps]);

  /**
   * 检查是否可以开始对比
   */
  const canStartComparison = uploadedFiles.primary && 
                            uploadedFiles.secondary && 
                            validationResults.primary?.isValid && 
                            validationResults.secondary?.isValid;

  return (
    <div className={`document-uploader ${className}`}>
      {/* 标题和说明 */}
      <div className="text-center mb-8">
        <Title level={3}>上传文档</Title>
        <Paragraph type="secondary" className="text-lg">
          请上传两个文档进行对比分析。支持 PDF 和 Word 格式，文件大小不超过 50MB。
        </Paragraph>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message="上传错误"
          description={error.message}
          type="error"
          closable
          onClose={() => setError(null)}
          className="mb-6"
        />
      )}

      {/* 上传区域 */}
      <Row gutter={24}>
        <Col span={12}>
          <div className="upload-section">
            <div className="flex items-center mb-4">
              <Title level={4} className="mb-0 mr-2">主文件</Title>
              <Tag color="blue">必需</Tag>
              <Tooltip title="作为对比基准的文档">
                <InfoCircleOutlined className="text-gray-400 ml-1" />
              </Tooltip>
            </div>
            
            {renderUploadArea('primary')}
            {renderFileInfo('primary')}
          </div>
        </Col>
        
        <Col span={12}>
          <div className="upload-section">
            <div className="flex items-center mb-4">
              <Title level={4} className="mb-0 mr-2">副文件</Title>
              <Tag color="green">必需</Tag>
              <Tooltip title="要与主文件进行对比的文档">
                <InfoCircleOutlined className="text-gray-400 ml-1" />
              </Tooltip>
            </div>
            
            {renderUploadArea('secondary')}
            {renderFileInfo('secondary')}
          </div>
        </Col>
      </Row>

      {/* 上传进度 */}
      {canStartComparison && (
        <div className="mt-8">
          <Divider />
          <div className="text-center">
            <Alert
              message="文件准备就绪"
              description="两个文件都已成功上传并验证，可以开始对比分析。"
              type="success"
              showIcon
              className="mb-4"
            />
            <Progress 
              percent={100} 
              status="success" 
              strokeColor="#52c41a"
              className="mb-4"
            />
          </div>
        </div>
      )}

      {/* 支持的格式说明 */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <Title level={5} className="mb-3">
          <InfoCircleOutlined className="mr-2" />
          支持的文件格式
        </Title>
        <Row gutter={16}>
          <Col span={8}>
            <div className="text-center">
              <FileTextOutlined className="text-2xl text-red-500 mb-2" />
              <div className="font-medium">PDF</div>
              <div className="text-xs text-gray-500">便携式文档格式</div>
            </div>
          </Col>
          <Col span={8}>
            <div className="text-center">
              <FileTextOutlined className="text-2xl text-blue-500 mb-2" />
              <div className="font-medium">DOC</div>
              <div className="text-xs text-gray-500">Word 97-2003</div>
            </div>
          </Col>
          <Col span={8}>
            <div className="text-center">
              <FileTextOutlined className="text-2xl text-green-500 mb-2" />
              <div className="font-medium">DOCX</div>
              <div className="text-xs text-gray-500">Word 2007+</div>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default DocumentUploader;
