/**
 * 文件上传区域组件
 * 支持拖拽上传、文件格式验证、大小检查、上传进度显示
 * 专为文件对比功能设计，与现有功能完全独立
 */
import React, { useState, useRef, useCallback } from 'react';
import { Upload, Button, Progress, Alert, Card, Typography, Space, Divider } from 'antd';
import { 
  InboxOutlined, 
  FileTextOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined
} from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd';
import { 
  FileValidationResult, 
  FileValidationError, 
  FileValidationWarning,
  FileValidationErrorCode,
  FileValidationWarningCode,
  FileInfo
} from '../types/fileCompare';

const { Dragger } = Upload;
const { Text, Title } = Typography;

// 支持的文件类型配置
const SUPPORTED_FILE_TYPES = {
  documents: [
    { type: 'application/pdf', extension: '.pdf', description: 'PDF文档' },
    { type: 'application/msword', extension: '.doc', description: 'Microsoft Word文档（旧版）' },
    { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', extension: '.docx', description: 'Microsoft Word文档' },
    { type: 'text/plain', extension: '.txt', description: '纯文本文件' }
  ],
  images: [
    { type: 'image/jpeg', extension: '.jpg, .jpeg', description: 'JPEG图片' },
    { type: 'image/png', extension: '.png', description: 'PNG图片' },
    { type: 'image/gif', extension: '.gif', description: 'GIF图片' },
    { type: 'image/bmp', extension: '.bmp', description: 'BMP图片' },
    { type: 'image/tiff', extension: '.tiff', description: 'TIFF图片' }
  ]
};

// 文件大小限制（10MB）
const MAX_FILE_SIZE = 10 * 1024 * 1024;

interface FileUploadAreaProps {
  /** 上传区域标题 */
  title: string;
  /** 上传区域描述 */
  description?: string;
  /** 是否必需上传文件 */
  required?: boolean;
  /** 当前上传的文件 */
  file?: File | null;
  /** 文件上传回调 */
  onFileChange: (file: File | null) => void;
  /** 文件验证回调 */
  onValidationChange?: (validation: FileValidationResult) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 上传进度 */
  uploadProgress?: number;
  /** 是否正在上传 */
  uploading?: boolean;
  /** 错误信息 */
  error?: string;
  /** 自定义样式类名 */
  className?: string;
}

interface FileUploadState {
  /** 当前文件 */
  currentFile: File | null;
  /** 验证结果 */
  validation: FileValidationResult;
  /** 是否拖拽悬停 */
  isDragOver: boolean;
  /** 预览状态 */
  showPreview: boolean;
}

const FileUploadArea: React.FC<FileUploadAreaProps> = ({
  title,
  description,
  required = false,
  file,
  onFileChange,
  onValidationChange,
  disabled = false,
  uploadProgress = 0,
  uploading = false,
  error,
  className
}) => {
  const [state, setState] = useState<FileUploadState>({
    currentFile: file || null,
    validation: { 
      isValid: true, 
      errors: [], 
      warnings: [],
      fileInfo: {
        name: '',
        size: 0,
        type: '',
        lastModified: new Date(),
        extension: '',
        isSupported: true
      }
    },
    isDragOver: false,
    showPreview: false
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  // 文件验证函数
  const validateFile = useCallback((file: File): FileValidationResult => {
    const errors: FileValidationError[] = [];
    const warnings: FileValidationWarning[] = [];

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      errors.push({
        code: FileValidationErrorCode.FILE_TOO_LARGE,
        message: `文件大小超过限制（${(MAX_FILE_SIZE / 1024 / 1024).toFixed(1)}MB）`,
        details: `文件大小: ${file.size} 字节`
      });
    } else if (file.size > MAX_FILE_SIZE * 0.8) {
      warnings.push({
        code: FileValidationWarningCode.LARGE_FILE_SIZE,
        message: '文件较大，处理时间可能较长',
        suggestion: '建议压缩文件或选择较小的文件'
      });
    }

    // 检查文件类型
    const allSupportedTypes = [
      ...SUPPORTED_FILE_TYPES.documents.map(t => t.type),
      ...SUPPORTED_FILE_TYPES.images.map(t => t.type)
    ];

    if (!allSupportedTypes.includes(file.type)) {
      errors.push({
        code: FileValidationErrorCode.UNSUPPORTED_TYPE,
        message: `不支持的文件类型：${file.type}`,
        details: `支持的类型: ${allSupportedTypes.join(', ')}`
      });
    }

    // 检查文件名
    if (!file.name || file.name.trim() === '') {
      errors.push({
        code: FileValidationErrorCode.INVALID_FORMAT,
        message: '文件名不能为空',
        details: '请选择有效的文件'
      });
    }

    // 检查文件是否为空
    if (file.size === 0) {
      errors.push({
        code: FileValidationErrorCode.FILE_EMPTY,
        message: '文件内容为空',
        details: '请选择包含内容的文件'
      });
    }

    // 检查文件扩展名
    const extension = file.name.toLowerCase().split('.').pop();
    const expectedExtensions = [
      ...SUPPORTED_FILE_TYPES.documents.map(t => t.extension.replace('.', '')),
      ...SUPPORTED_FILE_TYPES.images.map(t => t.extension.split(', ').map(e => e.replace('.', ''))).flat()
    ];

    if (extension && !expectedExtensions.includes(extension)) {
      warnings.push({
        code: FileValidationWarningCode.COMPLEX_FORMATTING,
        message: `文件扩展名 .${extension} 可能不被完全支持`,
        suggestion: '建议使用标准格式的文件'
      });
    }

    // 创建文件信息
    const fileInfo: FileInfo = {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: new Date(file.lastModified),
      extension: extension || '',
      isSupported: allSupportedTypes.includes(file.type)
    };

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      fileInfo
    };
  }, []);

  // 处理文件选择
  const handleFileSelect = useCallback((selectedFile: File) => {
    console.log('📁 [FileUploadArea] 文件选择:', {
      name: selectedFile.name,
      size: selectedFile.size,
      type: selectedFile.type,
      lastModified: selectedFile.lastModified
    });

    // 验证文件
    const validation = validateFile(selectedFile);
    
    // 更新状态
    setState(prev => ({
      ...prev,
      currentFile: selectedFile,
      validation
    }));

    // 通知父组件
    onFileChange(validation.isValid ? selectedFile : null);
    onValidationChange?.(validation);

    console.log('✅ [FileUploadArea] 文件验证结果:', validation);
  }, [validateFile, onFileChange, onValidationChange]);

  // 处理文件删除
  const handleFileRemove = useCallback(() => {
    console.log('🗑️ [FileUploadArea] 删除文件');
    
    const emptyValidation: FileValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      fileInfo: {
        name: '',
        size: 0,
        type: '',
        lastModified: new Date(),
        extension: '',
        isSupported: true
      }
    };
    
    setState(prev => ({
      ...prev,
      currentFile: null,
      validation: emptyValidation
    }));

    onFileChange(null);
    onValidationChange?.(emptyValidation);
  }, [onFileChange, onValidationChange]);

  // Ant Design Upload 配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    disabled,
    showUploadList: false,
    beforeUpload: (file) => {
      handleFileSelect(file);
      return false; // 阻止自动上传
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件类型图标
  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <EyeOutlined style={{ fontSize: 24, color: '#1890ff' }} />;
    }
    return <FileTextOutlined style={{ fontSize: 24, color: '#52c41a' }} />;
  };

  // 渲染文件信息
  const renderFileInfo = () => {
    if (!state.currentFile) return null;

    return (
      <Card 
        size="small" 
        className="mt-4"
        actions={[
          <Button 
            key="preview"
            type="text" 
            icon={<EyeOutlined />}
            onClick={() => setState(prev => ({ ...prev, showPreview: true }))}
          >
            预览
          </Button>,
          <Button 
            key="remove"
            type="text" 
            danger 
            icon={<DeleteOutlined />}
            onClick={handleFileRemove}
            disabled={uploading}
          >
            删除
          </Button>
        ]}
      >
        <div className="flex items-center space-x-3">
          {getFileIcon(state.currentFile)}
          <div className="flex-1">
            <div className="font-medium text-gray-900">{state.currentFile.name}</div>
            <div className="text-sm text-gray-500">
              {formatFileSize(state.currentFile.size)} • {state.currentFile.type}
            </div>
          </div>
          {state.validation.isValid ? (
            <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 20 }} />
          ) : (
            <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: 20 }} />
          )}
        </div>
        
        {/* 上传进度 */}
        {uploading && (
          <div className="mt-3">
            <Progress 
              percent={isNaN(uploadProgress) ? 0 : uploadProgress} 
              status={(isNaN(uploadProgress) ? 0 : uploadProgress) === 100 ? 'success' : 'active'}
              size="small"
            />
          </div>
        )}
      </Card>
    );
  };

  // 渲染验证消息
  const renderValidationMessages = () => {
    const { validation } = state;
    
    if (validation.errors.length === 0 && validation.warnings.length === 0) {
      return null;
    }

    return (
      <div className="mt-4 space-y-2">
        {/* 错误消息 */}
        {validation.errors.map((error, index) => (
          <Alert
            key={`error-${index}`}
            message={error.message}
            type="error"
            showIcon
          />
        ))}
        
        {/* 警告消息 */}
        {validation.warnings.map((warning, index) => (
          <Alert
            key={`warning-${index}`}
            message={warning.message}
            type="warning"
            showIcon
          />
        ))}
      </div>
    );
  };

  // 渲染支持的文件类型
  const renderSupportedTypes = () => (
    <div className="mt-4">
      <Divider orientation="left" orientationMargin="0">
        <Text type="secondary" style={{ fontSize: 12 }}>支持的文件类型</Text>
      </Divider>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-500">
        <div>
          <Text strong>文档类型：</Text>
          <ul className="mt-1 space-y-1">
            {SUPPORTED_FILE_TYPES.documents.map((type, index) => (
              <li key={index}>• {type.description} ({type.extension})</li>
            ))}
          </ul>
        </div>
        <div>
          <Text strong>图片类型：</Text>
          <ul className="mt-1 space-y-1">
            {SUPPORTED_FILE_TYPES.images.map((type, index) => (
              <li key={index}>• {type.description} ({type.extension})</li>
            ))}
          </ul>
        </div>
      </div>
      <div className="mt-2 text-xs text-gray-400">
        最大文件大小：{(MAX_FILE_SIZE / 1024 / 1024).toFixed(1)}MB
      </div>
    </div>
  );

  return (
    <div className={`file-upload-area ${className || ''}`}>
      {/* 标题和描述 */}
      <div className="mb-4">
        <Title level={5} className="mb-1">
          {title}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Title>
        {description && (
          <Text type="secondary" className="text-sm">
            {description}
          </Text>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          className="mb-4"
          showIcon
        />
      )}

      {/* 上传区域 */}
      {!state.currentFile ? (
        <Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            {uploading ? (
              <LoadingOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            ) : (
              <InboxOutlined style={{ fontSize: 48, color: '#d9d9d9' }} />
            )}
          </p>
          <p className="ant-upload-text">
            {uploading ? '正在处理文件...' : '点击或拖拽文件到此区域上传'}
          </p>
          <p className="ant-upload-hint">
            支持单个文件上传，支持PDF、Word、文本和图片格式
          </p>
        </Dragger>
      ) : (
        renderFileInfo()
      )}

      {/* 验证消息 */}
      {renderValidationMessages()}

      {/* 支持的文件类型说明 */}
      {!state.currentFile && renderSupportedTypes()}
    </div>
  );
};

export default FileUploadArea;