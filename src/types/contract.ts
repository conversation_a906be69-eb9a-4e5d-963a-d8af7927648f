/**
 * 合同管理模块的统一类型定义
 * 建立前端camelCase和数据库snake_case的字段映射规范
 */

// 数据库字段类型（snake_case）
export interface ContractDB {
  id: string;
  user_id: string;
  title: string;
  category: string;
  file_path?: string;
  file_url?: string;
  content?: string;
  ocr_content?: string;
  status: ContractStatus;
  counterparty?: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  risk_level?: RiskLevel;
  created_at: string;
  updated_at: string;
}

// 统一使用数据库字段名（snake_case）
export interface Contract {
  id: string;
  user_id: string;
  title: string;
  type: string;
  file_path?: string;
  file_url?: string;
  content?: string;
  ocr_content?: string;
  status: ContractStatus;
  counterparty?: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  risk_level?: RiskLevel;
  created_at: string;
  updated_at: string;
}

// 合同状态枚举
export type ContractStatus = 
  | 'draft' 
  | 'reviewing' 
  | 'approved' 
  | 'rejected' 
  | 'signed' 
  | 'expired' 
  | 'uploaded' 
  | 'processing' 
  | 'reviewed' 
  | 'archived';

// 风险等级枚举
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

// 合同类型枚举
export type ContractType = 
  | 'service' 
  | 'purchase' 
  | 'employment' 
  | 'lease' 
  | 'partnership' 
  | 'nda' 
  | 'other';

// 创建合同请求类型（统一使用数据库字段名）
export interface CreateContractRequest {
  title: string;
  category: ContractType;
  description?: string;
  counterparty?: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  risk_level?: RiskLevel;
}

// 更新合同请求类型（统一使用数据库字段名）
export interface UpdateContractRequest {
  title?: string;
  category?: ContractType;
  content?: string;
  description?: string;
  status?: ContractStatus;
  counterparty?: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  risk_level?: RiskLevel;
  file_path?: string;
  file_url?: string;
  ocr_content?: string;
}

// 合同查询参数类型（统一使用数据库字段名）
export interface ContractQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: ContractStatus;
  category?: ContractType;
  start_date?: string;
  end_date?: string;
  user_id?: string;
}

// 合同列表响应类型
export interface ContractListResponse {
  success: boolean;
  contracts: Contract[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  message?: string;
}

// 合同详情响应类型
export interface ContractDetailResponse {
  success: boolean;
  contract?: Contract;
  message?: string;
}

// 字段映射工具函数类型（现在不再需要复杂映射）

// 合同统计信息类型
export interface ContractStats {
  total: number;
  draft: number;
  reviewing: number;
  approved: number;
  rejected: number;
  signed: number;
  expired: number;
  uploaded: number;
  processing: number;
  reviewed: number;
  archived: number;
  byType: Record<ContractType, number>;
  recentActivity: Array<{
    id: string;
    title: string;
    action: string;
    timestamp: string;
  }>;
}
