/**
 * 合同对比模块数据类型定义
 * 
 * 本文件定义了合同对比功能所需的所有数据接口和类型
 * 专为合同对比模块设计，与现有功能完全独立
 */

// ==================== 核心数据接口 ====================

/**
 * 解析后的文档接口
 */
export interface ParsedDocument {
  /** 文档唯一ID */
  id: string;
  /** 原始文件名 */
  originalName: string;
  /** 文档内容 */
  content: DocumentContent;
  /** 文档元数据 */
  metadata: DocumentMetadata;
  /** 解析时间 */
  parseTime: Date;
}

/**
 * 文档内容接口
 */
export interface DocumentContent {
  /** 保留格式的HTML内容 */
  html: string;
  /** 纯文本内容 */
  plainText: string;
  /** 文档结构信息 */
  structure: DocumentStructure;
  /** 格式信息列表 */
  formatting: FormattingInfo[];
}

/**
 * 文档元数据接口
 */
export interface DocumentMetadata {
  /** 文件大小（字节） */
  size: number;
  /** MIME类型 */
  mimeType: string;
  /** 文件扩展名 */
  extension: string;
  /** 最后修改时间 */
  lastModified: Date;
  /** 页数或字数 */
  pageCount?: number;
  /** 字符数 */
  characterCount: number;
}

/**
 * 文档结构接口
 */
export interface DocumentStructure {
  /** 段落信息列表 */
  paragraphs: ParagraphInfo[];
  /** 表格信息列表 */
  tables: TableInfo[];
  /** 标题信息列表 */
  headers: HeaderInfo[];
  /** 列表信息列表 */
  lists: ListInfo[];
}

/**
 * 段落信息接口
 */
export interface ParagraphInfo {
  /** 段落ID */
  id: string;
  /** 段落内容 */
  content: string;
  /** 段落位置 */
  position: TextPosition;
  /** 段落类型 */
  type: 'normal' | 'heading' | 'list-item' | 'table-cell';
  /** 格式信息 */
  formatting?: FormattingInfo;
}

/**
 * 表格信息接口
 */
export interface TableInfo {
  /** 表格ID */
  id: string;
  /** 表格标题 */
  caption?: string;
  /** 行数 */
  rows: number;
  /** 列数 */
  columns: number;
  /** 表格数据 */
  data: string[][];
  /** 位置信息 */
  position: TextPosition;
}

/**
 * 标题信息接口
 */
export interface HeaderInfo {
  /** 标题ID */
  id: string;
  /** 标题级别（1-6） */
  level: number;
  /** 标题文本 */
  text: string;
  /** 位置信息 */
  position: TextPosition;
}

/**
 * 列表信息接口
 */
export interface ListInfo {
  /** 列表ID */
  id: string;
  /** 列表类型 */
  type: 'ordered' | 'unordered';
  /** 列表项 */
  items: string[];
  /** 位置信息 */
  position: TextPosition;
}

/**
 * 格式信息接口
 */
export interface FormattingInfo {
  /** 字体大小 */
  fontSize?: number;
  /** 是否粗体 */
  bold?: boolean;
  /** 是否斜体 */
  italic?: boolean;
  /** 是否下划线 */
  underline?: boolean;
  /** 文本颜色 */
  color?: string;
  /** 背景颜色 */
  backgroundColor?: string;
  /** 对齐方式 */
  alignment?: 'left' | 'center' | 'right' | 'justify';
}

/**
 * 文本位置接口
 */
export interface TextPosition {
  /** 起始字符索引 */
  startIndex: number;
  /** 结束字符索引 */
  endIndex: number;
  /** 行号 */
  lineNumber: number;
  /** 列号 */
  columnNumber: number;
  /** 段落索引 */
  paragraphIndex: number;
  /** 页码（PDF专用） */
  pageNumber?: number;
}

// ==================== 对比结果接口 ====================

/**
 * 对比结果接口
 */
export interface ComparisonResult {
  /** 会话ID */
  sessionId: string;
  /** 主文档 */
  primaryDocument: ParsedDocument;
  /** 副文档 */
  secondaryDocument: ParsedDocument;
  /** 差异项列表 */
  differences: DifferenceItem[];
  /** 统计信息 */
  statistics: DifferenceStatistics;
  /** 整体相似度（0-100） */
  similarity: number;
  /** 处理时间（毫秒） */
  processTime: number;
  /** 创建时间 */
  createdAt: Date;
  /** 处理状态 */
  status: 'processing' | 'completed' | 'failed';
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 差异项接口
 */
export interface DifferenceItem {
  /** 差异唯一ID */
  id: string;
  /** 差异类型 */
  type: DifferenceType;
  /** 差异严重程度 */
  severity: DifferenceSeverity;
  /** 主文档中的位置 */
  primaryPosition?: TextPosition;
  /** 副文档中的位置 */
  secondaryPosition?: TextPosition;
  /** 主文档中的内容 */
  primaryContent?: string;
  /** 副文档中的内容 */
  secondaryContent?: string;
  /** 差异描述 */
  description: string;
  /** HTML格式的内容 */
  htmlContent?: {
    primary?: string;
    secondary?: string;
  };
  /** 影响的段落数 */
  affectedParagraphs?: number;
}

/**
 * 差异统计接口
 */
export interface DifferenceStatistics {
  /** 总差异数 */
  totalDifferences: number;
  /** 新增内容数 */
  addedCount: number;
  /** 删除内容数 */
  deletedCount: number;
  /** 修改内容数 */
  modifiedCount: number;
  /** 移动内容数 */
  movedCount: number;
  /** 受影响的段落数 */
  affectedParagraphs: number;
  /** 相似度分数（0-100） */
  similarityScore: number;
  /** 文档长度对比 */
  lengthComparison: {
    primary: number;
    secondary: number;
    difference: number;
    percentageChange: number;
  };
}

// ==================== 枚举类型定义 ====================

/**
 * 差异类型枚举
 */
export enum DifferenceType {
  ADDED = 'added',           // 新增内容
  DELETED = 'deleted',       // 删除内容
  MODIFIED = 'modified',     // 修改内容
  MOVED = 'moved'           // 移动内容
}

/**
 * 差异严重程度枚举
 */
export enum DifferenceSeverity {
  HIGH = 'high',            // 高级差异
  MEDIUM = 'medium',        // 中级差异
  LOW = 'low'               // 低级差异
}

// ==================== 错误处理接口 ====================

/**
 * 合同对比错误类型枚举
 */
export enum ContractComparisonErrorType {
  INVALID_FILE_FORMAT = 'invalid_file_format',
  FILE_TOO_LARGE = 'file_too_large',
  PARSE_ERROR = 'parse_error',
  COMPARISON_FAILED = 'comparison_failed',
  NETWORK_ERROR = 'network_error',
  PERMISSION_DENIED = 'permission_denied',
  SERVER_ERROR = 'server_error',
  TIMEOUT = 'timeout',
  VALIDATION_ERROR = 'validation_error'
}

/**
 * 合同对比错误接口
 */
export interface ContractComparisonError {
  /** 错误类型 */
  type: ContractComparisonErrorType;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  details?: string;
  /** 错误代码 */
  code?: string;
  /** 时间戳 */
  timestamp: Date;
  /** 相关文件名 */
  fileName?: string;
}

// ==================== 文件上传相关接口 ====================

/**
 * 文件上传状态枚举
 */
export enum FileUploadStatus {
  IDLE = 'idle',                 // 空闲状态
  SELECTING = 'selecting',       // 选择文件中
  VALIDATING = 'validating',     // 验证文件中
  UPLOADING = 'uploading',       // 上传中
  PARSING = 'parsing',           // 解析中
  READY = 'ready',              // 准备就绪
  ERROR = 'error'               // 错误状态
}

/**
 * 文件验证结果接口
 */
export interface FileValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误列表 */
  errors: string[];
  /** 警告列表 */
  warnings: string[];
  /** 文件信息 */
  fileInfo: {
    name: string;
    size: number;
    type: string;
    lastModified: Date;
    extension: string;
  };
}

// ==================== API响应接口 ====================

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean;
  /** 响应数据 */
  data?: T;
  /** 错误信息 */
  error?: ContractComparisonError;
  /** 响应消息 */
  message?: string;
  /** 时间戳 */
  timestamp: Date;
}

/**
 * 对比进度接口
 */
export interface ComparisonProgress {
  /** 会话ID */
  sessionId: string;
  /** 进度百分比（0-100） */
  progress: number;
  /** 当前步骤 */
  currentStep: string;
  /** 总步骤数 */
  totalSteps: number;
  /** 预计剩余时间（秒） */
  estimatedTimeRemaining?: number;
  /** 当前阶段描述 */
  description: string;
}
