/**
 * 文件合同对比模块 - 数据类型定义
 * 
 * 本文件定义了文件对比功能所需的所有数据接口和类型
 * 与现有Compare.tsx模块完全独立，确保零影响集成
 */

// ==================== 核心数据接口 ====================

/**
 * 文件对比结果接口
 * 包含完整的对比分析结果
 */
export interface FileComparisonResult {
  /** 对比会话ID */
  sessionId: string;
  /** 对比时间戳 */
  timestamp: Date;
  /** 主文件信息 */
  primaryFile: FileMetadata;
  /** 副文件信息 */
  secondaryFile: FileMetadata;
  /** 主文件文档内容 */
  primaryContent?: DocumentContent;
  /** 副文件文档内容 */
  secondaryContent?: DocumentContent;
  /** 文件信息（新格式，与后端API匹配） */
  files?: {
    primary: FileMetadata;
    secondary: FileMetadata;
  };
  /** 整体相似度（0-100） */
  overallSimilarity: number;
  /** 差异项列表 */
  differences: DifferenceItem[];
  /** 相似项列表 */
  similarities: SimilarityItem[];
  /** 条款对比结果 */
  clauseComparison: ClauseComparisonResult;
  /** 统计信息 */
  statistics: ComparisonStatistics;
  /** 处理状态 */
  status: 'processing' | 'completed' | 'failed';
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 差异项接口
 * 描述两个文档之间的具体差异
 */
export interface DifferenceItem {
  /** 差异唯一ID */
  id: string;
  /** 差异类型 */
  type: DifferenceType;
  /** 差异严重程度 */
  severity: DifferenceSeverity;
  /** 主文件中的文本位置 */
  primaryPosition?: TextPosition;
  /** 副文件中的文本位置 */
  secondaryPosition?: TextPosition;
  /** 主文件中的文本内容 */
  primaryText?: string;
  /** 副文件中的文本内容 */
  secondaryText?: string;
  /** 差异描述 */
  description: string;
  /** 建议操作 */
  suggestion?: string;
  /** 条款类型（如果适用） */
  clauseType?: ClauseType;
  /** 影响评估 */
  impact: ImpactLevel;
}

/**
 * 相似项接口
 * 描述两个文档之间的相似内容
 */
export interface SimilarityItem {
  /** 相似项唯一ID */
  id: string;
  /** 相似度分数（0-100） */
  similarity: number;
  /** 主文件中的文本位置 */
  primaryPosition: TextPosition;
  /** 副文件中的文本位置 */
  secondaryPosition: TextPosition;
  /** 相似的文本内容 */
  text: string;
  /** 条款类型（如果适用） */
  clauseType?: ClauseType;
  /** 详细分析信息 */
  details?: {
    /** 增强算法相似度分数 */
    enhancedSimilarity: number;
    /** 简单算法相似度分数 */
    simpleSimilarity: number;
    /** 使用的算法名称 */
    algorithm: string;
    /** 其他分析数据 */
    [key: string]: any;
  };
}

/**
 * 条款对比结果接口
 */
export interface ClauseComparisonResult {
  /** 主文件提取的条款 */
  primaryClauses: ClauseItem[];
  /** 副文件提取的条款 */
  secondaryClauses: ClauseItem[];
  /** 匹配的条款对 */
  matchedClauses: ClauseMatch[];
  /** 主文件独有条款 */
  primaryOnlyClauses: ClauseItem[];
  /** 副文件独有条款 */
  secondaryOnlyClauses: ClauseItem[];
}

/**
 * 条款项接口
 */
export interface ClauseItem {
  /** 条款唯一ID */
  id: string;
  /** 条款类型 */
  type: ClauseType;
  /** 条款标题 */
  title: string;
  /** 条款内容 */
  content: string;
  /** 文本位置 */
  position: TextPosition;
  /** 重要性级别 */
  importance: ImportanceLevel;
  /** 关键词 */
  keywords: string[];
}

/**
 * 条款匹配接口
 */
export interface ClauseMatch {
  /** 主文件条款 */
  primaryClause: ClauseItem;
  /** 副文件条款 */
  secondaryClause: ClauseItem;
  /** 匹配度（0-100） */
  matchScore: number;
  /** 差异列表 */
  differences: DifferenceItem[];
}

// ==================== 文件相关接口 ====================

/**
 * 文件元数据接口
 */
export interface FileMetadata {
  /** 文件名 */
  name: string;
  /** 文件大小（字节） */
  size: number;
  /** MIME类型 */
  mimeType: string;
  /** 文件扩展名 */
  extension: string;
  /** 上传时间 */
  uploadTime: Date;
  /** 文件哈希值 */
  hash?: string;
  /** 页数（PDF）或字数（Word） */
  pageCount?: number;
  /** 字符数 */
  characterCount?: number;
}

/**
 * 对比选项接口
 */
export interface ComparisonOptions {
  /** 是否忽略格式差异 */
  ignoreFormatting: boolean;
  /** 是否忽略空白字符 */
  ignoreWhitespace: boolean;
  /** 是否进行条款级别对比 */
  enableClauseComparison: boolean;
  /** 相似度阈值（0-100） */
  similarityThreshold: number;
  /** 是否生成建议 */
  generateSuggestions: boolean;
  /** 语言设置 */
  language: 'zh' | 'en';
}

/**
 * 解析后的文档接口
 */
export interface ParsedDocument {
  /** 文档内容 */
  content: DocumentContent;
  /** 文件元数据 */
  metadata: FileMetadata;
  /** 解析时间 */
  parseTime: Date;
  /** 解析状态 */
  status: 'success' | 'partial' | 'failed';
  /** 错误信息 */
  errors?: string[];
  /** 纯文本内容（用于高亮计算） */
  plainText?: string;
}

/**
 * 文档内容接口
 */
export interface DocumentContent {
  /** 纯文本内容 */
  text: string;
  /** 段落列表 */
  paragraphs: Paragraph[];
  /** 结构化内容 */
  structure: DocumentStructure;
  /** 提取的条款 */
  extractedClauses?: ClauseItem[];
  /** 纯文本（用于高亮计算，当text为HTML时使用） */
  plainText?: string;
}

/**
 * 段落接口
 */
export interface Paragraph {
  /** 段落ID */
  id: string;
  /** 段落内容 */
  content: string;
  /** 段落位置 */
  position: TextPosition;
  /** 段落类型 */
  type: ParagraphType;
  /** 格式信息 */
  formatting?: FormattingInfo;
}

/**
 * 文档结构接口
 */
export interface DocumentStructure {
  /** 标题层级 */
  headings: HeadingItem[];
  /** 章节列表 */
  sections: SectionItem[];
  /** 列表项 */
  lists: ListItem[];
  /** 表格 */
  tables: TableItem[];
}

/**
 * 标题项接口
 */
export interface HeadingItem {
  /** 标题ID */
  id: string;
  /** 标题级别（1-6） */
  level: number;
  /** 标题文本 */
  text: string;
  /** 位置信息 */
  position: TextPosition;
}

/**
 * 章节项接口
 */
export interface SectionItem {
  /** 章节ID */
  id: string;
  /** 章节标题 */
  title: string;
  /** 章节内容 */
  content: string;
  /** 位置信息 */
  position: TextPosition;
  /** 子章节 */
  subsections: SectionItem[];
}

/**
 * 列表项接口
 */
export interface ListItem {
  /** 列表ID */
  id: string;
  /** 列表类型 */
  type: 'ordered' | 'unordered';
  /** 列表项 */
  items: string[];
  /** 位置信息 */
  position: TextPosition;
}

/**
 * 表格项接口
 */
export interface TableItem {
  /** 表格ID */
  id: string;
  /** 表格标题 */
  caption?: string;
  /** 表格数据 */
  data: string[][];
  /** 位置信息 */
  position: TextPosition;
}

// ==================== 位置和格式接口 ====================

/**
 * 文本位置接口
 */
export interface TextPosition {
  /** 起始行号 */
  startLine: number;
  /** 结束行号 */
  endLine: number;
  /** 起始列号 */
  startColumn: number;
  /** 结束列号 */
  endColumn: number;
  /** 起始字符索引 */
  startIndex: number;
  /** 结束字符索引 */
  endIndex: number;
  /** 页码（PDF专用） */
  pageNumber?: number;
}

/**
 * 格式信息接口
 */
export interface FormattingInfo {
  /** 字体大小 */
  fontSize?: number;
  /** 是否粗体 */
  bold?: boolean;
  /** 是否斜体 */
  italic?: boolean;
  /** 是否下划线 */
  underline?: boolean;
  /** 文本颜色 */
  color?: string;
  /** 对齐方式 */
  alignment?: 'left' | 'center' | 'right' | 'justify';
}

// ==================== 统计信息接口 ====================

/**
 * 对比统计信息接口
 */
export interface ComparisonStatistics {
  /** 总差异数 */
  totalDifferences: number;
  /** 新增内容数 */
  addedCount: number;
  /** 删除内容数 */
  deletedCount: number;
  /** 修改内容数 */
  modifiedCount: number;
  /** 相似内容数 */
  similarCount: number;
  /** 处理时间（毫秒） */
  processingTime: number;
  /** 文档长度对比 */
  lengthComparison: {
    primary: number;
    secondary: number;
    difference: number;
    percentageChange: number;
  };
  /** 条款统计 */
  clauseStatistics: {
    primaryCount: number;
    secondaryCount: number;
    matchedCount: number;
    primaryOnlyCount: number;
    secondaryOnlyCount: number;
  };
}

// ==================== 枚举类型定义 ====================

/**
 * 差异类型枚举
 */
export enum DifferenceType {
  ADDED = 'added',           // 新增内容
  DELETED = 'deleted',       // 删除内容
  MODIFIED = 'modified',     // 修改内容
  MOVED = 'moved',          // 移动内容
  FORMATTED = 'formatted'    // 格式变化
}

/**
 * 差异严重程度枚举
 */
export enum DifferenceSeverity {
  CRITICAL = 'critical',     // 关键差异
  HIGH = 'high',            // 高级差异
  MEDIUM = 'medium',        // 中级差异
  LOW = 'low',              // 低级差异
  COSMETIC = 'cosmetic'     // 外观差异
}

/**
 * 条款类型枚举
 */
export enum ClauseType {
  PAYMENT = 'payment',           // 付款条款
  DELIVERY = 'delivery',         // 交付条款
  WARRANTY = 'warranty',         // 保修条款
  LIABILITY = 'liability',       // 责任条款
  TERMINATION = 'termination',   // 终止条款
  CONFIDENTIALITY = 'confidentiality', // 保密条款
  DISPUTE = 'dispute',           // 争议解决
  FORCE_MAJEURE = 'force_majeure', // 不可抗力
  INTELLECTUAL_PROPERTY = 'intellectual_property', // 知识产权
  GENERAL = 'general',           // 一般条款
  OTHER = 'other'               // 其他
}

/**
 * 重要性级别枚举
 */
export enum ImportanceLevel {
  CRITICAL = 'critical',     // 关键重要
  HIGH = 'high',            // 高度重要
  MEDIUM = 'medium',        // 中等重要
  LOW = 'low'               // 低度重要
}

/**
 * 影响级别枚举
 */
export enum ImpactLevel {
  HIGH = 'high',            // 高影响
  MEDIUM = 'medium',        // 中等影响
  LOW = 'low',              // 低影响
  NONE = 'none'             // 无影响
}

/**
 * 段落类型枚举
 */
export enum ParagraphType {
  HEADING = 'heading',       // 标题
  BODY = 'body',            // 正文
  LIST_ITEM = 'list_item',  // 列表项
  TABLE_CELL = 'table_cell', // 表格单元格
  QUOTE = 'quote',          // 引用
  CODE = 'code'             // 代码块
}

// ==================== 错误处理接口 ====================

/**
 * 文件对比错误类型枚举
 */
export enum FileCompareErrorType {
  INVALID_FILE_FORMAT = 'invalid_file_format',
  FILE_TOO_LARGE = 'file_too_large',
  PARSE_ERROR = 'parse_error',
  COMPARISON_FAILED = 'comparison_failed',
  NETWORK_ERROR = 'network_error',
  PERMISSION_DENIED = 'permission_denied',
  SERVER_ERROR = 'server_error',
  TIMEOUT = 'timeout'
}

/**
 * 文件对比错误接口
 */
export interface FileCompareError {
  /** 错误类型 */
  type: FileCompareErrorType;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  details?: string;
  /** 错误代码 */
  code?: string;
  /** 时间戳 */
  timestamp: Date;
  /** 相关文件 */
  fileName?: string;
}

// ==================== API响应接口 ====================

/**
 * API响应基础接口
 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean;
  /** 响应数据 */
  data?: T;
  /** 错误信息 */
  error?: FileCompareError;
  /** 响应消息 */
  message?: string;
  /** 时间戳 */
  timestamp: Date;
}

/**
 * 文件上传响应接口
 */
export interface FileUploadResponse {
  /** 文件ID */
  fileId: string;
  /** 文件元数据 */
  metadata: FileMetadata;
  /** 上传状态 */
  status: 'uploaded' | 'processing' | 'ready';
}

/**
 * 对比进度接口
 */
export interface ComparisonProgress {
  /** 会话ID */
  sessionId: string;
  /** 进度百分比（0-100） */
  progress: number;
  /** 当前步骤 */
  currentStep: string;
  /** 总步骤数 */
  totalSteps: number;
  /** 预计剩余时间（秒） */
  estimatedTimeRemaining?: number;
}

// ==================== 文件上传扩展接口 ====================

/**
 * 文件上传状态枚举
 */
export enum FileUploadStatus {
  IDLE = 'idle',                 // 空闲状态
  SELECTING = 'selecting',       // 选择文件中
  VALIDATING = 'validating',     // 验证文件中
  UPLOADING = 'uploading',       // 上传中
  PARSING = 'parsing',           // 解析中
  READY = 'ready',              // 准备就绪
  ERROR = 'error'               // 错误状态
}

/**
 * 文件验证结果接口
 */
export interface FileValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误列表 */
  errors: FileValidationError[];
  /** 警告列表 */
  warnings: FileValidationWarning[];
  /** 文件信息 */
  fileInfo: FileInfo;
}

/**
 * 文件验证错误接口
 */
export interface FileValidationError {
  /** 错误代码 */
  code: FileValidationErrorCode;
  /** 错误消息 */
  message: string;
  /** 错误详情 */
  details?: string;
}

/**
 * 文件验证警告接口
 */
export interface FileValidationWarning {
  /** 警告代码 */
  code: FileValidationWarningCode;
  /** 警告消息 */
  message: string;
  /** 建议操作 */
  suggestion?: string;
}

/**
 * 文件信息接口
 */
export interface FileInfo {
  /** 文件名 */
  name: string;
  /** 文件大小 */
  size: number;
  /** 文件类型 */
  type: string;
  /** 最后修改时间 */
  lastModified: Date;
  /** 文件扩展名 */
  extension: string;
  /** 是否为支持的格式 */
  isSupported: boolean;
  /** 预估页数 */
  estimatedPages?: number;
}

/**
 * 文件上传进度接口
 */
export interface FileUploadProgress {
  /** 文件ID */
  fileId: string;
  /** 上传进度百分比 */
  uploadProgress: number;
  /** 解析进度百分比 */
  parseProgress: number;
  /** 当前状态 */
  status: FileUploadStatus;
  /** 当前步骤描述 */
  currentStep: string;
  /** 错误信息 */
  error?: FileCompareError;
  /** 开始时间 */
  startTime: Date;
  /** 预计完成时间 */
  estimatedCompletionTime?: Date;
}

/**
 * 文件上传配置接口
 */
export interface FileUploadConfig {
  /** 最大文件大小（字节） */
  maxFileSize: number;
  /** 支持的文件类型 */
  supportedTypes: string[];
  /** 支持的文件扩展名 */
  supportedExtensions: string[];
  /** 是否允许多文件上传 */
  allowMultiple: boolean;
  /** 上传超时时间（毫秒） */
  uploadTimeout: number;
  /** 解析超时时间（毫秒） */
  parseTimeout: number;
  /** 是否启用拖拽上传 */
  enableDragDrop: boolean;
}

/**
 * 文件处理队列项接口
 */
export interface FileProcessingQueueItem {
  /** 队列项ID */
  id: string;
  /** 文件信息 */
  file: File;
  /** 文件类型（主文件/副文件） */
  fileType: 'primary' | 'secondary';
  /** 处理状态 */
  status: FileUploadStatus;
  /** 优先级 */
  priority: number;
  /** 创建时间 */
  createdAt: Date;
  /** 开始处理时间 */
  startedAt?: Date;
  /** 完成时间 */
  completedAt?: Date;
  /** 处理结果 */
  result?: ParsedDocument;
  /** 错误信息 */
  error?: FileCompareError;
}

/**
 * 文件验证错误代码枚举
 */
export enum FileValidationErrorCode {
  INVALID_FORMAT = 'invalid_format',
  FILE_TOO_LARGE = 'file_too_large',
  FILE_EMPTY = 'file_empty',
  UNSUPPORTED_TYPE = 'unsupported_type',
  CORRUPTED_FILE = 'corrupted_file',
  PASSWORD_PROTECTED = 'password_protected',
  INVALID_ENCODING = 'invalid_encoding'
}

/**
 * 文件验证警告代码枚举
 */
export enum FileValidationWarningCode {
  LARGE_FILE_SIZE = 'large_file_size',
  COMPLEX_FORMATTING = 'complex_formatting',
  MULTIPLE_LANGUAGES = 'multiple_languages',
  LOW_TEXT_CONTENT = 'low_text_content',
  SCANNED_DOCUMENT = 'scanned_document'
}

// ==================== 解析扩展接口 ====================

/**
 * 提取的条款接口（扩展版）
 */
export interface ExtractedClauses {
  /** 条款列表 */
  clauses: ClauseItem[];
  /** 提取统计 */
  extractionStats: ClauseExtractionStats;
  /** 提取配置 */
  extractionConfig: ClauseExtractionConfig;
  /** 提取时间 */
  extractionTime: Date;
}

/**
 * 条款提取统计接口
 */
export interface ClauseExtractionStats {
  /** 总条款数 */
  totalClauses: number;
  /** 按类型分组的条款数 */
  clausesByType: Record<ClauseType, number>;
  /** 按重要性分组的条款数 */
  clausesByImportance: Record<ImportanceLevel, number>;
  /** 提取置信度 */
  extractionConfidence: number;
  /** 处理时间（毫秒） */
  processingTime: number;
}

/**
 * 条款提取配置接口
 */
export interface ClauseExtractionConfig {
  /** 是否启用智能识别 */
  enableSmartRecognition: boolean;
  /** 最小条款长度 */
  minClauseLength: number;
  /** 最大条款长度 */
  maxClauseLength: number;
  /** 置信度阈值 */
  confidenceThreshold: number;
  /** 语言设置 */
  language: 'zh' | 'en' | 'auto';
  /** 自定义关键词 */
  customKeywords: string[];
}

// ==================== 缓存和性能接口 ====================

/**
 * 解析缓存接口
 */
export interface ParseCache {
  /** 缓存键 */
  key: string;
  /** 文件哈希 */
  fileHash: string;
  /** 解析结果 */
  result: ParsedDocument;
  /** 缓存时间 */
  cachedAt: Date;
  /** 过期时间 */
  expiresAt: Date;
  /** 访问次数 */
  accessCount: number;
  /** 最后访问时间 */
  lastAccessedAt: Date;
}

/**
 * 性能监控接口
 */
export interface PerformanceMetrics {
  /** 文件上传时间 */
  uploadTime: number;
  /** 文件解析时间 */
  parseTime: number;
  /** 条款提取时间 */
  clauseExtractionTime: number;
  /** 对比分析时间 */
  comparisonTime: number;
  /** 总处理时间 */
  totalProcessingTime: number;
  /** 内存使用峰值 */
  peakMemoryUsage: number;
  /** CPU使用率 */
  cpuUsage: number;
}

// ==================== 导出相关接口 ====================

/**
 * 导出选项接口
 */
export interface ExportOptions {
  /** 导出格式 */
  format: 'pdf' | 'excel' | 'word';
  /** 是否包含统计信息 */
  includeStatistics: boolean;
  /** 是否包含详细差异 */
  includeDetailedDifferences: boolean;
  /** 是否包含相似内容 */
  includeSimilarities: boolean;
  /** 文件名 */
  fileName?: string;
  /** 模板选择 */
  template?: 'standard' | 'detailed' | 'summary';
}

/**
 * 导出结果接口
 */
export interface ExportResult {
  /** 导出文件URL */
  fileUrl: string;
  /** 文件名 */
  fileName: string;
  /** 文件大小 */
  fileSize: number;
  /** 导出时间 */
  exportTime: Date;
  /** 有效期 */
  expiresAt: Date;
}