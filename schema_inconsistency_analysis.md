# 数据库表结构与前后端代码字段不一致问题分析

## 分析时间
2024年1月

## 问题概述
通过对比Supabase实际表结构、前端代码和后端代码，发现多处字段不一致问题，导致新增合同等功能出现错误。

## 详细对比分析

### 1. contracts表字段不一致问题

#### Supabase实际表结构（标准）：
- id (uuid)
- user_id (uuid) 
- title (varchar)
- **category** (varchar) - 实际字段名
- file_path (varchar)
- content (text)
- ocr_content (text) - 带中文注释："OCR识别的文本内容"
- status (varchar)
- created_at (timestamptz)
- updated_at (timestamptz)
- file_url (text) - 带中文注释："文件的公共访问URL"
- counterparty (varchar) - 带中文注释："对方当事人/合同相对方"
- amount (numeric) - 带中文注释："合同金额"
- start_date (date) - 带中文注释："合同开始日期"
- end_date (date) - 带中文注释："合同结束日期"
- risk_level (varchar) - 带中文注释："风险等级：low-低风险，medium-中风险，high-高风险，critical-严重风险"

#### 前端代码中的Contract接口：
```typescript
interface Contract {
  id: string;
  title: string;
  type: string; // ❌ 错误：应该是category
  status: 'draft' | 'reviewing' | 'approved' | 'rejected' | 'signed' | 'expired'; // ❌ 状态值不匹配
  counterparty: string;
  amount?: number;
  startDate?: string; // ❌ 错误：应该是start_date
  endDate?: string; // ❌ 错误：应该是end_date
  createdAt: string; // ❌ 错误：应该是created_at
  updatedAt: string; // ❌ 错误：应该是updated_at
  createdBy: string; // ❌ 错误：应该是user_id
  riskLevel: 'low' | 'medium' | 'high'; // ❌ 错误：应该是risk_level，且缺少critical
  file_path?: string;
}
```

#### 后端代码中的类型定义：
```typescript
// supabaseClient.ts中的定义
contracts: {
  Row: {
    category: string | null; // ✅ 正确
    // 但status枚举值不完整
    status: 'uploaded' | 'processing' | 'reviewed' | 'archived'; // ❌ 缺少其他状态
  }
}
```

### 2. templates表字段一致性

#### Supabase实际表结构：
- id (uuid)
- name (varchar)
- **category** (varchar) - 正确字段名
- content (text)
- parameters (jsonb)
- is_active (boolean)
- created_at (timestamptz)

#### 前后端代码：
- ✅ 后端类型定义正确使用category字段
- ✅ 前端代码中也正确使用category字段

### 3. 其他表字段分析

#### users表：
- ✅ 字段定义基本一致
- ❌ 缺少中文注释

#### review_tasks表：
- ✅ 字段定义基本一致
- ❌ 缺少中文注释

#### risk_items表：
- ✅ 字段定义基本一致
- ❌ 缺少中文注释

#### knowledge_base表：
- ✅ 字段定义基本一致
- ❌ 缺少中文注释

#### review_rules表：
- ✅ 字段定义基本一致
- ❌ 缺少中文注释

#### contract_elements表：
- ✅ 字段定义基本一致
- ❌ 缺少中文注释

## 主要问题总结

### 1. 字段命名不一致
- **contracts表**：前端使用`type`，数据库实际是`category`
- **contracts表**：前端使用驼峰命名（startDate, endDate等），数据库使用下划线命名

### 2. 状态枚举值不匹配
- **contracts表**：前端定义的status枚举与数据库约束不匹配

### 3. 字段映射缺失
- 前后端代码中缺少完整的字段映射逻辑
- 特别是新增的字段（counterparty, amount, start_date, end_date, risk_level等）

### 4. 中文注释缺失
- 除了contracts表的部分字段外，其他表都缺少中文注释
- 影响数据库可读性和维护性

## 修复优先级

### 高优先级（影响功能）：
1. 修复contracts表的字段映射问题
2. 统一status枚举值定义
3. 修复前端字段命名不一致问题

### 中优先级（改善体验）：
1. 为所有表添加完整的中文注释
2. 更新SQL迁移脚本
3. 更新技术文档

### 低优先级（长期维护）：
1. 建立字段映射规范
2. 添加类型检查机制

## 下一步行动计划

1. 立即修复contracts表的字段映射问题
2. 统一前后端的类型定义
3. 为所有表添加中文注释
4. 更新相关文档和迁移脚本
5. 进行全面测试确保功能正常