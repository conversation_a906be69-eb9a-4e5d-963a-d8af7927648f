-- 为contracts表添加缺失的字段
-- 创建时间：2024-01-18
-- 目的：修复前端期望字段与数据库实际字段的不匹配问题

-- 1. 添加缺失的字段
ALTER TABLE contracts 
ADD COLUMN IF NOT EXISTS counterparty VARCHAR(255),
ADD COLUMN IF NOT EXISTS amount DECIMAL(15,2),
ADD COLUMN IF NOT EXISTS start_date DATE,
ADD COLUMN IF NOT EXISTS end_date DATE,
ADD COLUMN IF NOT EXISTS risk_level VARCHAR(20) CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
ADD COLUMN IF NOT EXISTS ocr_content TEXT;

-- 2. 修改status字段的约束，统一前后端状态值
ALTER TABLE contracts 
DROP CONSTRAINT IF EXISTS contracts_status_check;

ALTER TABLE contracts 
ADD CONSTRAINT contracts_status_check 
CHECK (status IN ('draft', 'reviewing', 'approved', 'rejected', 'signed', 'expired', 'uploaded', 'processing', 'reviewed', 'archived'));

-- 3. 修改type字段名为category以保持后端一致性
-- 注意：这里我们保持type字段，但在查询时做映射

-- 4. 为新字段添加索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_contracts_counterparty 
ON contracts (counterparty);

CREATE INDEX IF NOT EXISTS idx_contracts_amount 
ON contracts (amount);

CREATE INDEX IF NOT EXISTS idx_contracts_risk_level 
ON contracts (risk_level);

CREATE INDEX IF NOT EXISTS idx_contracts_start_date 
ON contracts (start_date);

CREATE INDEX IF NOT EXISTS idx_contracts_end_date 
ON contracts (end_date);

-- 5. 添加字段注释
COMMENT ON COLUMN contracts.counterparty IS '对方当事人/合同相对方';
COMMENT ON COLUMN contracts.amount IS '合同金额';
COMMENT ON COLUMN contracts.start_date IS '合同开始日期';
COMMENT ON COLUMN contracts.end_date IS '合同结束日期';
COMMENT ON COLUMN contracts.risk_level IS '风险等级：low-低风险，medium-中风险，high-高风险，critical-严重风险';
COMMENT ON COLUMN contracts.ocr_content IS 'OCR识别的文本内容';

-- 6. 更新现有数据的默认值（可选）
-- 为现有合同设置默认风险等级
UPDATE contracts 
SET risk_level = 'medium' 
WHERE risk_level IS NULL;

-- 7. 创建视图以便于前后端数据映射
CREATE OR REPLACE VIEW contract_view AS
SELECT 
  id,
  title,
  category as type,
  category,
  status,
  counterparty,
  amount,
  start_date as "startDate",
  end_date as "endDate",
  created_at as "createdAt",
  updated_at as "updatedAt",
  user_id as "createdBy",
  risk_level as "riskLevel",
  file_path,
  file_url,
  content,
  ocr_content
FROM contracts;

-- 8. 为视图添加注释
COMMENT ON VIEW contract_view IS '合同数据视图，用于前后端字段映射';