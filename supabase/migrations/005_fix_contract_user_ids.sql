-- 修复合同数据中的用户ID引用问题
-- 将合同数据中的用户ID更新为正确的auth用户ID

-- 更新合同数据中的用户ID，从旧的演示ID更新为正确的auth用户ID
UPDATE contracts 
SET user_id = 'd3eebc99-9c0b-4ef8-bb6d-6bb9bd380a44' 
WHERE user_id = '550e8400-e29b-41d4-a716-446655440004';

-- 如果有其他合同引用了admin用户的旧ID，也需要更新
UPDATE contracts 
SET user_id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' 
WHERE user_id = '550e8400-e29b-41d4-a716-446655440001';

-- 如果有合同引用了法务经理的旧ID，也需要更新
UPDATE contracts 
SET user_id = 'b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a22' 
WHERE user_id = '550e8400-e29b-41d4-a716-446655440002';

-- 如果有合同引用了法务专员的旧ID，也需要更新
UPDATE contracts 
SET user_id = 'c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a33' 
WHERE user_id = '550e8400-e29b-41d4-a716-446655440003';

-- 验证更新结果
-- 检查是否还有引用旧用户ID的合同
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM contracts 
        WHERE user_id IN (
            '550e8400-e29b-41d4-a716-446655440001',
            '550e8400-e29b-41d4-a716-446655440002', 
            '550e8400-e29b-41d4-a716-446655440003',
            '550e8400-e29b-41d4-a716-446655440004'
        )
    ) THEN
        RAISE NOTICE '警告：仍有合同引用旧的用户ID';
    ELSE
        RAISE NOTICE '成功：所有合同的用户ID已更新为正确的auth用户ID';
    END IF;
END $$;

-- 更新统计信息
ANALYZE contracts;