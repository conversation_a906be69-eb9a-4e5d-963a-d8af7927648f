-- 配置行级安全策略和权限

-- 1. 基础权限设置
-- 为anon角色授予基本读取权限
GRANT SELECT ON users TO anon;
GRANT SELECT ON templates TO anon;
GRANT SELECT ON knowledge_base TO anon;
GRANT SELECT ON review_rules TO anon;

-- 为authenticated角色授予完整权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;

-- 2. 启用行级安全策略
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE contract_elements ENABLE ROW LEVEL SECURITY;
ALTER TABLE risk_items ENABLE ROW LEVEL SECURITY;

-- 3. 合同访问策略
-- 用户可以查看自己的合同，管理员和法务经理可以查看所有合同
CREATE POLICY "Users can view own contracts" ON contracts
    FOR SELECT USING (
        auth.uid() = user_id::uuid OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'legal_manager')
        )
    );

-- 用户可以插入自己的合同
CREATE POLICY "Users can insert own contracts" ON contracts
    FOR INSERT WITH CHECK (auth.uid() = user_id::uuid);

-- 用户可以更新自己的合同，管理员和法务经理可以更新所有合同
CREATE POLICY "Users can update own contracts" ON contracts
    FOR UPDATE USING (
        auth.uid() = user_id::uuid OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'legal_manager')
        )
    );

-- 用户可以删除自己的合同，管理员可以删除所有合同
CREATE POLICY "Users can delete own contracts" ON contracts
    FOR DELETE USING (
        auth.uid() = user_id::uuid OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- 4. 审查任务访问策略
-- 用户可以查看自己的审查任务，管理员和法务人员可以查看所有任务
CREATE POLICY "Users can view own review tasks" ON review_tasks
    FOR SELECT USING (
        auth.uid() = user_id::uuid OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'legal_manager', 'legal_staff')
        )
    );

-- 用户可以创建自己的审查任务
CREATE POLICY "Users can insert own review tasks" ON review_tasks
    FOR INSERT WITH CHECK (auth.uid() = user_id::uuid);

-- 用户可以更新自己的审查任务，法务人员可以更新所有任务
CREATE POLICY "Users can update own review tasks" ON review_tasks
    FOR UPDATE USING (
        auth.uid() = user_id::uuid OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'legal_manager', 'legal_staff')
        )
    );

-- 5. 合同要素访问策略
-- 基于合同的访问权限
CREATE POLICY "Users can view contract elements" ON contract_elements
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM contracts 
            WHERE contracts.id = contract_elements.contract_id 
            AND (
                auth.uid() = contracts.user_id::uuid OR 
                EXISTS (
                    SELECT 1 FROM users 
                    WHERE id = auth.uid() 
                    AND role IN ('admin', 'legal_manager', 'legal_staff')
                )
            )
        )
    );

-- 法务人员可以插入合同要素
CREATE POLICY "Legal staff can insert contract elements" ON contract_elements
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'legal_manager', 'legal_staff')
        )
    );

-- 6. 风险项访问策略
-- 基于审查任务的访问权限
CREATE POLICY "Users can view risk items" ON risk_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM review_tasks 
            WHERE review_tasks.id = risk_items.review_task_id 
            AND (
                auth.uid() = review_tasks.user_id::uuid OR 
                EXISTS (
                    SELECT 1 FROM users 
                    WHERE id = auth.uid() 
                    AND role IN ('admin', 'legal_manager', 'legal_staff')
                )
            )
        )
    );

-- 法务人员可以插入风险项
CREATE POLICY "Legal staff can insert risk items" ON risk_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'legal_manager', 'legal_staff')
        )
    );

-- 7. 用户表访问策略
-- 用户可以查看自己的信息，管理员可以查看所有用户
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (
        auth.uid() = id OR 
        EXISTS (
            SELECT 1 FROM users u2 
            WHERE u2.id = auth.uid() 
            AND u2.role = 'admin'
        )
    );

-- 用户可以更新自己的信息
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- 只有管理员可以插入新用户
CREATE POLICY "Admin can insert users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );