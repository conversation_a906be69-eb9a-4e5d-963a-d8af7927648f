-- 为合同表添加性能优化索引
-- 创建时间：2024-01-18
-- 目的：优化合同查询性能，特别是搜索、过滤和分页操作

-- 1. 为经常用于搜索的字段创建索引
-- 标题搜索索引（支持模糊搜索）
CREATE INDEX IF NOT EXISTS idx_contracts_title_gin 
ON contracts USING gin(to_tsvector('english', title));

-- 内容搜索索引（支持模糊搜索）
CREATE INDEX IF NOT EXISTS idx_contracts_content_gin 
ON contracts USING gin(to_tsvector('english', COALESCE(content, '')));

-- OCR内容搜索索引（支持模糊搜索）
CREATE INDEX IF NOT EXISTS idx_contracts_ocr_content_gin 
ON contracts USING gin(to_tsvector('english', COALESCE(ocr_content, '')));

-- 2. 为经常用于过滤的字段创建B-tree索引
-- 状态过滤索引
CREATE INDEX IF NOT EXISTS idx_contracts_status 
ON contracts (status);

-- 合同类型过滤索引
CREATE INDEX IF NOT EXISTS idx_contracts_category 
ON contracts (category);

-- 创建者过滤索引
CREATE INDEX IF NOT EXISTS idx_contracts_user_id 
ON contracts (user_id);

-- 3. 为排序和分页创建复合索引
-- 按创建时间排序的复合索引（包含用户ID用于权限过滤）
CREATE INDEX IF NOT EXISTS idx_contracts_user_created 
ON contracts (user_id, created_at DESC);

-- 按创建时间排序的全局索引（管理员查看所有合同）
CREATE INDEX IF NOT EXISTS idx_contracts_created_at 
ON contracts (created_at DESC);

-- 4. 为日期范围查询创建索引
-- 创建时间范围查询索引
CREATE INDEX IF NOT EXISTS idx_contracts_created_at_range 
ON contracts (created_at);

-- 更新时间索引
CREATE INDEX IF NOT EXISTS idx_contracts_updated_at 
ON contracts (updated_at DESC);

-- 5. 为复杂查询创建复合索引
-- 状态 + 创建时间复合索引
CREATE INDEX IF NOT EXISTS idx_contracts_status_created 
ON contracts (status, created_at DESC);

-- 类型 + 创建时间复合索引
CREATE INDEX IF NOT EXISTS idx_contracts_category_created 
ON contracts (category, created_at DESC);

-- 用户 + 状态 + 创建时间复合索引（用户查看自己的合同）
CREATE INDEX IF NOT EXISTS idx_contracts_user_status_created 
ON contracts (user_id, status, created_at DESC);

-- 6. 为文本搜索优化创建复合GIN索引
-- 标题、内容和类型的联合文本搜索索引
CREATE INDEX IF NOT EXISTS idx_contracts_text_search 
ON contracts USING gin(
  to_tsvector('english', 
    COALESCE(title, '') || ' ' || 
    COALESCE(content, '') || ' ' || 
    COALESCE(ocr_content, '') || ' ' || 
    COALESCE(category, '')
  )
);

-- 7. 为统计查询优化
-- 状态统计索引
CREATE INDEX IF NOT EXISTS idx_contracts_status_count 
ON contracts (status) 
WHERE status IS NOT NULL;

-- 用户状态统计索引
CREATE INDEX IF NOT EXISTS idx_contracts_user_status_count 
ON contracts (user_id, status) 
WHERE status IS NOT NULL;

-- 添加注释说明索引用途
COMMENT ON INDEX idx_contracts_title_gin IS '合同标题全文搜索索引';
COMMENT ON INDEX idx_contracts_content_gin IS '合同内容全文搜索索引';
COMMENT ON INDEX idx_contracts_ocr_content_gin IS 'OCR内容全文搜索索引';
COMMENT ON INDEX idx_contracts_status IS '合同状态过滤索引';
COMMENT ON INDEX idx_contracts_category IS '合同类型过滤索引';
COMMENT ON INDEX idx_contracts_user_id IS '创建者过滤索引';
COMMENT ON INDEX idx_contracts_user_created IS '用户合同按时间排序索引';
COMMENT ON INDEX idx_contracts_created_at IS '全局合同按时间排序索引';
COMMENT ON INDEX idx_contracts_text_search IS '合同综合文本搜索索引';
COMMENT ON INDEX idx_contracts_status_count IS '合同状态统计优化索引';
COMMENT ON INDEX idx_contracts_user_status_count IS '用户合同状态统计优化索引';

-- 分析表以更新统计信息
ANALYZE contracts;