-- Create demo users in Supabase Auth with default passwords
-- This script creates authentication records for demo users

-- Insert demo users into auth.users table
-- Note: Supabase Auth requires specific format for user creation

-- Create admin user
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('admin123', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"name": "Admin User", "role": "admin"}',
    false,
    '',
    '',
    '',
    ''
) ON CONFLICT (id) DO NOTHING;

-- Create legal manager user
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    'b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a22',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('manager123', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"name": "Legal Manager", "role": "legal_manager"}',
    false,
    '',
    '',
    '',
    ''
) ON CONFLICT (id) DO NOTHING;

-- Create legal staff user
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    'c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a33',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('staff123', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"name": "Legal Staff", "role": "legal_staff"}',
    false,
    '',
    '',
    '',
    ''
) ON CONFLICT (id) DO NOTHING;

-- Create regular user
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    'd3eebc99-9c0b-4ef8-bb6d-6bb9bd380a44',
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('user123', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"name": "Regular User", "role": "user"}',
    false,
    '',
    '',
    '',
    ''
) ON CONFLICT (id) DO NOTHING;

-- Create corresponding identities for each user
INSERT INTO auth.identities (
    provider_id,
    user_id,
    identity_data,
    provider,
    last_sign_in_at,
    created_at,
    updated_at
) VALUES 
(
    'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
    'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
    '{"email": "<EMAIL>", "sub": "a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11"}',
    'email',
    NOW(),
    NOW(),
    NOW()
),
(
    'b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a22',
    'b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a22',
    '{"email": "<EMAIL>", "sub": "b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a22"}',
    'email',
    NOW(),
    NOW(),
    NOW()
),
(
    'c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a33',
    'c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a33',
    '{"email": "<EMAIL>", "sub": "c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a33"}',
    'email',
    NOW(),
    NOW(),
    NOW()
),
(
    'd3eebc99-9c0b-4ef8-bb6d-6bb9bd380a44',
    'd3eebc99-9c0b-4ef8-bb6d-6bb9bd380a44',
    '{"email": "<EMAIL>", "sub": "d3eebc99-9c0b-4ef8-bb6d-6bb9bd380a44"}',
    'email',
    NOW(),
    NOW(),
    NOW()
)
ON CONFLICT (provider_id, provider) DO NOTHING;

-- Update the public.users table to match the auth user IDs
UPDATE public.users SET id = 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11' WHERE email = '<EMAIL>';
UPDATE public.users SET id = 'b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a22' WHERE email = '<EMAIL>';
UPDATE public.users SET id = 'c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a33' WHERE email = '<EMAIL>';
UPDATE public.users SET id = 'd3eebc99-9c0b-4ef8-bb6d-6bb9bd380a44' WHERE email = '<EMAIL>';

-- Grant permissions to anon and authenticated roles for auth tables (if needed)
-- Note: These are system tables, permissions are usually managed by Supabase

COMMIT;