-- 演示数据迁移脚本
-- 创建演示用户、模板、知识库和审查规则数据

-- 插入演示用户数据
INSERT INTO users (id, email, name, role) VALUES
  ('550e8400-e29b-41d4-a716-************', '<EMAIL>', '系统管理员', 'admin'),
  ('550e8400-e29b-41d4-a716-************', '<EMAIL>', '法务经理', 'legal_manager'),
  ('550e8400-e29b-41d4-a716-************', '<EMAIL>', '法务专员', 'legal_staff'),
  ('550e8400-e29b-41d4-a716-************', '<EMAIL>', '普通用户', 'user')
ON CONFLICT (id) DO NOTHING;

-- 插入合同模板数据
INSERT INTO templates (id, name, category, content, parameters, is_active) VALUES
  (
    gen_random_uuid(),
    '销售合同模板',
    'sales',
    '甲方：{{party_a}}\n乙方：{{party_b}}\n\n合同金额：{{amount}}\n交付日期：{{delivery_date}}\n付款方式：{{payment_method}}\n\n特别条款：\n1. 质量保证期为{{warranty_period}}\n2. 违约金比例为合同金额的{{penalty_rate}}%\n3. 争议解决方式：{{dispute_resolution}}',
    '{"party_a": "甲方名称", "party_b": "乙方名称", "amount": "合同金额", "delivery_date": "交付日期", "payment_method": "付款方式", "warranty_period": "质保期", "penalty_rate": "违约金比例", "dispute_resolution": "争议解决方式"}',
    true
  ),
  (
    gen_random_uuid(),
    '服务合同模板',
    'service',
    '服务提供方：{{service_provider}}\n服务接受方：{{service_receiver}}\n\n服务内容：{{service_content}}\n服务期限：{{service_period}}\n服务费用：{{service_fee}}\n\n服务标准：\n1. 服务质量标准：{{quality_standard}}\n2. 响应时间要求：{{response_time}}\n3. 保密义务：{{confidentiality}}',
    '{"service_provider": "服务提供方", "service_receiver": "服务接受方", "service_content": "服务内容", "service_period": "服务期限", "service_fee": "服务费用", "quality_standard": "质量标准", "response_time": "响应时间", "confidentiality": "保密条款"}',
    true
  ),
  (
    gen_random_uuid(),
    '采购合同模板',
    'procurement',
    '采购方：{{buyer}}\n供应方：{{supplier}}\n\n采购物品：{{items}}\n采购数量：{{quantity}}\n单价：{{unit_price}}\n总价：{{total_price}}\n\n交付要求：\n1. 交付地点：{{delivery_location}}\n2. 交付时间：{{delivery_time}}\n3. 验收标准：{{acceptance_criteria}}',
    '{"buyer": "采购方", "supplier": "供应方", "items": "采购物品", "quantity": "数量", "unit_price": "单价", "total_price": "总价", "delivery_location": "交付地点", "delivery_time": "交付时间", "acceptance_criteria": "验收标准"}',
    true
  )
ON CONFLICT (id) DO NOTHING;

-- 插入知识库数据
INSERT INTO knowledge_base (id, type, title, content, metadata) VALUES
  (
    gen_random_uuid(),
    'regulation',
    '合同法基本原则',
    '根据《中华人民共和国合同法》，合同当事人应当遵循以下基本原则：\n1. 平等原则：合同当事人的法律地位平等\n2. 自愿原则：当事人依法享有自愿订立合同的权利\n3. 公平原则：当事人应当遵循公平原则确定各方的权利和义务\n4. 诚实信用原则：当事人行使权利、履行义务应当遵循诚实信用原则\n5. 合法原则：合同不得违反法律、行政法规的强制性规定',
    '{"source": "中华人民共和国合同法", "article": "第三条、第四条、第五条、第六条、第七条", "effective_date": "1999-10-01"}'
  ),
  (
    gen_random_uuid(),
    'regulation',
    '合同无效的情形',
    '有下列情形之一的，合同无效：\n1. 一方以欺诈、胁迫的手段订立合同，损害国家利益\n2. 恶意串通，损害国家、集体或者第三人利益\n3. 以合法形式掩盖非法目的\n4. 损害社会公共利益\n5. 违反法律、行政法规的强制性规定',
    '{"source": "中华人民共和国合同法", "article": "第五十二条", "risk_level": "high"}'
  ),
  (
    gen_random_uuid(),
    'guideline',
    '合同审查要点',
    '合同审查应重点关注以下要点：\n1. 主体资格：审查合同当事人的主体资格和签约能力\n2. 合同内容：审查合同条款是否完整、明确、合法\n3. 权利义务：审查双方权利义务是否对等、公平\n4. 违约责任：审查违约责任条款是否明确、合理\n5. 争议解决：审查争议解决方式是否明确、可执行\n6. 法律风险：识别潜在的法律风险并提出防范建议',
    '{"category": "审查指南", "priority": "high", "checklist": true}'
  ),
  (
    gen_random_uuid(),
    'template',
    '常见合同条款模板',
    '1. 保密条款模板：\n"双方应对在合同履行过程中知悉的对方商业秘密承担保密义务，保密期限为合同终止后{{保密期限}}年。"\n\n2. 知识产权条款模板：\n"因履行本合同而产生的知识产权归{{知识产权归属方}}所有。"\n\n3. 不可抗力条款模板：\n"因不可抗力导致合同无法履行的，受影响方应及时通知对方，并提供相关证明。"',
    '{"type": "clause_template", "usage": "common"}'
  )
ON CONFLICT (id) DO NOTHING;

-- 插入审查规则数据
INSERT INTO review_rules (id, knowledge_base_id, rule_name, rule_content, severity, is_active) VALUES
  (
    gen_random_uuid(),
    (SELECT id FROM knowledge_base WHERE title = '合同无效的情形' LIMIT 1),
    '检查合同主体资格',
    '审查合同当事人是否具备相应的民事权利能力和民事行为能力，法人或其他组织是否在经营范围内签订合同',
    'critical',
    true
  ),
  (
    gen_random_uuid(),
    (SELECT id FROM knowledge_base WHERE title = '合同法基本原则' LIMIT 1),
    '检查合同公平性',
    '审查合同条款是否显失公平，双方权利义务是否对等，是否存在明显不合理的条款',
    'warning',
    true
  ),
  (
    gen_random_uuid(),
    (SELECT id FROM knowledge_base WHERE title = '合同审查要点' LIMIT 1),
    '检查违约责任条款',
    '审查违约责任条款是否明确具体，违约金或损失赔偿的计算方式是否合理',
    'error',
    true
  ),
  (
    gen_random_uuid(),
    (SELECT id FROM knowledge_base WHERE title = '合同审查要点' LIMIT 1),
    '检查争议解决条款',
    '审查争议解决方式是否明确，管辖法院或仲裁机构的选择是否合理',
    'warning',
    true
  ),
  (
    gen_random_uuid(),
    (SELECT id FROM knowledge_base WHERE title = '常见合同条款模板' LIMIT 1),
    '检查保密条款',
    '审查是否包含必要的保密条款，保密范围和期限是否明确',
    'info',
    true
  ),
  (
    gen_random_uuid(),
    (SELECT id FROM knowledge_base WHERE title = '合同法基本原则' LIMIT 1),
    '检查合同合法性',
    '审查合同内容是否违反法律法规的强制性规定，是否损害国家、集体或第三人利益',
    'critical',
    true
  )
ON CONFLICT (id) DO NOTHING;

-- 插入演示合同数据
INSERT INTO contracts (id, user_id, title, category, content, status) VALUES
  (
    gen_random_uuid(),
    '550e8400-e29b-41d4-a716-************',
    '软件开发服务合同',
    'service',
    '甲方：ABC科技有限公司\n乙方：XYZ软件开发公司\n\n根据《中华人民共和国合同法》及相关法律法规，甲乙双方在平等、自愿、公平、诚信的基础上，就软件开发服务事宜达成如下协议：\n\n第一条 项目概述\n甲方委托乙方开发一套企业管理系统，包括用户管理、权限控制、数据统计等功能模块。\n\n第二条 开发周期\n项目开发周期为6个月，自合同签署之日起计算。\n\n第三条 合同金额\n合同总金额为人民币50万元整。\n\n第四条 付款方式\n1. 合同签署后支付30%预付款\n2. 项目完成50%后支付40%进度款\n3. 项目验收合格后支付剩余30%尾款\n\n第五条 知识产权\n项目开发的所有源代码及相关文档的知识产权归甲方所有。\n\n第六条 保密条款\n双方应对在合同履行过程中知悉的对方商业秘密承担保密义务。\n\n第七条 违约责任\n任何一方违约应承担违约责任，违约金为合同总金额的10%。\n\n第八条 争议解决\n因本合同引起的争议，双方应友好协商解决；协商不成的，提交北京仲裁委员会仲裁。',
    'uploaded'
  ),
  (
    gen_random_uuid(),
    '550e8400-e29b-41d4-a716-************',
    '设备采购合同',
    'procurement',
    '采购方：DEF制造有限公司\n供应方：GHI设备供应商\n\n第一条 采购物品\n采购方向供应方采购生产设备一套，具体规格见附件清单。\n\n第二条 合同金额\n设备总价为人民币80万元整。\n\n第三条 交付要求\n1. 交付地点：采购方指定工厂\n2. 交付时间：合同签署后3个月内\n3. 运输费用由供应方承担\n\n第四条 质量保证\n设备质量保证期为2年，保证期内免费维修。\n\n第五条 验收标准\n设备到货后，采购方有15天验收期，验收合格后签署验收单。\n\n第六条 付款条件\n1. 合同签署后支付20%预付款\n2. 设备到货验收合格后支付70%\n3. 质保期满后支付剩余10%\n\n第七条 违约责任\n供应方延期交付的，每延期一天支付合同金额0.5‰的违约金。',
    'uploaded'
  )
ON CONFLICT (id) DO NOTHING;

-- 更新统计信息
ANALYZE users;
ANALYZE contracts;
ANALYZE templates;
ANALYZE knowledge_base;
ANALYZE review_rules;