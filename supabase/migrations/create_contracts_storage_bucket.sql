-- 创建合同文件存储bucket
-- Create contracts storage bucket

-- 创建contracts bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('contracts', 'contracts', false);

-- 为contracts bucket创建RLS策略
-- Create RLS policies for contracts bucket

-- 允许认证用户上传文件
CREATE POLICY "Allow authenticated users to upload files" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'contracts' AND 
  auth.role() = 'authenticated'
);

-- 允许认证用户查看自己上传的文件或与自己相关的合同文件
CREATE POLICY "Allow authenticated users to view files" ON storage.objects
FOR SELECT USING (
  bucket_id = 'contracts' AND 
  auth.role() = 'authenticated'
);

-- 允许认证用户更新自己上传的文件
CREATE POLICY "Allow authenticated users to update their files" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'contracts' AND 
  auth.role() = 'authenticated'
);

-- 允许认证用户删除自己上传的文件
CREATE POLICY "Allow authenticated users to delete their files" ON storage.objects
FOR DELETE USING (
  bucket_id = 'contracts' AND 
  auth.role() = 'authenticated'
);

-- 为anon角色授予基本权限（如果需要匿名访问）
GRANT SELECT ON storage.objects TO anon;
GRANT INSERT ON storage.objects TO anon;

-- 为authenticated角色授予完整权限
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;