-- 为所有数据库表和字段添加完整的中文注释
-- 创建时间: 2024-01-18
-- 目的: 解决数据库表结构缺少中文注释的问题

-- 为 users 表添加注释
COMMENT ON TABLE users IS '用户表 - 存储系统用户信息';
COMMENT ON COLUMN users.id IS '用户唯一标识符';
COMMENT ON COLUMN users.email IS '用户邮箱地址';
COMMENT ON COLUMN users.name IS '用户姓名';
COMMENT ON COLUMN users.role IS '用户角色: user(普通用户), legal_staff(法务专员), legal_manager(法务经理), admin(管理员)';
COMMENT ON COLUMN users.created_at IS '用户创建时间';
COMMENT ON COLUMN users.updated_at IS '用户信息最后更新时间';

-- 为 contracts 表添加注释
COMMENT ON TABLE contracts IS '合同表 - 存储合同基本信息和内容';
COMMENT ON COLUMN contracts.id IS '合同唯一标识符';
COMMENT ON COLUMN contracts.title IS '合同标题';
COMMENT ON COLUMN contracts.category IS '合同类别: service(服务合同)、purchase(采购合同)、sales(销售合同)、lease(租赁合同)、employment(劳动合同)、general(通用合同)';
COMMENT ON COLUMN contracts.content IS '合同正文内容';
COMMENT ON COLUMN contracts.status IS '合同状态: draft(草稿), reviewing(审查中), approved(已批准), rejected(已拒绝), signed(已签署), expired(已过期), uploaded(已上传), processing(处理中), reviewed(已审查), archived(已归档)';
COMMENT ON COLUMN contracts.file_path IS '合同文件存储路径';
COMMENT ON COLUMN contracts.file_url IS '合同文件访问URL';
COMMENT ON COLUMN contracts.ocr_content IS 'OCR识别的合同文本内容';
COMMENT ON COLUMN contracts.counterparty IS '合同对方当事人名称';
COMMENT ON COLUMN contracts.amount IS '合同金额';
COMMENT ON COLUMN contracts.start_date IS '合同开始日期';
COMMENT ON COLUMN contracts.end_date IS '合同结束日期';
COMMENT ON COLUMN contracts.risk_level IS '风险等级: low(低风险), medium(中等风险), high(高风险), critical(严重风险)';
COMMENT ON COLUMN contracts.created_at IS '合同创建时间';
COMMENT ON COLUMN contracts.updated_at IS '合同最后更新时间';
COMMENT ON COLUMN contracts.user_id IS '创建合同的用户ID';

-- 为 templates 表添加注释
COMMENT ON TABLE templates IS '合同模板表 - 存储各类合同模板';
COMMENT ON COLUMN templates.id IS '模板唯一标识符';
COMMENT ON COLUMN templates.name IS '模板名称';
COMMENT ON COLUMN templates.category IS '模板类别: 采购模板、销售模板、服务模板等';
COMMENT ON COLUMN templates.content IS '模板内容';
COMMENT ON COLUMN templates.parameters IS '模板参数配置(JSON格式)';
COMMENT ON COLUMN templates.is_active IS '模板是否启用';
COMMENT ON COLUMN templates.created_at IS '模板创建时间';

-- 为 review_tasks 表添加注释
COMMENT ON TABLE review_tasks IS '审查任务表 - 存储合同审查任务信息';
COMMENT ON COLUMN review_tasks.id IS '审查任务唯一标识符';
COMMENT ON COLUMN review_tasks.contract_id IS '关联的合同ID';
COMMENT ON COLUMN review_tasks.user_id IS '审查员用户ID';
COMMENT ON COLUMN review_tasks.review_type IS '审查类型: full(全面审查), quick(快速审查), legal(法律审查), financial(财务审查)';
COMMENT ON COLUMN review_tasks.analysis_result IS '审查分析结果(JSON格式)';
COMMENT ON COLUMN review_tasks.status IS '审查状态: pending(待审查), processing(处理中), completed(已完成), failed(失败)';
COMMENT ON COLUMN review_tasks.created_at IS '任务创建时间';
COMMENT ON COLUMN review_tasks.completed_at IS '任务完成时间';

-- 为 contract_elements 表添加注释
COMMENT ON TABLE contract_elements IS '合同要素表 - 存储合同中提取的关键要素';
COMMENT ON COLUMN contract_elements.id IS '要素唯一标识符';
COMMENT ON COLUMN contract_elements.contract_id IS '关联的合同ID';
COMMENT ON COLUMN contract_elements.element_type IS '要素类型: party(当事人), amount(金额), date(日期), term(条款), obligation(义务)等';
COMMENT ON COLUMN contract_elements.element_value IS '要素值';
COMMENT ON COLUMN contract_elements.confidence_score IS '置信度分数(0-1)';
COMMENT ON COLUMN contract_elements.extracted_at IS '要素提取时间';

-- 为 risk_items 表添加注释
COMMENT ON TABLE risk_items IS '风险项表 - 存储合同审查中发现的风险项';
COMMENT ON COLUMN risk_items.id IS '风险项唯一标识符';
COMMENT ON COLUMN risk_items.review_task_id IS '关联的审查任务ID';
COMMENT ON COLUMN risk_items.risk_type IS '风险类型: legal(法律风险), financial(财务风险), operational(运营风险), compliance(合规风险)';
COMMENT ON COLUMN risk_items.risk_level IS '风险等级: low(低风险), medium(中等风险), high(高风险), critical(严重风险)';
COMMENT ON COLUMN risk_items.description IS '风险描述';
COMMENT ON COLUMN risk_items.suggestion IS '风险处理建议';
COMMENT ON COLUMN risk_items.identified_at IS '风险项识别时间';

-- 为 review_rules 表添加注释
COMMENT ON TABLE review_rules IS '审查规则表 - 存储合同审查的规则配置';
COMMENT ON COLUMN review_rules.id IS '规则唯一标识符';
COMMENT ON COLUMN review_rules.knowledge_base_id IS '关联的知识库ID';
COMMENT ON COLUMN review_rules.rule_name IS '规则名称';
COMMENT ON COLUMN review_rules.rule_content IS '规则内容';
COMMENT ON COLUMN review_rules.severity IS '严重程度: info(信息), warning(警告), error(错误), critical(严重)';
COMMENT ON COLUMN review_rules.is_active IS '规则是否启用';

-- 为 knowledge_base 表添加注释
COMMENT ON TABLE knowledge_base IS '知识库表 - 存储法律法规和审查知识';
COMMENT ON COLUMN knowledge_base.id IS '知识条目唯一标识符';
COMMENT ON COLUMN knowledge_base.type IS '知识类型: regulation(法规), case_law(案例法), template(模板), guideline(指导原则)';
COMMENT ON COLUMN knowledge_base.title IS '知识条目标题';
COMMENT ON COLUMN knowledge_base.content IS '知识内容';
COMMENT ON COLUMN knowledge_base.metadata IS '元数据(JSON格式)';
COMMENT ON COLUMN knowledge_base.created_at IS '知识条目创建时间';
COMMENT ON COLUMN knowledge_base.updated_at IS '知识条目最后更新时间';