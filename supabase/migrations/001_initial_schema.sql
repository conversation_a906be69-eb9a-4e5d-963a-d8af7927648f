-- 创建用户表
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'legal_staff', 'legal_manager', 'admin')),
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建合同表
CREATE TABLE IF NOT EXISTS public.contracts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  type VARCHAR(100) NOT NULL,
  content TEXT,
  description TEXT,
  status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'pending_review', 'under_review', 'approved', 'rejected', 'archived')),
  file_name VA<PERSON>HAR(255),
  file_path TEXT,
  file_size INTEGER,
  template_id UUID,
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建审查任务表
CREATE TABLE IF NOT EXISTS public.review_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contract_id UUID NOT NULL REFERENCES public.contracts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  review_type VARCHAR(50) DEFAULT 'full' CHECK (review_type IN ('full', 'quick', 'custom')),
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  risk_level VARCHAR(20) CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
  risk_score INTEGER CHECK (risk_score >= 0 AND risk_score <= 100),
  focus_areas TEXT[],
  custom_rules JSONB,
  result JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- 创建合同要素表
CREATE TABLE IF NOT EXISTS public.contract_elements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contract_id UUID NOT NULL REFERENCES public.contracts(id) ON DELETE CASCADE,
  element_type VARCHAR(100) NOT NULL,
  element_name VARCHAR(255) NOT NULL,
  content TEXT,
  position_start INTEGER,
  position_end INTEGER,
  confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
  is_standard BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建风险项表
CREATE TABLE IF NOT EXISTS public.risk_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  contract_id UUID NOT NULL REFERENCES public.contracts(id) ON DELETE CASCADE,
  review_task_id UUID REFERENCES public.review_tasks(id) ON DELETE CASCADE,
  risk_type VARCHAR(100) NOT NULL,
  risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
  description TEXT NOT NULL,
  suggestion TEXT,
  position_start INTEGER,
  position_end INTEGER,
  confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
  is_resolved BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建模板表
CREATE TABLE IF NOT EXISTS public.templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  type VARCHAR(100) NOT NULL,
  content TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建知识库表
CREATE TABLE IF NOT EXISTS public.knowledge_base (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  category VARCHAR(100) NOT NULL,
  tags TEXT[],
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建审查规则表
CREATE TABLE IF NOT EXISTS public.review_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  rule_type VARCHAR(100) NOT NULL,
  severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  conditions JSONB,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_contracts_user_id ON public.contracts(user_id);
CREATE INDEX IF NOT EXISTS idx_contracts_status ON public.contracts(status);
CREATE INDEX IF NOT EXISTS idx_contracts_type ON public.contracts(type);
CREATE INDEX IF NOT EXISTS idx_contracts_created_at ON public.contracts(created_at);

CREATE INDEX IF NOT EXISTS idx_review_tasks_contract_id ON public.review_tasks(contract_id);
CREATE INDEX IF NOT EXISTS idx_review_tasks_user_id ON public.review_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_review_tasks_status ON public.review_tasks(status);
CREATE INDEX IF NOT EXISTS idx_review_tasks_created_at ON public.review_tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_contract_elements_contract_id ON public.contract_elements(contract_id);
CREATE INDEX IF NOT EXISTS idx_contract_elements_type ON public.contract_elements(element_type);

CREATE INDEX IF NOT EXISTS idx_risk_items_contract_id ON public.risk_items(contract_id);
CREATE INDEX IF NOT EXISTS idx_risk_items_review_task_id ON public.risk_items(review_task_id);
CREATE INDEX IF NOT EXISTS idx_risk_items_risk_level ON public.risk_items(risk_level);

CREATE INDEX IF NOT EXISTS idx_templates_type ON public.templates(type);
CREATE INDEX IF NOT EXISTS idx_templates_is_active ON public.templates(is_active);

CREATE INDEX IF NOT EXISTS idx_knowledge_base_category ON public.knowledge_base(category);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_is_active ON public.knowledge_base(is_active);

CREATE INDEX IF NOT EXISTS idx_review_rules_rule_type ON public.review_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_review_rules_is_active ON public.review_rules(is_active);

-- 启用行级安全策略 (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.contract_elements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.risk_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.knowledge_base ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.review_rules ENABLE ROW LEVEL SECURITY;

-- 创建基础RLS策略
-- 用户表策略
CREATE POLICY "Users can view their own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- 合同表策略
CREATE POLICY "Users can view their own contracts" ON public.contracts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create contracts" ON public.contracts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own contracts" ON public.contracts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own contracts" ON public.contracts
  FOR DELETE USING (auth.uid() = user_id);

-- 审查任务表策略
CREATE POLICY "Users can view their own review tasks" ON public.review_tasks
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create review tasks" ON public.review_tasks
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own review tasks" ON public.review_tasks
  FOR UPDATE USING (auth.uid() = user_id);

-- 合同要素表策略
CREATE POLICY "Users can view contract elements for their contracts" ON public.contract_elements
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.contracts 
      WHERE contracts.id = contract_elements.contract_id 
      AND contracts.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create contract elements for their contracts" ON public.contract_elements
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.contracts 
      WHERE contracts.id = contract_elements.contract_id 
      AND contracts.user_id = auth.uid()
    )
  );

-- 风险项表策略
CREATE POLICY "Users can view risk items for their contracts" ON public.risk_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.contracts 
      WHERE contracts.id = risk_items.contract_id 
      AND contracts.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create risk items for their contracts" ON public.risk_items
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.contracts 
      WHERE contracts.id = risk_items.contract_id 
      AND contracts.user_id = auth.uid()
    )
  );

-- 模板表策略（所有用户可查看活跃模板）
CREATE POLICY "Users can view active templates" ON public.templates
  FOR SELECT USING (is_active = true);

-- 知识库表策略（所有用户可查看活跃知识库）
CREATE POLICY "Users can view active knowledge base" ON public.knowledge_base
  FOR SELECT USING (is_active = true);

-- 审查规则表策略（所有用户可查看活跃规则）
CREATE POLICY "Users can view active review rules" ON public.review_rules
  FOR SELECT USING (is_active = true);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contracts_updated_at BEFORE UPDATE ON public.contracts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON public.templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_knowledge_base_updated_at BEFORE UPDATE ON public.knowledge_base
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_review_rules_updated_at BEFORE UPDATE ON public.review_rules
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();