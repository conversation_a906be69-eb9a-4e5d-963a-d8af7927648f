# 合同管理模块搜索功能重构

## 重构概述

重构了合同管理模块的搜索功能，从复杂的多字段搜索简化为仅支持合同标题的模糊查询。

## 重构前的问题

1. **复杂的多字段搜索**：搜索会在 `title`、`content`、`ocr_content`、`category`、`counterparty` 多个字段中进行
2. **性能问题**：使用复杂的 `OR` 查询，可能影响数据库性能
3. **用户体验不佳**：搜索范围太广，用户难以精确找到想要的合同
4. **维护复杂**：包含了全文搜索、GIN索引等复杂功能

## 重构后的改进

### 1. 简化搜索逻辑

**前端变化**：
- 搜索框标签：`搜索关键词` → `搜索合同标题`
- 占位符文本：`搜索合同标题、对方当事人、合同内容` → `请输入合同标题关键词`

**后端变化**：
```typescript
// 重构前：复杂的多字段搜索
query = query.or(
  `title.ilike.%${search}%,` +
  `content.ilike.%${search}%,` +
  `ocr_content.ilike.%${search}%,` +
  `category.ilike.%${search}%,` +
  `counterparty.ilike.%${search}%`
);

// 重构后：简单的标题搜索
query = query.ilike('title', `%${searchTerm}%`);
```

### 2. 性能优化

- **查询简化**：从复杂的 `OR` 查询简化为单字段 `ILIKE` 查询
- **索引友好**：单字段查询更容易利用数据库索引
- **响应速度**：减少了数据库查询的复杂度

### 3. 用户体验改进

- **明确的搜索范围**：用户清楚知道只搜索合同标题
- **精确的搜索结果**：避免了不相关内容的干扰
- **简单易用**：降低了用户的学习成本

## 技术实现

### 修改的文件

1. **api/services/supabaseService.ts**
   - `getContracts()` 方法：简化搜索逻辑
   - `getAllContracts()` 方法：简化搜索逻辑

2. **api/services/contractService.ts**
   - `getContracts()` 方法：简化模拟数据搜索逻辑

3. **api/routes/contracts.ts**
   - 添加搜索功能的注释说明

4. **src/pages/Contracts.tsx**
   - 更新搜索框的标签和占位符文本

### 搜索功能特性

- **模糊匹配**：支持部分关键词匹配
- **大小写不敏感**：使用 `ILIKE` 操作符
- **前后匹配**：使用 `%keyword%` 模式
- **空值处理**：自动过滤空白字符

## 使用示例

### API 调用
```bash
GET /api/contracts?search=服务合同&page=1&limit=10
```

### 前端使用
```typescript
// 搜索包含"服务"的合同标题
setSearchText('服务');
fetchContracts(1, pageSize);
```

## 搜索交互优化

### 问题解决
**原问题**：每次输入都会触发搜索请求，用户体验不佳

**根本原因**：
- `fetchContracts` 函数的依赖数组包含 `searchText`
- 每次输入时 `searchText` 变化 → `fetchContracts` 重新创建 → `useEffect` 触发 → 执行搜索

**解决方案**：
1. **参数化搜索关键词**：将搜索关键词作为参数传递，而不是依赖状态
2. **移除依赖**：从 `fetchContracts` 的依赖数组中移除 `searchText`
3. **按需搜索**：只在点击搜索按钮或按Enter键时传递搜索关键词
4. **保持其他筛选**：状态、类型、日期筛选仍然是实时的
5. **清空处理**：点击清空按钮时传递空字符串

### 技术实现
```typescript
// 修改前：依赖searchText导致每次输入都重新创建函数
const fetchContracts = useCallback(async (page, limit) => {
  // 使用 searchText 状态
  if (searchText) params.append('search', searchText);
}, [currentPage, pageSize, searchText, statusFilter, typeFilter, dateRange, token]);

// 修改后：通过参数传递搜索关键词，移除searchText依赖
const fetchContracts = useCallback(async (page, limit, searchKeyword = '') => {
  // 使用传入的 searchKeyword 参数
  if (searchKeyword) params.append('search', searchKeyword);
}, [currentPage, pageSize, statusFilter, typeFilter, dateRange, token]);

// 搜索按钮点击时传递关键词
onSearch={(value) => {
  fetchContracts(1, pageSize, value); // 传递搜索关键词
}}

// 其他操作时传递空字符串或当前搜索状态
fetchContracts(1, pageSize, ''); // 筛选条件变化时不使用搜索
fetchContracts(page, newPageSize, searchText); // 分页时保持搜索状态
```

### 用户交互流程
1. **输入关键词**：用户在搜索框中输入，不会触发搜索
2. **点击搜索**：点击搜索按钮或按Enter键才执行搜索
3. **清空搜索**：点击清空按钮立即刷新列表
4. **其他筛选**：状态、类型、日期筛选仍然实时生效

## 后续优化建议

1. **搜索历史**：可以考虑添加搜索历史功能
2. **搜索建议**：基于现有合同标题提供搜索建议
3. **高级搜索**：如果需要，可以添加独立的高级搜索功能
4. **搜索统计**：记录搜索关键词的使用频率
5. **键盘快捷键**：支持Ctrl+F快速聚焦搜索框

## 兼容性说明

- **向后兼容**：API 接口保持不变，只是搜索范围缩小
- **前端兼容**：搜索组件的使用方式保持不变
- **数据库兼容**：不需要修改数据库结构

## 测试建议

1. **功能测试**：验证搜索结果的准确性
2. **性能测试**：对比重构前后的查询性能
3. **用户体验测试**：收集用户对新搜索功能的反馈
