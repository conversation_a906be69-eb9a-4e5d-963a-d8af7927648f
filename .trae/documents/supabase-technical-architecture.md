# Supabase迁移后技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户浏览器] --> B[React前端应用]
    B --> C[Supabase客户端SDK]
    B --> D[Express后端API]
    D --> E[Supabase服务端SDK]
    C --> F[Supabase服务]
    E --> F[Supabase服务]
    
    subgraph "前端层"
        B
        C
    end
    
    subgraph "后端层"
        D
        E
    end
    
    subgraph "数据服务层 (Supabase)"
        F
        G[PostgreSQL数据库]
        H[Supabase Auth]
        I[实时订阅]
        J[存储服务]
        F --> G
        F --> H
        F --> I
        F --> J
    end
end
```

## 2. 技术描述

### 2.1 核心技术栈

* **前端**: React\@18 + TypeScript + Tailwind CSS + Vite

* **后端**: Express\@4 + TypeScript + Node.js

* **数据库**: Supabase (PostgreSQL)

* **认证**: Supabase Auth + JWT

* **实时功能**: Supabase Realtime

* **文件存储**: Supabase Storage

### 2.2 关键依赖包

```json
{
  "dependencies": {
    "@supabase/supabase-js": "^2.39.0",
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "jsonwebtoken": "^9.0.2",
    "bcrypt": "^5.1.1"
  }
}
```

## 3. 路由定义

### 3.1 前端路由

| 路由             | 用途               | 权限要求            |
| -------------- | ---------------- | --------------- |
| /              | 首页，显示数据概览和导航     | 需要登录            |
| /login         | 登录页面，用户认证        | 公开访问            |
| /register      | 注册页面，新用户注册       | 公开访问            |
| /contracts     | 合同管理页面，显示合同列表和操作 | 需要登录            |
| /contracts/:id | 合同详情页面，显示单个合同信息  | 需要登录            |
| /review        | 智能审查页面，上传和审查合同   | 需要登录            |
| /review/:id    | 审查结果页面，显示审查结果详情  | 需要登录            |
| /compare       | 条款对比页面，对比不同合同条款  | 需要登录            |
| /draft         | AI起草页面，使用模板生成合同  | 需要登录            |
| /knowledge     | 知识库页面，管理法律知识和规则  | legal\_staff及以上 |
| /profile       | 用户资料页面，显示和编辑用户信息 | 需要登录            |

### 3.2 API路由

| 路由                  | 方法     | 用途      | 权限要求            |
| ------------------- | ------ | ------- | --------------- |
| /api/auth/login     | POST   | 用户登录    | 公开访问            |
| /api/auth/register  | POST   | 用户注册    | 公开访问            |
| /api/auth/refresh   | POST   | 刷新token | 需要token         |
| /api/auth/logout    | POST   | 用户登出    | 需要token         |
| /api/contracts      | GET    | 获取合同列表  | 需要认证            |
| /api/contracts      | POST   | 创建新合同   | 需要认证            |
| /api/contracts/:id  | GET    | 获取合同详情  | 需要认证            |
| /api/contracts/:id  | PUT    | 更新合同信息  | 需要认证            |
| /api/contracts/:id  | DELETE | 删除合同    | 需要认证            |
| /api/review/start   | POST   | 开始智能审查  | 需要认证            |
| /api/review/:id     | GET    | 获取审查结果  | 需要认证            |
| /api/review/compare | POST   | 条款对比分析  | 需要认证            |
| /api/templates      | GET    | 获取合同模板  | 需要认证            |
| /api/knowledge      | GET    | 获取知识库内容 | 需要认证            |
| /api/knowledge      | POST   | 添加知识库条目 | legal\_staff及以上 |
| /api/statistics     | GET    | 获取统计数据  | 需要认证            |

## 4. API定义

### 4.1 认证相关API

#### 用户登录

```
POST /api/auth/login
```

**请求参数:**

| 参数名      | 参数类型   | 是否必需 | 描述   |
| -------- | ------ | ---- | ---- |
| email    | string | true | 用户邮箱 |
| password | string | true | 用户密码 |

**响应参数:**

| 参数名     | 参数类型    | 描述      |
| ------- | ------- | ------- |
| success | boolean | 登录是否成功  |
| token   | string  | JWT访问令牌 |
| user    | object  | 用户信息对象  |
| message | string  | 响应消息    |

**请求示例:**

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例:**

```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "系统管理员",
    "role": "admin"
  },
  "message": "登录成功"
}
```

#### 用户注册

```
POST /api/auth/register
```

**请求参数:**

| 参数名      | 参数类型   | 是否必需  | 描述             |
| -------- | ------ | ----- | -------------- |
| email    | string | true  | 用户邮箱           |
| password | string | true  | 用户密码           |
| name     | string | true  | 用户姓名           |
| role     | string | false | 用户角色，默认为'user' |

### 4.2 合同管理API

#### 获取合同列表

```
GET /api/contracts
```

**查询参数:**

| 参数名      | 参数类型   | 是否必需  | 描述        |
| -------- | ------ | ----- | --------- |
| page     | number | false | 页码，默认1    |
| limit    | number | false | 每页数量，默认10 |
| status   | string | false | 合同状态筛选    |
| category | string | false | 合同类别筛选    |

**响应参数:**

| 参数名        | 参数类型   | 描述   |
| ---------- | ------ | ---- |
| contracts  | array  | 合同列表 |
| total      | number | 总数量  |
| page       | number | 当前页码 |
| totalPages | number | 总页数  |

#### 创建合同

```
POST /api/contracts
```

**请求参数:**

| 参数名        | 参数类型   | 是否必需  | 描述   |
| ---------- | ------ | ----- | ---- |
| title      | string | true  | 合同标题 |
| category   | string | true  | 合同类别 |
| content    | string | false | 合同内容 |
| file\_path | string | false | 文件路径 |

### 4.3 智能审查API

#### 开始智能审查

```
POST /api/review/start
```

**请求参数:**

| 参数名          | 参数类型   | 是否必需 | 描述   |
| ------------ | ------ | ---- | ---- |
| contract\_id | string | true | 合同ID |
| review\_type | string | true | 审查类型 |

**响应参数:**

| 参数名             | 参数类型   | 描述        |
| --------------- | ------ | --------- |
| task\_id        | string | 审查任务ID    |
| status          | string | 任务状态      |
| estimated\_time | number | 预估完成时间(秒) |

## 5. 服务器架构图

```mermaid
graph TD
    A[客户端请求] --> B[Express路由层]
    B --> C[认证中间件]
    C --> D[业务逻辑层]
    D --> E[数据访问层]
    E --> F[Supabase客户端]
    F --> G[Supabase服务]
    
    subgraph "Express服务器"
        B
        C
        D
        E
        F
    end
    
    subgraph "中间件层"
        H[CORS中间件]
        I[JSON解析中间件]
        J[错误处理中间件]
        C
    end
    
    subgraph "服务层"
        K[认证服务]
        L[合同服务]
        M[审查服务]
        N[统计服务]
    end
    
    D --> K
    D --> L
    D --> M
    D --> N
end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    USERS ||--o{ CONTRACTS : creates
    USERS ||--o{ REVIEW_TASKS : performs
    CONTRACTS ||--o{ REVIEW_TASKS : has
    CONTRACTS ||--o{ CONTRACT_ELEMENTS : contains
    REVIEW_TASKS ||--o{ RISK_ITEMS : identifies
    KNOWLEDGE_BASE ||--o{ REVIEW_RULES : defines
    
    USERS {
        uuid id PK
        string email UK
        string name
        string role
        timestamp created_at
        timestamp updated_at
    }
    
    CONTRACTS {
        uuid id PK
        uuid user_id FK
        string title
        string category
        string file_path
        text content
        text ocr_content
        string status
        timestamp created_at
        timestamp updated_at
    }
    
    REVIEW_TASKS {
        uuid id PK
        uuid contract_id FK
        uuid user_id FK
        string review_type
        jsonb analysis_result
        string status
        timestamp created_at
        timestamp completed_at
    }
    
    CONTRACT_ELEMENTS {
        uuid id PK
        uuid contract_id FK
        string element_type
        text element_value
        decimal confidence_score
        timestamp extracted_at
    }
    
    RISK_ITEMS {
        uuid id PK
        uuid review_task_id FK
        string risk_type
        string risk_level
        text description
        text suggestion
        timestamp identified_at
    }
    
    TEMPLATES {
        uuid id PK
        string name
        string category
        text content
        jsonb parameters
        boolean is_active
        timestamp created_at
    }
    
    KNOWLEDGE_BASE {
        uuid id PK
        string type
        string title
        text content
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }
    
    REVIEW_RULES {
        uuid id PK
        uuid knowledge_base_id FK
        string rule_name
        text rule_content
        string severity
        boolean is_active
    }
```

### 6.2 数据定义语言 (DDL)

#### 用户表 (users)

```sql
-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'legal_staff', 'legal_manager', 'admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 初始化演示数据
INSERT INTO users (email, name, role) VALUES
('<EMAIL>', '系统管理员', 'admin'),
('<EMAIL>', '法务主管', 'legal_manager'),
('<EMAIL>', '法务专员', 'legal_staff');
```

#### 合同表 (contracts)

```sql
-- 创建合同表
CREATE TABLE contracts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    file_path VARCHAR(500),
    content TEXT,
    ocr_content TEXT,
    status VARCHAR(20) DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'processing', 'reviewed', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_contracts_user_id ON contracts(user_id);
CREATE INDEX idx_contracts_status ON contracts(status);
CREATE INDEX idx_contracts_category ON contracts(category);
CREATE INDEX idx_contracts_created_at ON contracts(created_at DESC);

-- 创建更新时间触发器
CREATE TRIGGER update_contracts_updated_at BEFORE UPDATE ON contracts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 审查任务表 (review\_tasks)

```sql
-- 创建审查任务表
CREATE TABLE review_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID NOT NULL,
    user_id UUID NOT NULL,
    review_type VARCHAR(100) NOT NULL,
    analysis_result JSONB,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 创建索引
CREATE INDEX idx_review_tasks_contract_id ON review_tasks(contract_id);
CREATE INDEX idx_review_tasks_user_id ON review_tasks(user_id);
CREATE INDEX idx_review_tasks_status ON review_tasks(status);
CREATE INDEX idx_review_tasks_created_at ON review_tasks(created_at DESC);
```

#### 模板表 (templates)

```sql
-- 创建模板表
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    parameters JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_templates_category ON templates(category);
CREATE INDEX idx_templates_active ON templates(is_active);

-- 初始化模板数据
INSERT INTO templates (name, category, content, parameters, is_active) VALUES
('标准服务合同模板', '服务合同', '甲方：[甲方名称]\n乙方：[乙方名称]\n\n根据《中华人民共和国合同法》等相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就服务事宜达成如下协议：\n\n第一条 服务内容\n乙方向甲方提供[服务内容]服务。\n\n第二条 服务期限\n服务期限为[服务期限]，自[开始日期]起至[结束日期]止。\n\n第三条 服务费用\n服务费用总计人民币[金额]元（大写：[大写金额]）。\n\n第四条 付款方式\n[付款方式说明]\n\n第五条 违约责任\n[违约责任条款]\n\n第六条 争议解决\n因履行本合同发生的争议，双方应友好协商解决；协商不成的，提交[仲裁机构]仲裁。\n\n第七条 其他\n本合同一式两份，甲乙双方各执一份，具有同等法律效力。\n\n甲方（盖章）：\n法定代表人：\n日期：\n\n乙方（盖章）：\n法定代表人：\n日期：', '{"fields": ["甲方名称", "乙方名称", "服务内容", "服务期限", "开始日期", "结束日期", "金额", "大写金额", "付款方式", "违约责任条款", "仲裁机构"]}', true),
('采购合同模板', '采购合同', '采购方：[采购方名称]\n供应方：[供应方名称]\n\n根据《中华人民共和国合同法》等相关法律法规，采购方与供应方就货物采购事宜达成如下协议：\n\n第一条 货物信息\n货物名称：[货物名称]\n规格型号：[规格型号]\n数量：[数量]\n单价：[单价]\n总价：[总价]\n\n第二条 交货\n交货地点：[交货地点]\n交货时间：[交货时间]\n\n第三条 质量标准\n[质量标准说明]\n\n第四条 验收\n[验收条款]\n\n第五条 付款\n[付款条款]\n\n第六条 违约责任\n[违约责任条款]\n\n采购方（盖章）：\n日期：\n\n供应方（盖章）：\n日期：', '{"fields": ["采购方名称", "供应方名称", "货物名称", "规格型号", "数量", "单价", "总价", "交货地点", "交货时间", "质量标准说明", "验收条款", "付款条款", "违约责任条款"]}', true);
```

#### 知识库表 (knowledge\_base)

```sql
-- 创建知识库表
CREATE TABLE knowledge_base (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL CHECK (type IN ('regulation', 'case_law', 'template', 'guideline')),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_knowledge_base_type ON knowledge_base(type);
CREATE INDEX idx_knowledge_base_title ON knowledge_base(title);

-- 创建更新时间触发器
CREATE TRIGGER update_knowledge_base_updated_at BEFORE UPDATE ON knowledge_base
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 初始化知识库数据
INSERT INTO knowledge_base (type, title, content, metadata) VALUES
('regulation', '合同法基本原则', '《中华人民共和国民法典》合同编规定了合同的基本原则：\n1. 平等原则：当事人在合同中的地位平等\n2. 自愿原则：当事人依法享有自愿订立合同的权利\n3. 公平原则：当事人应当遵循公平原则确定各方的权利和义务\n4. 诚实信用原则：当事人行使权利、履行义务应当遵循诚实信用原则\n5. 守法原则：当事人订立、履行合同，应当遵守法律、行政法规', '{"source": "中华人民共和国民法典", "effective_date": "2021-01-01"}'),
('guideline', '合同审查指导原则', '合同审查的基本指导原则：\n1. 合法性审查：确保合同内容符合法律法规\n2. 完整性审查：检查合同条款是否完整\n3. 明确性审查：确保合同条款表述清晰明确\n4. 可执行性审查：评估合同条款的可执行性\n5. 风险识别：识别潜在的法律风险和商业风险\n6. 利益平衡：确保各方权利义务平衡', '{"version": "1.0", "department": "法务部"}');
```

#### 审查规则表 (review\_rules)

```sql
-- 创建审查规则表
CREATE TABLE review_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    knowledge_base_id UUID NOT NULL,
    rule_name VARCHAR(255) NOT NULL,
    rule_content TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    is_active BOOLEAN DEFAULT true
);

-- 创建索引
CREATE INDEX idx_review_rules_knowledge_base_id ON review_rules(knowledge_base_id);
CREATE INDEX idx_review_rules_severity ON review_rules(severity);
CREATE INDEX idx_review_rules_active ON review_rules(is_active);

-- 初始化审查规则数据
INSERT INTO review_rules (knowledge_base_id, rule_name, rule_content, severity, is_active)
SELECT 
    kb.id,
    '违约责任条款检查',
    '合同应当明确约定违约责任，包括违约情形、责任承担方式、损失赔偿等',
    'warning',
    true
FROM knowledge_base kb WHERE kb.type = 'regulation' LIMIT 1;

INSERT INTO review_rules (knowledge_base_id, rule_name, rule_content, severity, is_active)
SELECT 
    kb.id,
    '合同期限明确性检查',
    '合同应当明确约定履行期限，避免使用模糊的时间表述',
    'error',
    true
FROM knowledge_base kb WHERE kb.type = 'guideline' LIMIT 1;
```

### 6.3 行级安全策略 (RLS)

```sql
-- 启用行级安全
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE contract_elements ENABLE ROW LEVEL SECURITY;
ALTER TABLE risk_items ENABLE ROW LEVEL SECURITY;

-- 合同访问策略
CREATE POLICY "用户可以查看自己的合同" ON contracts
    FOR SELECT USING (
        auth.uid()::text = user_id OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid()::text 
            AND role IN ('admin', 'legal_manager')
        )
    );

CREATE POLICY "用户可以创建自己的合同" ON contracts
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "用户可以更新自己的合同" ON contracts
    FOR UPDATE USING (
        auth.uid()::text = user_id OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid()::text 
            AND role IN ('admin', 'legal_manager')
        )
    );

-- 审查任务访问策略
CREATE POLICY "用户可以查看相关的审查任务" ON review_tasks
    FOR SELECT USING (
        auth.uid()::text = user_id OR 
        EXISTS (
            SELECT 1 FROM contracts c 
            WHERE c.id = contract_id 
            AND c.user_id = auth.uid()::text
        ) OR
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid()::text 
            AND role IN ('admin', 'legal_manager')
        )
    );

-- 基础权限设置
GRANT SELECT ON users TO anon;
GRANT SELECT ON templates TO anon;
GRANT SELECT ON knowledge_base TO anon;
GRANT SELECT ON review_rules TO anon;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated;
```

## 7. 环境配置

### 7.1 开发环境

* Node.js 18+

* PostgreSQL 15+ (通过Supabase)

* React 18+

* TypeScript 5+

### 7.2 生产环境

* Vercel部署 (前端)

* Supabase托管 (数据库)

* 环境变量安全配置

* HTTPS强制启用

### 7.3 监控和日志

* Supabase内置监控

* 错误日志收集

* 性能指标监控

* 安全事件追踪

