# Supabase迁移实施指南

## 1. 前期准备

### 1.1 环境变量配置

#### 创建根目录 .env 文件

```env
# Supabase配置
SUPABASE_URL=https://zeihrmnskcglumxyegcg.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWhybW5za2NnbHVteHllZ2NnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ1NjE5NzMsImV4cCI6MjA3MDEzNzk3M30.0U3c3_AKrv3CD0t1dVna1sjoYZhk9HrdI6D_BNBLS-0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWhybW5za2NnbHVteHllZ2NnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDU2MTk3MywiZXhwIjoyMDcwMTM3OTczfQ.hvkJcH8sE0uRSs_7vTgym_W4INvxZhVHssIYirUR82U

# 应用配置
NODE_ENV=development
PORT=3001
JWT_SECRET=your-secret-key-change-in-production
```

#### 创建前端 .env.local 文件

```env
# 前端环境变量
VITE_SUPABASE_URL=https://zeihrmnskcglumxyegcg.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWhybW5za2NnbHVteHllZ2NnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ1NjE5NzMsImV4cCI6MjA3MDEzNzk3M30.0U3c3_AKrv3CD0t1dVna1sjoYZhk9HrdI6D_BNBLS-0
VITE_API_BASE_URL=http://localhost:3001/api
```

### 1.2 依赖包安装

```bash
# 安装Supabase客户端
npm install @supabase/supabase-js

# 安装其他必要依赖
npm install dotenv
```

## 2. 数据库初始化

### 2.1 执行DDL语句

在Supabase SQL编辑器中依次执行以下SQL语句：

#### 步骤1: 创建基础函数

```sql
-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';
```

#### 步骤2: 创建用户表

```sql
-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'legal_staff', 'legal_manager', 'admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- 创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 步骤3: 创建合同表

```sql
-- 创建合同表
CREATE TABLE contracts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    file_path VARCHAR(500),
    content TEXT,
    ocr_content TEXT,
    status VARCHAR(20) DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'processing', 'reviewed', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_contracts_user_id ON contracts(user_id);
CREATE INDEX idx_contracts_status ON contracts(status);
CREATE INDEX idx_contracts_category ON contracts(category);
CREATE INDEX idx_contracts_created_at ON contracts(created_at DESC);

-- 创建更新时间触发器
CREATE TRIGGER update_contracts_updated_at BEFORE UPDATE ON contracts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

#### 步骤4: 创建其他表

```sql
-- 创建审查任务表
CREATE TABLE review_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID NOT NULL,
    user_id UUID NOT NULL,
    review_type VARCHAR(100) NOT NULL,
    analysis_result JSONB,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

CREATE INDEX idx_review_tasks_contract_id ON review_tasks(contract_id);
CREATE INDEX idx_review_tasks_user_id ON review_tasks(user_id);
CREATE INDEX idx_review_tasks_status ON review_tasks(status);

-- 创建合同要素表
CREATE TABLE contract_elements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID NOT NULL,
    element_type VARCHAR(100) NOT NULL,
    element_value TEXT NOT NULL,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_contract_elements_contract_id ON contract_elements(contract_id);

-- 创建风险项表
CREATE TABLE risk_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    review_task_id UUID NOT NULL,
    risk_type VARCHAR(100) NOT NULL,
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    suggestion TEXT,
    identified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_risk_items_review_task_id ON risk_items(review_task_id);

-- 创建模板表
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    parameters JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_templates_category ON templates(category);

-- 创建知识库表
CREATE TABLE knowledge_base (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL CHECK (type IN ('regulation', 'case_law', 'template', 'guideline')),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_knowledge_base_type ON knowledge_base(type);
CREATE TRIGGER update_knowledge_base_updated_at BEFORE UPDATE ON knowledge_base
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 创建审查规则表
CREATE TABLE review_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    knowledge_base_id UUID NOT NULL,
    rule_name VARCHAR(255) NOT NULL,
    rule_content TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    is_active BOOLEAN DEFAULT true
);

CREATE INDEX idx_review_rules_knowledge_base_id ON review_rules(knowledge_base_id);
```

#### 步骤5: 设置权限和RLS

```sql
-- 基础权限设置
GRANT SELECT ON users TO anon;
GRANT SELECT ON templates TO anon;
GRANT SELECT ON knowledge_base TO anon;
GRANT SELECT ON review_rules TO anon;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- 启用行级安全
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE contract_elements ENABLE ROW LEVEL SECURITY;
ALTER TABLE risk_items ENABLE ROW LEVEL SECURITY;

-- 合同访问策略
CREATE POLICY "用户可以查看自己的合同" ON contracts
    FOR SELECT USING (
        auth.uid()::text = user_id OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid()::text 
            AND role IN ('admin', 'legal_manager')
        )
    );

CREATE POLICY "用户可以创建自己的合同" ON contracts
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "用户可以更新自己的合同" ON contracts
    FOR UPDATE USING (
        auth.uid()::text = user_id OR 
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid()::text 
            AND role IN ('admin', 'legal_manager')
        )
    );
```

### 2.2 初始化演示数据

```sql
-- 插入演示用户
INSERT INTO users (email, name, role) VALUES
('<EMAIL>', '系统管理员', 'admin'),
('<EMAIL>', '法务主管', 'legal_manager'),
('<EMAIL>', '法务专员', 'legal_staff');

-- 插入模板数据
INSERT INTO templates (name, category, content, parameters, is_active) VALUES
('标准服务合同模板', '服务合同', '甲方：[甲方名称]\n乙方：[乙方名称]\n\n根据《中华人民共和国合同法》等相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就服务事宜达成如下协议：\n\n第一条 服务内容\n乙方向甲方提供[服务内容]服务。\n\n第二条 服务期限\n服务期限为[服务期限]，自[开始日期]起至[结束日期]止。\n\n第三条 服务费用\n服务费用总计人民币[金额]元（大写：[大写金额]）。\n\n第四条 付款方式\n[付款方式说明]\n\n第五条 违约责任\n[违约责任条款]\n\n第六条 争议解决\n因履行本合同发生的争议，双方应友好协商解决；协商不成的，提交[仲裁机构]仲裁。\n\n第七条 其他\n本合同一式两份，甲乙双方各执一份，具有同等法律效力。\n\n甲方（盖章）：\n法定代表人：\n日期：\n\n乙方（盖章）：\n法定代表人：\n日期：', '{"fields": ["甲方名称", "乙方名称", "服务内容", "服务期限", "开始日期", "结束日期", "金额", "大写金额", "付款方式", "违约责任条款", "仲裁机构"]}', true),
('采购合同模板', '采购合同', '采购方：[采购方名称]\n供应方：[供应方名称]\n\n根据《中华人民共和国合同法》等相关法律法规，采购方与供应方就货物采购事宜达成如下协议：\n\n第一条 货物信息\n货物名称：[货物名称]\n规格型号：[规格型号]\n数量：[数量]\n单价：[单价]\n总价：[总价]\n\n第二条 交货\n交货地点：[交货地点]\n交货时间：[交货时间]\n\n第三条 质量标准\n[质量标准说明]\n\n第四条 验收\n[验收条款]\n\n第五条 付款\n[付款条款]\n\n第六条 违约责任\n[违约责任条款]\n\n采购方（盖章）：\n日期：\n\n供应方（盖章）：\n日期：', '{"fields": ["采购方名称", "供应方名称", "货物名称", "规格型号", "数量", "单价", "总价", "交货地点", "交货时间", "质量标准说明", "验收条款", "付款条款", "违约责任条款"]}', true);

-- 插入知识库数据
INSERT INTO knowledge_base (type, title, content, metadata) VALUES
('regulation', '合同法基本原则', '《中华人民共和国民法典》合同编规定了合同的基本原则：\n1. 平等原则：当事人在合同中的地位平等\n2. 自愿原则：当事人依法享有自愿订立合同的权利\n3. 公平原则：当事人应当遵循公平原则确定各方的权利和义务\n4. 诚实信用原则：当事人行使权利、履行义务应当遵循诚实信用原则\n5. 守法原则：当事人订立、履行合同，应当遵守法律、行政法规', '{"source": "中华人民共和国民法典", "effective_date": "2021-01-01"}'),
('guideline', '合同审查指导原则', '合同审查的基本指导原则：\n1. 合法性审查：确保合同内容符合法律法规\n2. 完整性审查：检查合同条款是否完整\n3. 明确性审查：确保合同条款表述清晰明确\n4. 可执行性审查：评估合同条款的可执行性\n5. 风险识别：识别潜在的法律风险和商业风险\n6. 利益平衡：确保各方权利义务平衡', '{"version": "1.0", "department": "法务部"}');

-- 插入审查规则数据
INSERT INTO review_rules (knowledge_base_id, rule_name, rule_content, severity, is_active)
SELECT 
    kb.id,
    '违约责任条款检查',
    '合同应当明确约定违约责任，包括违约情形、责任承担方式、损失赔偿等',
    'warning',
    true
FROM knowledge_base kb WHERE kb.type = 'regulation' LIMIT 1;

INSERT INTO review_rules (knowledge_base_id, rule_name, rule_content, severity, is_active)
SELECT 
    kb.id,
    '合同期限明确性检查',
    '合同应当明确约定履行期限，避免使用模糊的时间表述',
    'error',
    true
FROM knowledge_base kb WHERE kb.type = 'guideline' LIMIT 1;
```

## 3. 后端代码实现

### 3.1 创建Supabase客户端

#### api/services/supabaseClient.ts

```typescript
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing Supabase environment variables');
}

// 服务端客户端，使用service_role_key
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// 客户端使用的anon key
export const supabaseAnonKey = process.env.SUPABASE_ANON_KEY!;

export default supabaseAdmin;
```

### 3.2 创建Supabase服务层

#### api/services/supabaseService.ts

```typescript
import { supabaseAdmin } from './supabaseClient.js';
import { User, Contract, ReviewTask, Template, KnowledgeBase, ReviewRule } from './mockDatabase.js';

export class SupabaseService {
  // 用户相关操作
  async getUsers(): Promise<User[]> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  async getUserById(id: string): Promise<User | null> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // No rows returned
      throw error;
    }
    return data;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('email', email)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  async createUser(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<User> {
    const { data, error } = await supabaseAdmin
      .from('users')
      .insert([userData])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // 合同相关操作
  async getContracts(userId?: string): Promise<Contract[]> {
    let query = supabaseAdmin
      .from('contracts')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (userId) {
      query = query.eq('user_id', userId);
    }
    
    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  async getAllContracts(): Promise<Contract[]> {
    return this.getContracts();
  }

  async getContractById(id: string): Promise<Contract | null> {
    const { data, error } = await supabaseAdmin
      .from('contracts')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  async createContract(contractData: Omit<Contract, 'id' | 'created_at' | 'updated_at'>): Promise<Contract> {
    const { data, error } = await supabaseAdmin
      .from('contracts')
      .insert([contractData])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  async updateContract(id: string, updates: Partial<Contract>): Promise<Contract | null> {
    const { data, error } = await supabaseAdmin
      .from('contracts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  async deleteContract(id: string): Promise<void> {
    const { error } = await supabaseAdmin
      .from('contracts')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }

  // 审查任务相关操作
  async getReviewTasks(userId?: string): Promise<ReviewTask[]> {
    let query = supabaseAdmin
      .from('review_tasks')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (userId) {
      query = query.eq('user_id', userId);
    }
    
    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  async getReviewTaskById(id: string): Promise<ReviewTask | null> {
    const { data, error } = await supabaseAdmin
      .from('review_tasks')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  async createReviewTask(taskData: Omit<ReviewTask, 'id' | 'created_at'>): Promise<ReviewTask> {
    const { data, error } = await supabaseAdmin
      .from('review_tasks')
      .insert([taskData])
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  async updateReviewTask(id: string, updates: Partial<ReviewTask>): Promise<ReviewTask | null> {
    const { data, error } = await supabaseAdmin
      .from('review_tasks')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  // 模板相关操作
  async getTemplates(): Promise<Template[]> {
    const { data, error } = await supabaseAdmin
      .from('templates')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  async getTemplateById(id: string): Promise<Template | null> {
    const { data, error } = await supabaseAdmin
      .from('templates')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  // 知识库相关操作
  async getKnowledgeBase(): Promise<KnowledgeBase[]> {
    const { data, error } = await supabaseAdmin
      .from('knowledge_base')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  async getReviewRules(): Promise<ReviewRule[]> {
    const { data, error } = await supabaseAdmin
      .from('review_rules')
      .select('*')
      .eq('is_active', true)
      .order('rule_name');
    
    if (error) throw error;
    return data || [];
  }

  async getAllReviewRules(): Promise<ReviewRule[]> {
    return this.getReviewRules();
  }

  // 模拟方法（保持兼容性）
  async getContractElements(contractId: string): Promise<any[]> {
    // 模拟返回合同要素
    return [
      { type: 'party', content: '甲方：示例公司', confidence: 0.95 },
      { type: 'amount', content: '合同金额：100万元', confidence: 0.90 },
      { type: 'term', content: '合同期限：1年', confidence: 0.85 }
    ];
  }

  async getRiskItems(contractId: string): Promise<any[]> {
    // 模拟返回风险项
    return [
      { type: 'legal', description: '违约责任条款不够明确', severity: 'medium', suggestion: '建议明确违约责任的具体承担方式' },
      { type: 'commercial', description: '付款条件对己方不利', severity: 'high', suggestion: '建议调整付款周期' }
    ];
  }

  // 统计数据
  async getStatistics(userId?: string): Promise<any> {
    const contracts = await this.getContracts(userId);
    const reviewTasks = await this.getReviewTasks(userId);
    
    return {
      totalContracts: contracts.length,
      pendingReviews: reviewTasks.filter(task => task.status === 'pending').length,
      completedReviews: reviewTasks.filter(task => task.status === 'completed').length,
      contractsByCategory: this.groupBy(contracts, 'category'),
      contractsByStatus: this.groupBy(contracts, 'status'),
      riskDistribution: { low: 5, medium: 3, high: 2, critical: 1 }
    };
  }

  private groupBy(array: any[], key: string): Record<string, number> {
    return array.reduce((acc, item) => {
      const value = item[key] || 'unknown';
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {});
  }
}

// 导出单例实例
export const supabaseService = new SupabaseService();
export default supabaseService;
```

### 3.3 更新认证服务

#### api/services/authService.ts (更新)

```typescript
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { supabaseService } from './supabaseService.js';
import { User } from './mockDatabase.js';

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
const JWT_EXPIRES_IN = '24h';

export interface TokenPayload {
  userId: string;
  email: string;
  role: string;
}

export class AuthService {
  // 用户登录
  async login(email: string, password: string): Promise<{ user: User; token: string } | null> {
    try {
      const user = await supabaseService.getUserByEmail(email);
      if (!user) {
        return null;
      }

      // 验证密码（兼容现有的bcrypt哈希）
      const isValidPassword = await bcrypt.compare(password, user.password_hash || '');
      if (!isValidPassword) {
        return null;
      }

      // 生成JWT token
      const token = this.generateToken({
        userId: user.id,
        email: user.email,
        role: user.role
      });

      return { user, token };
    } catch (error) {
      console.error('Login error:', error);
      return null;
    }
  }

  // 用户注册
  async register(userData: {
    email: string;
    password: string;
    name: string;
    role?: string;
  }): Promise<{ user: User; token: string } | null> {
    try {
      // 检查用户是否已存在
      const existingUser = await supabaseService.getUserByEmail(userData.email);
      if (existingUser) {
        throw new Error('用户已存在');
      }

      // 加密密码
      const saltRounds = 10;
      const password_hash = await bcrypt.hash(userData.password, saltRounds);

      // 创建用户
      const user = await supabaseService.createUser({
        email: userData.email,
        password_hash,
        name: userData.name,
        role: userData.role as any || 'user'
      });

      // 生成JWT token
      const token = this.generateToken({
        userId: user.id,
        email: user.email,
        role: user.role
      });

      return { user, token };
    } catch (error) {
      console.error('Registration error:', error);
      return null;
    }
  }

  // 验证token
  async verifyToken(token: string): Promise<TokenPayload | null> {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as TokenPayload;
      
      // 验证用户是否仍然存在
      const user = await supabaseService.getUserById(decoded.userId);
      if (!user) {
        return null;
      }

      return decoded;
    } catch (error) {
      console.error('Token verification error:', error);
      return null;
    }
  }

  // 刷新token
  async refreshToken(token: string): Promise<string | null> {
    try {
      const payload = await this.verifyToken(token);
      if (!payload) {
        return null;
      }

      // 生成新token
      return this.generateToken(payload);
    } catch (error) {
      console.error('Token refresh error:', error);
      return null;
    }
  }

  // 生成JWT token
  private generateToken(payload: TokenPayload): string {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });
  }

  // 获取用户信息
  async getUserById(id: string): Promise<User | null> {
    return supabaseService.getUserById(id);
  }
}

// 导出单例实例
export const authService = new AuthService();
export default authService;
```

### 3.4 更新API路由

#### api/routes/auth.ts (更新)

```typescript
import express from 'express';
import { authService } from '../services/authService.js';

const router = express.Router();

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '邮箱和密码不能为空'
      });
    }

    const result = await authService.login(email, password);
    if (!result) {
      return res.status(401).json({
        success: false,
        message: '邮箱或密码错误'
      });
    }

    const { user, token } = result;
    
    // 移除密码哈希
    const { password_hash, ...userWithoutPassword } = user;
    
    res.json({
      success: true,
      message: '登录成功',
      token,
      user: {
        id: userWithoutPassword.id,
        username: userWithoutPassword.name,
        email: userWithoutPassword.email,
        role: userWithoutPassword.role,
        name: userWithoutPassword.name,
        createdAt: userWithoutPassword.created_at
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { email, password, name, role } = req.body;

    if (!email || !password || !name) {
      return res.status(400).json({
        success: false,
        message: '邮箱、密码和姓名不能为空'
      });
    }

    const result = await authService.register({ email, password, name, role });
    if (!result) {
      return res.status(400).json({
        success: false,
        message: '注册失败，用户可能已存在'
      });
    }

    const { user, token } = result;
    
    // 移除密码哈希
    const { password_hash, ...userWithoutPassword } = user;
    
    res.status(201).json({
      success: true,
      message: '注册成功',
      token,
      user: {
        id: userWithoutPassword.id,
        username: userWithoutPassword.name,
        email: userWithoutPassword.email,
        role: userWithoutPassword.role,
        name: userWithoutPassword.name,
        createdAt: userWithoutPassword.created_at
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 刷新token
router.post('/refresh', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: '缺少认证token'
      });
    }

    const token = authHeader.substring(7);
    const newToken = await authService.refreshToken(token);
    
    if (!newToken) {
      return res.status(401).json({
        success: false,
        message: 'Token无效或已过期'
      });
    }

    res.json({
      success: true,
      token: newToken
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 用户登出
router.post('/logout', (req, res) => {
  // JWT是无状态的，客户端删除token即可
  res.json({
    success: true,
    message: '登出成功'
  });
});

export default router;
```

### 3.5 更新其他服务路由

#### api/routes/contracts.ts (更新)

```typescript
import express from 'express';
import { supabaseService } from '../services/supabaseService.js';
import { authMiddleware } from '../middleware/auth.js';

const router = express.Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取合同列表
router.get('/', async (req, res) => {
  try {
    const userId = req.user?.role === 'admin' || req.user?.role === 'legal_manager' 
      ? undefined 
      : req.user?.userId;
    
    const contracts = await supabaseService.getContracts(userId);
    res.json({
      success: true,
      data: contracts
    });
  } catch (error) {
    console.error('Get contracts error:', error);
    res.status(500).json({
      success: false,
      message: '获取合同列表失败'
    });
  }
});

// 获取合同详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const contract = await supabaseService.getContractById(id);
    
    if (!contract) {
      return res.status(404).json({
        success: false,
        message: '合同不存在'
      });
    }

    // 权限检查
    if (contract.user_id !== req.user?.userId && 
        req.user?.role !== 'admin' && 
        req.user?.role !== 'legal_manager') {
      return res.status(403).json({
        success: false,
        message: '无权访问此合同'
      });
    }

    res.json({
      success: true,
      data: contract
    });
  } catch (error) {
    console.error('Get contract error:', error);
    res.status(500).json({
      success: false,
      message: '获取合同详情失败'
    });
  }
});

// 创建合同
router.post('/', async (req, res) => {
  try {
    const { title, category, content, file_path } = req.body;
    
    if (!title) {
      return res.status(400).json({
        success: false,
        message: '合同标题不能为空'
      });
    }

    const contract = await supabaseService.createContract({
      user_id: req.user!.userId,
      title,
      category: category || '其他',
      content,
      file_path,
      status: 'uploaded'
    });

    res.status(201).json({
      success: true,
      message: '合同创建成功',
      data: contract
    });
  } catch (error) {
    console.error('Create contract error:', error);
    res.status(500).json({
      success: false,
      message: '创建合同失败'
    });
  }
});

// 更新合同
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updates = req.body;
    
    // 检查合同是否存在
    const existingContract = await supabaseService.getContractById(id);
    if (!existingContract) {
      return res.status(404).json({
        success: false,
        message: '合同不存在'
      });
    }

    // 权限检查
    if (existingContract.user_id !== req.user?.userId && 
        req.user?.role !== 'admin' && 
        req.user?.role !== 'legal_manager') {
      return res.status(403).json({
        success: false,
        message: '无权修改此合同'
      });
    }

    const contract = await supabaseService.updateContract(id, updates);
    
    res.json({
      success: true,
      message: '合同更新成功',
      data: contract
    });
  } catch (error) {
    console.error('Update contract error:', error);
    res.status(500).json({
      success: false,
      message: '更新合同失败'
    });
  }
});

// 删除合同
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查合同是否存在
    const existingContract = await supabaseService.getContractById(id);
    if (!existingContract) {
      return res.status(404).json({
        success: false,
        message: '合同不存在'
      });
    }

    // 权限检查
    if (existingContract.user_id !== req.user?.userId && 
        req.user?.role !== 'admin' && 
        req.user?.role !== 'legal_manager') {
      return res.status(403).json({
        success: false,
        message: '无权删除此合同'
      });
    }

    await supabaseService.deleteContract(id);
    
    res.json({
      success: true,
      message: '合同删除成功'
    });
  } catch (error) {
    console.error('Delete contract error:', error);
    res.status(500).json({
      success: false,
      message: '删除合同失败'
    });
  }
});

// 获取统计数据
router.get('/stats/overview', async (req, res) => {
  try {
    const userId = req.user?.role === 'admin' || req.user?.role === 'legal_manager' 
      ? undefined 
      : req.user?.userId;
    
    const stats = await supabaseService.getStatistics(userId);
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败'
    });
  }
});

export default router;
```

## 4. 前端配置更新

### 4.1 更新AuthContext

#### src/contexts/AuthContext.tsx (更新)

```typescript
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: string;
  username: string;
  email: string;
  role: 'admin' | 'legal_manager' | 'legal_staff';
  name: string;
  avatar?: string;
  department?: string;
  phone?: string;
  createdAt: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>;
  register: (data: RegisterData) => Promise<{ success: boolean; message?: string }>;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  name: string;
  role?: 'legal_manager' | 'legal_staff';
  department?: string;
  phone?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 使用环境变量配置API地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化时检查本地存储的token
  useEffect(() => {
    const savedToken = localStorage.getItem('token');
    if (savedToken) {
      setToken(savedToken);
      // 可以在这里验证token有效性
      refreshToken().finally(() => setIsLoading(false));
    } else {
      setIsLoading(false);
    }
  }, []);

  const login = async (email: string, password: string): Promise<{ success: boolean; message?: string }> => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();

      if (data.success) {
        setUser(data.user);
        setToken(data.token);
        localStorage.setItem('token', data.token);
        return { success: true, message: data.message };
      } else {
        return { success: false, message: data.message };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: '网络错误，请稍后重试' };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterData): Promise<{ success: boolean; message?: string }> => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (result.success) {
        setUser(result.user);
        setToken(result.token);
        localStorage.setItem('token', result.token);
        return { success: true, message: result.message };
      } else {
        return { success: false, message: result.message };
      }
    } catch (error) {
      console.error('Register error:', error);
      return { success: false, message: '网络错误，请稍后重试' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        setToken(data.token);
        localStorage.setItem('token', data.token);
        return true;
      } else {
        logout();
        return false;
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      logout();
      return false;
    }
  };

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    login,
    register,
    logout,
    refreshToken
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
```

## 5. 测试和验证

### 5.1 功能测试清单

* [ ] 数据库连接测试

* [ ] 用户认证功能测试

  * [ ] 登录功能

  * [ ] 注册功能

  * [ ] Token刷新

* [ ] 合同管理功能测试

  * [ ] 创建合同

  * [ ] 查看合同列表

  * [ ] 更新合同

  * [ ] 删除合同

* [ ] 权限控制测试

  * [ ] 用户只能访问自己的合同

  * [ ] 管理员可以访问所有合同

* [ ] 数据完整性测试

  * [ ] 演示数据正确迁移

  * [ ] 关联关系正确

### 5.2 性能测试

* [ ] API响应时间测试

* [ ] 数据库查询性能测试

* [ ] 并发用户测试

### 5.3 安全测试

* [ ] RLS策略有效性测试

* [ ] SQL注入防护测试

* [ ] 认证绕过测试

## 6. 部署和监控

### 6.1 生产环境配置

```env
# 生产环境变量
NODE_ENV=production
SUPABASE_URL=https://zeihrmnskcglumxyegcg.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-production-service-key
JWT_SECRET=your-production-jwt-secret
```

### 6.2 监控设置

* 设置Supabase监控告警

* 配置错误日志收集

* 设置性能指标监控

## 7. 回滚计划

如果迁移过程中出现问题，可以按以下步骤回滚：

1. 停止新的Supabase服务
2. 恢复原有的Mock数据库服务
3. 恢复原有的API路由配置
4. 恢复原有的前端配置
5. 重启服务验证功能正常

## 8. 迁移完成后的清理

* 删除不再使用的Mock数据库文件

* 清理旧的API服务代码

* 更新文档和注释

* 删除临时配置文件

