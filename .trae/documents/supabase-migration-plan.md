# Supabase数据库迁移方案

## 1. 项目概述

本文档详细规划了智能合同审查系统从Mock数据库到Supabase的完整迁移方案。当前系统使用内存存储和JSON文件持久化的Mock数据库，包含8个核心数据模型，需要平滑迁移到Supabase PostgreSQL数据库，确保系统功能完整性和数据安全性。

### 1.1 迁移目标
- 将Mock数据库完全替换为Supabase PostgreSQL数据库
- 保持现有API接口兼容性
- 确保演示数据的完整迁移
- 实现用户认证系统与Supabase Auth的集成
- 提升系统性能和可扩展性

### 1.2 技术栈变更
- **数据库**: Mock Database → Supabase PostgreSQL
- **认证**: JWT + bcrypt → Supabase Auth
- **前端**: React + 硬编码API_BASE_URL → React + 环境变量配置
- **后端**: Express + Mock数据 → Express + Supabase客户端

## 2. 现有系统分析

### 2.1 当前数据模型

基于对`mockDatabase.ts`的分析，系统包含以下8个核心数据模型：

| 模型名称 | 用途 | 关键字段 |
|---------|------|----------|
| User | 用户管理 | id, email, password_hash, name, role |
| Contract | 合同管理 | id, user_id, title, category, content, status |
| ReviewTask | 审查任务 | id, contract_id, user_id, review_type, status |
| ContractElement | 合同要素 | id, contract_id, element_type, element_value |
| RiskItem | 风险项 | id, review_task_id, risk_type, risk_level |
| Template | 合同模板 | id, name, category, content, parameters |
| KnowledgeBase | 知识库 | id, type, title, content, metadata |
| ReviewRule | 审查规则 | id, knowledge_base_id, rule_name, severity |

### 2.2 当前API架构
- **前端**: React应用，通过`http://localhost:3001/api`访问后端
- **后端**: Express服务器，端口3001，使用Mock数据库
- **认证**: JWT token + bcrypt密码哈希
- **数据持久化**: JSON文件存储在`api/data/`目录

### 2.3 演示数据
- 3个预设用户账号（admin、manager、staff）
- 2个合同模板（服务合同、采购合同）
- 2个知识库条目（合同法、审查指导原则）
- 2个审查规则

## 3. Supabase数据库设计

### 3.1 数据库表结构

#### 3.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('user', 'legal_staff', 'legal_manager', 'admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
```

#### 3.1.2 合同表 (contracts)
```sql
CREATE TABLE contracts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(100),
    file_path VARCHAR(500),
    content TEXT,
    ocr_content TEXT,
    status VARCHAR(20) DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'processing', 'reviewed', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_contracts_user_id ON contracts(user_id);
CREATE INDEX idx_contracts_status ON contracts(status);
CREATE INDEX idx_contracts_category ON contracts(category);
CREATE INDEX idx_contracts_created_at ON contracts(created_at DESC);
```

#### 3.1.3 审查任务表 (review_tasks)
```sql
CREATE TABLE review_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID NOT NULL,
    user_id UUID NOT NULL,
    review_type VARCHAR(100) NOT NULL,
    analysis_result JSONB,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 索引
CREATE INDEX idx_review_tasks_contract_id ON review_tasks(contract_id);
CREATE INDEX idx_review_tasks_user_id ON review_tasks(user_id);
CREATE INDEX idx_review_tasks_status ON review_tasks(status);
```

#### 3.1.4 合同要素表 (contract_elements)
```sql
CREATE TABLE contract_elements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID NOT NULL,
    element_type VARCHAR(100) NOT NULL,
    element_value TEXT NOT NULL,
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_contract_elements_contract_id ON contract_elements(contract_id);
CREATE INDEX idx_contract_elements_type ON contract_elements(element_type);
```

#### 3.1.5 风险项表 (risk_items)
```sql
CREATE TABLE risk_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    review_task_id UUID NOT NULL,
    risk_type VARCHAR(100) NOT NULL,
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    suggestion TEXT,
    identified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_risk_items_review_task_id ON risk_items(review_task_id);
CREATE INDEX idx_risk_items_risk_level ON risk_items(risk_level);
```

#### 3.1.6 模板表 (templates)
```sql
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    parameters JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_templates_category ON templates(category);
CREATE INDEX idx_templates_active ON templates(is_active);
```

#### 3.1.7 知识库表 (knowledge_base)
```sql
CREATE TABLE knowledge_base (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL CHECK (type IN ('regulation', 'case_law', 'template', 'guideline')),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_knowledge_base_type ON knowledge_base(type);
CREATE INDEX idx_knowledge_base_title ON knowledge_base(title);
```

#### 3.1.8 审查规则表 (review_rules)
```sql
CREATE TABLE review_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    knowledge_base_id UUID NOT NULL,
    rule_name VARCHAR(255) NOT NULL,
    rule_content TEXT NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    is_active BOOLEAN DEFAULT true
);

-- 索引
CREATE INDEX idx_review_rules_knowledge_base_id ON review_rules(knowledge_base_id);
CREATE INDEX idx_review_rules_severity ON review_rules(severity);
CREATE INDEX idx_review_rules_active ON review_rules(is_active);
```

### 3.2 行级安全策略 (RLS)

#### 3.2.1 基础权限设置
```sql
-- 为anon角色授予基本读取权限
GRANT SELECT ON users TO anon;
GRANT SELECT ON templates TO anon;
GRANT SELECT ON knowledge_base TO anon;
GRANT SELECT ON review_rules TO anon;

-- 为authenticated角色授予完整权限
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO authenticated;
```

#### 3.2.2 用户数据隔离策略
```sql
-- 启用RLS
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE review_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE contract_elements ENABLE ROW LEVEL SECURITY;
ALTER TABLE risk_items ENABLE ROW LEVEL SECURITY;

-- 合同访问策略
CREATE POLICY "Users can view own contracts" ON contracts
    FOR SELECT USING (auth.uid()::text = user_id OR 
                     EXISTS (SELECT 1 FROM users WHERE id = auth.uid()::text AND role IN ('admin', 'legal_manager')));

CREATE POLICY "Users can insert own contracts" ON contracts
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update own contracts" ON contracts
    FOR UPDATE USING (auth.uid()::text = user_id OR 
                     EXISTS (SELECT 1 FROM users WHERE id = auth.uid()::text AND role IN ('admin', 'legal_manager')));
```

## 4. 分阶段实施计划

### 阶段1: 环境配置和数据库初始化 (预计1-2小时)

#### 4.1.1 环境变量配置
- 创建`.env`文件，配置Supabase连接信息
- 更新前端API配置，支持环境变量
- 配置开发和生产环境的不同设置

#### 4.1.2 Supabase数据库初始化
- 执行DDL语句创建所有表结构
- 设置索引和约束
- 配置行级安全策略
- 验证数据库连接

#### 4.1.3 依赖包安装
- 安装`@supabase/supabase-js`客户端库
- 更新package.json依赖
- 配置TypeScript类型定义

### 阶段2: 后端API重构 (预计3-4小时)

#### 4.2.1 Supabase客户端配置
- 创建Supabase客户端实例
- 配置服务端和客户端不同的密钥
- 实现连接池和错误处理

#### 4.2.2 数据访问层重构
- 创建新的`supabaseService.ts`
- 实现所有Mock数据库方法的Supabase版本
- 保持API接口兼容性
- 添加事务支持和错误处理

#### 4.2.3 认证系统集成
- 集成Supabase Auth
- 更新JWT token验证逻辑
- 实现用户注册和登录流程
- 配置密码重置功能

### 阶段3: 数据迁移 (预计1-2小时)

#### 4.3.1 演示数据迁移
- 创建数据迁移脚本
- 迁移用户账号（保持密码兼容性）
- 迁移模板和知识库数据
- 迁移审查规则

#### 4.3.2 数据验证
- 验证所有数据完整性
- 测试关联关系
- 确认演示账号可正常登录

### 阶段4: 前端配置更新 (预计1小时)

#### 4.4.1 环境变量配置
- 更新前端环境变量配置
- 修改API_BASE_URL为动态配置
- 配置Supabase客户端（如需要）

#### 4.4.2 错误处理优化
- 更新错误处理逻辑
- 适配Supabase错误格式
- 优化用户体验

### 阶段5: 测试和验证 (预计1-2小时)

#### 4.5.1 功能测试
- 测试用户认证流程
- 测试合同管理功能
- 测试智能审查功能
- 测试数据统计功能

#### 4.5.2 性能测试
- 对比迁移前后性能
- 优化查询性能
- 验证并发处理能力

#### 4.5.3 安全测试
- 验证RLS策略有效性
- 测试权限控制
- 检查数据泄露风险

## 5. 技术实施细节

### 5.1 环境变量配置

#### 5.1.1 创建.env文件
```env
# Supabase配置
SUPABASE_URL=https://zeihrmnskcglumxyegcg.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWhybW5za2NnbHVteHllZ2NnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ1NjE5NzMsImV4cCI6MjA3MDEzNzk3M30.0U3c3_AKrv3CD0t1dVna1sjoYZhk9HrdI6D_BNBLS-0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWhybW5za2NnbHVteHllZ2NnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDU2MTk3MywiZXhwIjoyMDcwMTM3OTczfQ.hvkJcH8sE0uRSs_7vTgym_W4INvxZhVHssIYirUR82U

# 应用配置
NODE_ENV=development
PORT=3001
JWT_SECRET=your-secret-key-change-in-production
```

#### 5.1.2 前端环境变量
```env
# .env.local
VITE_SUPABASE_URL=https://zeihrmnskcglumxyegcg.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InplaWhybW5za2NnbHVteHllZ2NnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ1NjE5NzMsImV4cCI6MjA3MDEzNzk3M30.0U3c3_AKrv3CD0t1dVna1sjoYZhk9HrdI6D_BNBLS-0
VITE_API_BASE_URL=http://localhost:3001/api
```

### 5.2 Supabase服务层架构

#### 5.2.1 服务层结构
```
api/services/
├── supabaseClient.ts      # Supabase客户端配置
├── supabaseService.ts     # 数据访问服务
├── authService.ts         # 认证服务（更新）
└── migrationService.ts    # 数据迁移服务
```

#### 5.2.2 类型定义更新
- 更新接口定义以匹配Supabase字段
- 添加Supabase特定的类型
- 保持向后兼容性

### 5.3 数据迁移策略

#### 5.3.1 迁移脚本设计
- 批量数据插入
- 错误回滚机制
- 进度监控
- 数据验证

#### 5.3.2 演示数据处理
- 保持现有演示账号
- 迁移密码哈希（兼容bcrypt）
- 保持数据关联关系

## 6. 风险评估与应对

### 6.1 技术风险

| 风险项 | 影响程度 | 应对策略 |
|--------|----------|----------|
| 数据迁移失败 | 高 | 完整备份，分批迁移，回滚机制 |
| API兼容性问题 | 中 | 保持接口一致性，渐进式重构 |
| 性能下降 | 中 | 优化查询，添加索引，连接池 |
| 认证集成问题 | 中 | 保持JWT兼容，渐进式迁移 |

### 6.2 业务风险

| 风险项 | 影响程度 | 应对策略 |
|--------|----------|----------|
| 服务中断 | 高 | 分阶段部署，快速回滚 |
| 数据丢失 | 高 | 多重备份，验证机制 |
| 用户体验下降 | 中 | 充分测试，用户反馈 |

### 6.3 安全风险

| 风险项 | 影响程度 | 应对策略 |
|--------|----------|----------|
| 数据泄露 | 高 | RLS策略，权限控制 |
| 认证绕过 | 高 | 多层验证，安全测试 |
| SQL注入 | 中 | 参数化查询，输入验证 |

## 7. 成功标准

### 7.1 功能完整性
- [ ] 所有现有功能正常工作
- [ ] 演示账号可正常登录
- [ ] 数据完整性保持
- [ ] API响应时间不超过现有系统的150%

### 7.2 数据一致性
- [ ] 所有演示数据成功迁移
- [ ] 数据关联关系正确
- [ ] 无数据丢失或损坏

### 7.3 安全性
- [ ] RLS策略有效
- [ ] 用户权限控制正确
- [ ] 无安全漏洞

### 7.4 性能指标
- [ ] 页面加载时间 < 2秒
- [ ] API响应时间 < 500ms
- [ ] 并发用户支持 > 100

## 8. 后续优化建议

### 8.1 短期优化 (1-2周)
- 实现数据库连接池优化
- 添加缓存层（Redis）
- 优化复杂查询性能
- 完善错误监控

### 8.2 中期优化 (1-2月)
- 实现实时数据同步
- 添加数据分析功能
- 优化移动端体验
- 实现高可用部署

### 8.3 长期规划 (3-6月)
- 微服务架构重构
- 多租户支持
- 国际化支持
- AI功能增强

## 9. 总结

本迁移方案采用分阶段、渐进式的方式，确保系统平滑过渡到Supabase。通过详细的风险评估和应对策略，最大化降低迁移风险。预计总体迁移时间为8-12小时，可在1-2个工作日内完成。

迁移完成后，系统将获得更好的性能、可扩展性和安全性，为后续功能扩展奠定坚实基础。