# 合同编辑功能修复技术实施计划

## 1. 技术架构分析

### 1.1 当前架构
```mermaid
graph TD
    A[前端 ContractDetail.tsx] --> B[FormData 格式]
    B --> C[PUT /api/contracts/:id]
    C --> D[后端路由处理]
    D --> E[❌ 无 multer 中间件]
    E --> F[❌ req.body 解析失败]
    F --> G[❌ 数据库更新失败]
```

### 1.2 修复后架构
```mermaid
graph TD
    A[前端 ContractDetail.tsx] --> B[FormData 格式]
    B --> C[PUT /api/contracts/:id]
    C --> D[✅ multer 中间件]
    D --> E[✅ 数据解析成功]
    E --> F[✅ 文件处理逻辑]
    F --> G[✅ 数据库更新成功]
```

## 2. 详细修改计划

### 2.1 后端修改

#### 文件：`/api/routes/contracts.ts`

**修改位置**：第280行 PUT路由定义

**当前代码**：
```javascript
router.put('/:id', authenticateToken, validateContractId, validateContractUpdate, asyncHandler(async (req, res) => {
```

**修改后代码**：
```javascript
router.put('/:id', authenticateToken, upload.single('file'), validateContractId, validateContractUpdate, asyncHandler(async (req, res) => {
```

**修改内容**：
1. 在中间件链中添加 `upload.single('file')`
2. 添加文件处理逻辑
3. 保持现有的数据验证和权限检查
4. 增强错误处理机制

### 2.2 具体实现步骤

#### 步骤1：修改路由中间件
- **位置**：`/api/routes/contracts.ts:280`
- **操作**：在现有中间件链中插入 `upload.single('file')`
- **影响**：使路由能够处理 FormData 格式的请求

#### 步骤2：添加文件处理逻辑
- **位置**：`/api/routes/contracts.ts:330-340`（在现有数据清理逻辑之后）
- **操作**：添加文件上传和更新逻辑
- **功能**：
  - 检查是否有新文件上传
  - 验证文件类型和大小
  - 上传到 Supabase Storage
  - 更新合同的文件路径

#### 步骤3：增强数据处理
- **位置**：`/api/routes/contracts.ts:295-330`
- **操作**：确保所有 FormData 字段正确解析
- **验证**：添加数据类型转换和验证

#### 步骤4：完善错误处理
- **位置**：整个 PUT 路由处理函数
- **操作**：添加文件上传相关的错误处理
- **覆盖**：文件类型错误、文件大小超限、存储失败等情况

## 3. 代码实现规格

### 3.1 路由签名修改
```typescript
router.put('/:id', 
  authenticateToken, 
  upload.single('file'),  // 新增
  validateContractId, 
  validateContractUpdate, 
  asyncHandler(async (req, res) => {
    // 实现逻辑
  })
);
```

### 3.2 文件处理逻辑
```typescript
// 检查是否有新文件上传
if (req.file) {
  // 验证文件类型和大小
  if (!fileStorageService.validateFileType(req.file.mimetype)) {
    throw new FileError('不支持的文件类型');
  }
  
  if (!fileStorageService.validateFileSize(req.file.size)) {
    throw new FileError('文件大小超过限制');
  }
  
  // 准备文件元数据
  const metadata: FileMetadata = {
    originalName: req.file.originalname,
    mimeType: req.file.mimetype,
    size: req.file.size,
    uploadedBy: req.user!.userId,
    contractId: contractId
  };
  
  // 上传文件到 Supabase Storage
  const uploadResult = await fileStorageService.uploadFile(req.file.buffer, metadata);
  
  if (uploadResult.success) {
    cleanedData.file_path = uploadResult.filePath;
    cleanedData.file_url = uploadResult.publicUrl;
  } else {
    throw new FileError(uploadResult.error || '文件上传失败');
  }
}
```

### 3.3 数据类型转换
```typescript
// 确保数值类型正确转换
if (updateData.amount !== undefined) {
  cleanedData.amount = typeof updateData.amount === 'string' 
    ? parseFloat(updateData.amount) 
    : updateData.amount;
}
```

## 4. 测试计划

### 4.1 单元测试用例

#### 测试用例1：仅更新文本数据
- **输入**：FormData 包含文本字段，无文件
- **预期**：数据成功更新，文件字段不变
- **验证**：数据库记录正确更新

#### 测试用例2：同时更新数据和文件
- **输入**：FormData 包含文本字段和文件
- **预期**：数据和文件都成功更新
- **验证**：数据库记录和文件存储都正确

#### 测试用例3：无效文件类型
- **输入**：FormData 包含不支持的文件类型
- **预期**：返回文件类型错误
- **验证**：错误信息正确，数据不被更新

#### 测试用例4：文件大小超限
- **输入**：FormData 包含超过10MB的文件
- **预期**：返回文件大小错误
- **验证**：错误信息正确，数据不被更新

### 4.2 集成测试用例

#### 测试用例1：完整编辑流程
- **步骤**：
  1. 用户登录
  2. 进入合同编辑页面
  3. 修改各项数据
  4. 上传新文件
  5. 保存更改
- **验证**：所有数据正确保存，页面正确跳转

#### 测试用例2：权限验证
- **步骤**：
  1. 非授权用户尝试编辑合同
  2. 提交更新请求
- **验证**：返回权限错误，数据不被更新

## 5. 部署检查清单

### 5.1 代码审查检查项
- [ ] 中间件顺序正确
- [ ] 文件处理逻辑完整
- [ ] 错误处理覆盖全面
- [ ] 数据验证严格
- [ ] 权限检查保持
- [ ] 代码注释清晰

### 5.2 功能测试检查项
- [ ] 仅文本更新功能正常
- [ ] 文件上传功能正常
- [ ] 错误处理正确
- [ ] 权限验证有效
- [ ] 性能表现良好

### 5.3 安全检查项
- [ ] 文件类型验证严格
- [ ] 文件大小限制有效
- [ ] 用户权限检查完整
- [ ] 输入数据清理彻底
- [ ] 错误信息不泄露敏感信息

## 6. 回滚计划

### 6.1 回滚触发条件
- 文件上传功能异常
- 数据更新失败率超过5%
- 系统性能显著下降
- 安全漏洞发现

### 6.2 回滚步骤
1. 立即停止新版本部署
2. 恢复到上一个稳定版本
3. 验证系统功能正常
4. 分析问题原因
5. 制定修复计划

## 7. 监控指标

### 7.1 功能指标
- 合同更新成功率
- 文件上传成功率
- 平均响应时间
- 错误率分布

### 7.2 性能指标
- API 响应时间
- 文件上传速度
- 内存使用情况
- 存储空间使用

## 8. 实施时间表

### 8.1 开发阶段（预计6-8小时）
- **第1-2小时**：修改路由中间件配置
- **第3-5小时**：实现文件处理逻辑
- **第6-7小时**：完善错误处理
- **第8小时**：代码审查和优化

### 8.2 测试阶段（预计4-6小时）
- **第1-2小时**：单元测试
- **第3-4小时**：集成测试
- **第5-6小时**：用户验收测试

### 8.3 部署阶段（预计2-3小时）
- **第1小时**：预生产环境部署和验证
- **第2小时**：生产环境部署
- **第3小时**：监控和验证

## 9. 成功标准

### 9.1 功能标准
- ✅ 合同编辑数据100%正确保存
- ✅ 文件上传功能正常工作
- ✅ 错误处理机制完善
- ✅ 用户体验流畅

### 9.2 性能标准
- ✅ API响应时间 < 2秒
- ✅ 文件上传速度合理
- ✅ 系统稳定性保持
- ✅ 错误率 < 1%

### 9.3 安全标准
- ✅ 文件类型验证有效
- ✅ 权限控制严格
- ✅ 数据清理彻底
- ✅ 无安全漏洞