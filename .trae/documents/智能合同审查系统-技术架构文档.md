## 1.Architecture design

```mermaid
  graph TD
    A[用户浏览器] --> B[React前端应用]
    B --> C[Node.js后端服务]
    C --> D[Supabase数据库]
    C --> E[AI服务接口]
    C --> F[文件存储服务]
    C --> G[OCR识别服务]

    subgraph "前端层"
        B
    end

    subgraph "后端服务层"
        C
    end

    subgraph "数据存储层"
        D
        F
    end

    subgraph "外部AI服务"
        E
        G
    end
```

## 2.Technology Description

* Frontend: React\@18 + TypeScript + Tailwind CSS + Vite + Ant Design

* Backend: Node.js\@18 + Express\@4 + TypeScript

* Database: Supabase (PostgreSQL)

* File Storage: Supabase Storage

* AI Services: OpenAI GPT-4 API + 百度OCR API

* Authentication: Supabase Auth

## 3.Route definitions

| Route       | Purpose            |
| ----------- | ------------------ |
| /           | 首页，显示系统概览和快速操作入口   |
| /contracts  | 合同管理页面，文件上传和分类归档   |
| /review     | 智能审查页面，AI风险识别和要素抽取 |
| /compare    | 条款对比页面，主副板对比分析     |
| /draft      | AI起草页面，智能合同生成      |
| /knowledge  | 知识库管理页面，法规和模板配置    |
| /statistics | 数据统计页面，审查效率和风险分析   |
| /login      | 用户登录页面             |
| /profile    | 用户个人资料和设置          |

## 4.API definitions

### 4.1 Core API

用户认证相关

```
POST /api/auth/login
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| email      | string     | true       | 用户邮箱        |
| password   | string     | true       | 用户密码        |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 登录是否成功      |
| token      | string     | JWT访问令牌     |
| user       | object     | 用户信息        |

合同管理相关

```
POST /api/contracts/upload
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| file       | File       | true       | 合同文件        |
| category   | string     | true       | 合同分类        |
| title      | string     | true       | 合同标题        |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 上传是否成功      |
| contractId | string     | 合同ID        |
| ocrText    | string     | OCR识别文本     |

智能审查相关

```
POST /api/review/analyze
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| contractId | string     | true       | 合同ID        |
| reviewType | string     | true       | 审查类型        |

Response:

| Param Name  | Param Type | Description |
| ----------- | ---------- | ----------- |
| success     | boolean    | 分析是否成功      |
| risks       | array      | 风险识别结果      |
| elements    | object     | 抽取的合同要素     |
| suggestions | array      | 修改建议        |

条款对比相关

```
POST /api/compare/contracts
```

Request:

| Param Name  | Param Type | isRequired | Description |
| ----------- | ---------- | ---------- | ----------- |
| contractId1 | string     | true       | 主合同ID       |
| contractId2 | string     | true       | 对比合同ID      |

Response:

| Param Name  | Param Type | Description |
| ----------- | ---------- | ----------- |
| success     | boolean    | 对比是否成功      |
| differences | array      | 差异分析结果      |
| similarity  | number     | 相似度评分       |

AI起草相关

```
POST /api/draft/generate
```

Request:

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| templateId | string     | true       | 模板ID        |
| parameters | object     | true       | 起草参数        |

Response:

| Param Name | Param Type | Description |
| ---------- | ---------- | ----------- |
| success    | boolean    | 生成是否成功      |
| content    | string     | 生成的合同内容     |
| draftId    | string     | 草稿ID        |

## 5.Server architecture diagram

```mermaid
graph TD
  A[客户端请求] --> B[路由层 Router]
  B --> C[中间件层 Middleware]
  C --> D[控制器层 Controller]
  D --> E[服务层 Service]
  E --> F[数据访问层 Repository]
  F --> G[(Supabase数据库)]
  
  E --> H[AI服务层 AI Service]
  H --> I[OpenAI API]
  H --> J[百度OCR API]
  
  E --> K[文件服务层 File Service]
  K --> L[Supabase Storage]

  subgraph 服务器架构
      B
      C
      D
      E
      F
      H
      K
  end
```

## 6.Data model

### 6.1 Data model definition

```mermaid
erDiagram
  USERS ||--o{ CONTRACTS : uploads
  USERS ||--o{ REVIEW_TASKS : creates
  CONTRACTS ||--o{ REVIEW_TASKS : has
  CONTRACTS ||--o{ CONTRACT_ELEMENTS : contains
  REVIEW_TASKS ||--o{ RISK_ITEMS : identifies
  TEMPLATES ||--o{ CONTRACTS : generates
  KNOWLEDGE_BASE ||--o{ REVIEW_RULES : defines

  USERS {
      uuid id PK
      string email
      string name
      string role
      timestamp created_at
      timestamp updated_at
  }
  
  CONTRACTS {
      uuid id PK
      uuid user_id FK
      string title
      string category
      string file_path
      text content
      text ocr_content
      text file_url
      string counterparty
      decimal amount
      date start_date
      date end_date
      string risk_level
      string status
      timestamp created_at
      timestamp updated_at
  }
  
  REVIEW_TASKS {
      uuid id PK
      uuid contract_id FK
      uuid user_id FK
      string review_type
      json analysis_result
      string status
      timestamp created_at
      timestamp completed_at
  }
  
  CONTRACT_ELEMENTS {
      uuid id PK
      uuid contract_id FK
      string element_type
      text element_value
      float confidence_score
      timestamp extracted_at
  }
  
  RISK_ITEMS {
      uuid id PK
      uuid review_task_id FK
      string risk_type
      string risk_level
      text description
      text suggestion
      timestamp identified_at
  }
  
  TEMPLATES {
      uuid id PK
      string name
      string category
      text content
      json parameters
      boolean is_active
      timestamp created_at
  }
  
  KNOWLEDGE_BASE {
      uuid id PK
      string type
      string title
      text content
      json metadata
      timestamp created_at
      timestamp updated_at
  }
  
  REVIEW_RULES {
      uuid id PK
      uuid knowledge_base_id FK
      string rule_name
      text rule_content
      string severity
      boolean is_active
  }
```

### 6.2 Data Definition Language

用户表 (users)

```sql
-- 创建用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'legal_staff', 'legal_manager', 'admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- 设置权限
GRANT SELECT ON users TO anon;
GRANT ALL PRIVILEGES ON users TO authenticated;
```

合同表 (contracts)

```sql
-- 创建合同表
CREATE TABLE contracts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    file_path TEXT NOT NULL,
    content TEXT,
    ocr_content TEXT,
    file_url TEXT,
    counterparty VARCHAR(255),
    amount DECIMAL(15,2),
    start_date DATE,
    end_date DATE,
    risk_level VARCHAR(20) CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    status VARCHAR(20) DEFAULT 'uploaded' CHECK (status IN ('uploaded', 'processing', 'reviewed', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_contracts_user_id ON contracts(user_id);
CREATE INDEX idx_contracts_category ON contracts(category);
CREATE INDEX idx_contracts_status ON contracts(status);
CREATE INDEX idx_contracts_risk_level ON contracts(risk_level);
CREATE INDEX idx_contracts_counterparty ON contracts(counterparty);
CREATE INDEX idx_contracts_created_at ON contracts(created_at DESC);

-- 设置权限
GRANT SELECT ON contracts TO anon;
GRANT ALL PRIVILEGES ON contracts TO authenticated;
```

审查任务表 (review\_tasks)

```sql
-- 创建审查任务表
CREATE TABLE review_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID REFERENCES contracts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    review_type VARCHAR(50) NOT NULL,
    analysis_result JSONB,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- 创建索引
CREATE INDEX idx_review_tasks_contract_id ON review_tasks(contract_id);
CREATE INDEX idx_review_tasks_user_id ON review_tasks(user_id);
CREATE INDEX idx_review_tasks_status ON review_tasks(status);
CREATE INDEX idx_review_tasks_created_at ON review_tasks(created_at DESC);

-- 设置权限
GRANT SELECT ON review_tasks TO anon;
GRANT ALL PRIVILEGES ON review_tasks TO authenticated;
```

合同要素表 (contract\_elements)

```sql
-- 创建合同要素表
CREATE TABLE contract_elements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    contract_id UUID REFERENCES contracts(id) ON DELETE CASCADE,
    element_type VARCHAR(100) NOT NULL,
    element_value TEXT NOT NULL,
    confidence_score DECIMAL(3,2) DEFAULT 0.0,
    extracted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_contract_elements_contract_id ON contract_elements(contract_id);
CREATE INDEX idx_contract_elements_type ON contract_elements(element_type);
CREATE INDEX idx_contract_elements_confidence ON contract_elements(confidence_score DESC);

-- 设置权限
GRANT SELECT ON contract_elements TO anon;
GRANT ALL PRIVILEGES ON contract_elements TO authenticated;
```

风险项表 (risk\_items)

```sql
-- 创建风险项表
CREATE TABLE risk_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    review_task_id UUID REFERENCES review_tasks(id) ON DELETE CASCADE,
    risk_type VARCHAR(100) NOT NULL,
    risk_level VARCHAR(20) CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    suggestion TEXT,
    identified_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_risk_items_review_task_id ON risk_items(review_task_id);
CREATE INDEX idx_risk_items_risk_level ON risk_items(risk_level);
CREATE INDEX idx_risk_items_risk_type ON risk_items(risk_type);

-- 设置权限
GRANT SELECT ON risk_items TO anon;
GRANT ALL PRIVILEGES ON risk_items TO authenticated;
```

模板表 (templates)

```sql
-- 创建模板表
CREATE TABLE templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    parameters JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_templates_category ON templates(category);
CREATE INDEX idx_templates_is_active ON templates(is_active);
CREATE INDEX idx_templates_name ON templates(name);

-- 设置权限
GRANT SELECT ON templates TO anon;
GRANT ALL PRIVILEGES ON templates TO authenticated;
```

知识库表 (knowledge\_base)

```sql
-- 创建知识库表
CREATE TABLE knowledge_base (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL CHECK (type IN ('regulation', 'case_law', 'template', 'guideline')),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_knowledge_base_type ON knowledge_base(type);
CREATE INDEX idx_knowledge_base_title ON knowledge_base(title);
CREATE INDEX idx_knowledge_base_created_at ON knowledge_base(created_at DESC);

-- 设置权限
GRANT SELECT ON knowledge_base TO anon;
GRANT ALL PRIVILEGES ON knowledge_base TO authenticated;
```

审查规则表 (review\_rules)

```sql
-- 创建审查规则表
CREATE TABLE review_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    knowledge_base_id UUID REFERENCES knowledge_base(id) ON DELETE CASCADE,
    rule_name VARCHAR(255) NOT NULL,
    rule_content TEXT NOT NULL,
    severity VARCHAR(20) CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    is_active BOOLEAN DEFAULT true
);

-- 创建索引
CREATE INDEX idx_review_rules_knowledge_base_id ON review_rules(knowledge_base_id);
CREATE INDEX idx_review_rules_severity ON review_rules(severity);
CREATE INDEX idx_review_rules_is_active ON review_rules(is_active);

-- 设置权限
GRANT SELECT ON review_rules TO anon;
GRANT ALL PRIVILEGES ON review_rules TO authenticated;
```

初始化数据

```sql
-- 插入默认管理员用户
INSERT INTO users (email, name, role) VALUES 
('<EMAIL>', '系统管理员', 'admin');

-- 插入默认合同模板
INSERT INTO templates (name, category, content, parameters) VALUES 
('标准服务合同模板', '服务合同', '合同模板内容...', '{"fields": ["party_a", "party_b", "service_scope", "payment_terms"]}'),
('采购合同模板', '采购合同', '采购合同模板内容...', '{"fields": ["supplier", "buyer", "goods", "delivery_terms"]}');

-- 插入基础知识库数据
INSERT INTO knowledge_base (type, title, content, metadata) VALUES 
('regulation', '合同法基本原则', '合同法相关法规内容...', '{"source": "中华人民共和国合同法", "effective_date": "2021-01-01"}'),
('guideline', '合同审查指导原则', '合同审查的基本指导原则...', '{"version": "1.0", "department": "法
```

