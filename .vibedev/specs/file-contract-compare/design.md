# 文件合同对比模块 - 技术设计文档

## 概述

文件合同对比模块是一个独立的功能模块，允许用户直接上传PDF和Word格式的合同文件进行实时对比分析。该模块与现有的数据库合同对比功能（Compare.tsx）完全独立，提供直观的左右分栏对比界面，智能识别并标记条款的新增、修改和删除变化。

### 设计原则

1. **功能独立性**：与现有Compare.tsx完全分离，确保零影响
2. **基础设施复用**：最大化利用现有的文档解析、文件存储和UI组件
3. **用户体验优先**：提供直观、响应式的对比界面
4. **性能优化**：支持大文件处理和并发用户访问
5. **安全保障**：确保文件安全处理和隐私保护

## 架构设计

### 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[FileCompare.tsx] --> B[文件上传组件]
        A --> C[对比结果展示组件]
        A --> D[差异导航组件]
        A --> E[导出功能组件]
    end
    
    subgraph "API层"
        F[/api/review/file-compare] --> G[文件上传处理]
        F --> H[文档解析调用]
        F --> I[条款对比算法]
        F --> J[结果格式化]
    end
    
    subgraph "服务层"
        K[DocumentParserService] --> L[PDF解析]
        K --> M[Word解析]
        N[FileStorageService] --> O[临时文件存储]
        P[FileCompareService] --> Q[条款提取]
        P --> R[差异分析]
        P --> S[相似度计算]
    end
    
    subgraph "存储层"
        T[Supabase Storage] --> U[临时文件存储]
        V[内存缓存] --> W[解析结果缓存]
    end
    
    A --> F
    F --> K
    F --> N
    F --> P
    K --> T
    N --> T
    P --> V
```

### 技术栈

**前端技术栈：**
- React 18 + TypeScript
- Ant Design 5.x (复用现有组件库)
- React Hooks (状态管理)
- CSS-in-JS (样式处理)

**后端技术栈：**
- Node.js + Express + TypeScript
- Multer (文件上传处理)
- pdf-parse (PDF文档解析)
- mammoth (Word文档解析)
- Supabase Storage (文件存储)

**算法库：**
- diff算法 (文本差异检测)
- 自然语言处理 (条款语义分析)
- 相似度计算 (余弦相似度)

## 组件设计

### 1. 前端组件架构

#### 1.1 FileCompare.tsx (主组件)

```typescript
interface FileCompareProps {}

interface FileCompareState {
  // 文件上传状态
  primaryFile: File | null;
  secondaryFile: File | null;
  uploadingPrimary: boolean;
  uploadingSecondary: boolean;
  
  // 对比状态
  comparing: boolean;
  comparisonResult: FileComparisonResult | null;
  
  // UI状态
  activeTab: 'upload' | 'result';
  showDiffOnly: boolean;
  currentDiffIndex: number;
}

const FileCompare: React.FC<FileCompareProps> = () => {
  // 组件实现
};
```

#### 1.2 FileUploadArea.tsx (文件上传组件)

```typescript
interface FileUploadAreaProps {
  title: string;
  file: File | null;
  uploading: boolean;
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  accept: string;
  maxSize: number;
}

const FileUploadArea: React.FC<FileUploadAreaProps> = ({
  title,
  file,
  uploading,
  onFileSelect,
  onFileRemove,
  accept,
  maxSize
}) => {
  // 拖拽上传实现
  // 文件验证逻辑
  // 上传进度显示
};
```

#### 1.3 ComparisonResultView.tsx (对比结果展示)

```typescript
interface ComparisonResultViewProps {
  result: FileComparisonResult;
  showDiffOnly: boolean;
  onToggleDiffOnly: (show: boolean) => void;
  onNavigateDiff: (direction: 'prev' | 'next') => void;
  currentDiffIndex: number;
}

const ComparisonResultView: React.FC<ComparisonResultViewProps> = ({
  result,
  showDiffOnly,
  onToggleDiffOnly,
  onNavigateDiff,
  currentDiffIndex
}) => {
  // 左右分栏布局
  // 差异高亮显示
  // 同步滚动实现
};
```

#### 1.4 DiffNavigator.tsx (差异导航组件)

```typescript
interface DiffNavigatorProps {
  differences: DifferenceItem[];
  currentIndex: number;
  onNavigate: (index: number) => void;
  onExport: () => void;
}

const DiffNavigator: React.FC<DiffNavigatorProps> = ({
  differences,
  currentIndex,
  onNavigate,
  onExport
}) => {
  // 差异列表展示
  // 快速导航功能
  // 统计信息显示
};
```

### 2. 后端服务设计

#### 2.1 FileCompareService.ts (核心对比服务)

```typescript
export class FileCompareService {
  /**
   * 执行文件对比分析
   */
  async compareFiles(
    primaryFileBuffer: Buffer,
    secondaryFileBuffer: Buffer,
    primaryMimeType: string,
    secondaryMimeType: string,
    options: ComparisonOptions
  ): Promise<FileComparisonResult> {
    // 1. 文档解析
    const primaryContent = await this.parseDocument(primaryFileBuffer, primaryMimeType);
    const secondaryContent = await this.parseDocument(secondaryFileBuffer, secondaryMimeType);
    
    // 2. 条款提取
    const primaryClauses = this.extractClauses(primaryContent);
    const secondaryClauses = this.extractClauses(secondaryContent);
    
    // 3. 差异分析
    const differences = this.analyzeDifferences(primaryClauses, secondaryClauses);
    
    // 4. 相似度计算
    const similarity = this.calculateSimilarity(primaryContent, secondaryContent);
    
    // 5. 结果格式化
    return this.formatResult(differences, similarity, primaryContent, secondaryContent);
  }
  
  /**
   * 条款提取算法
   */
  private extractClauses(content: string): ClauseItem[] {
    // 基于规则的条款识别
    // 段落分割和语义分析
    // 条款类型分类
  }
  
  /**
   * 差异分析算法
   */
  private analyzeDifferences(
    primaryClauses: ClauseItem[],
    secondaryClauses: ClauseItem[]
  ): DifferenceItem[] {
    // 使用改进的diff算法
    // 语义相似度匹配
    // 差异类型分类
  }
  
  /**
   * 相似度计算
   */
  private calculateSimilarity(content1: string, content2: string): number {
    // 余弦相似度计算
    // 词频-逆文档频率(TF-IDF)
    // 语义相似度分析
  }
}
```

#### 2.2 API路由设计

```typescript
// /api/review/file-compare
router.post('/file-compare', 
  authenticateToken,
  upload.fields([
    { name: 'primaryFile', maxCount: 1 },
    { name: 'secondaryFile', maxCount: 1 }
  ]),
  async (req: Request, res: Response) => {
    try {
      // 1. 文件验证
      const { primaryFile, secondaryFile } = req.files as any;
      
      if (!primaryFile || !secondaryFile) {
        return res.status(400).json({
          success: false,
          message: '请上传主板和副板文件'
        });
      }
      
      // 2. 文件格式验证
      const supportedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      
      if (!supportedTypes.includes(primaryFile[0].mimetype) || 
          !supportedTypes.includes(secondaryFile[0].mimetype)) {
        return res.status(400).json({
          success: false,
          message: '仅支持PDF和Word格式文件'
        });
      }
      
      // 3. 执行对比分析
      const comparisonOptions: ComparisonOptions = {
        highlightDifferences: true,
        includeMetadata: true,
        analysisDepth: req.body.analysisDepth || 'standard'
      };
      
      const result = await fileCompareService.compareFiles(
        primaryFile[0].buffer,
        secondaryFile[0].buffer,
        primaryFile[0].mimetype,
        secondaryFile[0].mimetype,
        comparisonOptions
      );
      
      // 4. 返回结果
      res.json({
        success: true,
        result: result,
        metadata: {
          primaryFile: {
            name: primaryFile[0].originalname,
            size: primaryFile[0].size,
            type: primaryFile[0].mimetype
          },
          secondaryFile: {
            name: secondaryFile[0].originalname,
            size: secondaryFile[0].size,
            type: secondaryFile[0].mimetype
          },
          processedAt: new Date().toISOString()
        }
      });
      
    } catch (error) {
      console.error('文件对比失败:', error);
      res.status(500).json({
        success: false,
        message: '文件对比处理失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
);
```

## 数据模型

### 1. 前端数据接口

```typescript
// 文件对比结果接口
export interface FileComparisonResult {
  id: string;
  summary: {
    totalClauses: number;
    differencesFound: number;
    similarityScore: number;
    riskAssessment: 'low' | 'medium' | 'high';
    processingTime: number;
  };
  differences: DifferenceItem[];
  similarities: SimilarityItem[];
  primaryContent: DocumentContent;
  secondaryContent: DocumentContent;
  metadata: ComparisonMetadata;
}

// 差异项接口
export interface DifferenceItem {
  id: string;
  type: 'added' | 'modified' | 'deleted';
  severity: 'low' | 'medium' | 'high';
  clauseType: string;
  primaryText?: string;
  secondaryText?: string;
  description: string;
  recommendation?: string;
  position: {
    primary?: TextPosition;
    secondary?: TextPosition;
  };
}

// 相似项接口
export interface SimilarityItem {
  id: string;
  clauseType: string;
  primaryText: string;
  secondaryText: string;
  matchPercentage: number;
  position: {
    primary: TextPosition;
    secondary: TextPosition;
  };
}

// 文档内容接口
export interface DocumentContent {
  text: string;
  paragraphs: ParagraphItem[];
  clauses: ClauseItem[];
  metadata: DocumentMetadata;
}

// 段落项接口
export interface ParagraphItem {
  id: string;
  text: string;
  startIndex: number;
  endIndex: number;
  type: 'title' | 'content' | 'list' | 'table';
}

// 条款项接口
export interface ClauseItem {
  id: string;
  type: string;
  title: string;
  content: string;
  startIndex: number;
  endIndex: number;
  importance: 'low' | 'medium' | 'high';
}

// 文本位置接口
export interface TextPosition {
  startIndex: number;
  endIndex: number;
  lineNumber: number;
  columnNumber: number;
}

// 对比选项接口
export interface ComparisonOptions {
  highlightDifferences: boolean;
  includeMetadata: boolean;
  analysisDepth: 'basic' | 'standard' | 'detailed';
  focusAreas?: string[];
}

// 对比元数据接口
export interface ComparisonMetadata {
  primaryFile: FileMetadata;
  secondaryFile: FileMetadata;
  processedAt: string;
  processingTime: number;
  algorithm: string;
  version: string;
}

// 文件元数据接口
export interface FileMetadata {
  name: string;
  size: number;
  type: string;
  lastModified?: string;
  pageCount?: number;
  wordCount?: number;
}
```

### 2. 后端数据处理

```typescript
// 文档解析结果
export interface ParsedDocument {
  success: boolean;
  content: string;
  metadata: {
    pageCount?: number;
    wordCount: number;
    confidence?: number;
  };
  error?: string;
}

// 条款提取结果
export interface ExtractedClauses {
  clauses: ClauseItem[];
  structure: DocumentStructure;
  confidence: number;
}

// 文档结构
export interface DocumentStructure {
  sections: SectionItem[];
  hierarchy: HierarchyLevel[];
}

// 章节项
export interface SectionItem {
  id: string;
  title: string;
  level: number;
  startIndex: number;
  endIndex: number;
  subsections: SectionItem[];
}
```

## 算法设计

### 1. 条款提取算法

```typescript
class ClauseExtractor {
  /**
   * 基于规则的条款提取
   */
  extractByRules(content: string): ClauseItem[] {
    const clauses: ClauseItem[] = [];
    
    // 1. 标题识别（基于格式和关键词）
    const titlePatterns = [
      /第[一二三四五六七八九十\d]+条/g,
      /\d+\.\s*[^\n]+/g,
      /[一二三四五六七八九十]\s*、\s*[^\n]+/g
    ];
    
    // 2. 内容分割
    const paragraphs = this.splitIntoParagraphs(content);
    
    // 3. 条款类型识别
    for (const paragraph of paragraphs) {
      const clauseType = this.identifyClauseType(paragraph);
      if (clauseType) {
        clauses.push({
          id: this.generateId(),
          type: clauseType,
          title: this.extractTitle(paragraph),
          content: paragraph.text,
          startIndex: paragraph.startIndex,
          endIndex: paragraph.endIndex,
          importance: this.assessImportance(clauseType)
        });
      }
    }
    
    return clauses;
  }
  
  /**
   * 条款类型识别
   */
  private identifyClauseType(paragraph: ParagraphItem): string | null {
    const keywords = {
      '付款条款': ['付款', '支付', '费用', '价款', '报酬'],
      '违约责任': ['违约', '责任', '赔偿', '损失', '承担'],
      '知识产权': ['知识产权', '专利', '商标', '著作权', '版权'],
      '保密条款': ['保密', '机密', '秘密', '泄露', '披露'],
      '争议解决': ['争议', '纠纷', '仲裁', '诉讼', '管辖'],
      '合同期限': ['期限', '有效期', '生效', '终止', '到期']
    };
    
    for (const [type, words] of Object.entries(keywords)) {
      if (words.some(word => paragraph.text.includes(word))) {
        return type;
      }
    }
    
    return null;
  }
}
```

### 2. 差异分析算法

```typescript
class DifferenceAnalyzer {
  /**
   * 改进的diff算法
   */
  analyzeDifferences(
    primaryClauses: ClauseItem[],
    secondaryClauses: ClauseItem[]
  ): DifferenceItem[] {
    const differences: DifferenceItem[] = [];
    
    // 1. 建立条款映射
    const primaryMap = this.buildClauseMap(primaryClauses);
    const secondaryMap = this.buildClauseMap(secondaryClauses);
    
    // 2. 查找删除的条款
    for (const [type, primaryClause] of primaryMap) {
      if (!secondaryMap.has(type)) {
        differences.push({
          id: this.generateId(),
          type: 'deleted',
          severity: this.assessSeverity(type),
          clauseType: type,
          primaryText: primaryClause.content,
          description: `删除了${type}条款`,
          recommendation: `建议保留${type}条款以确保合同完整性`,
          position: {
            primary: {
              startIndex: primaryClause.startIndex,
              endIndex: primaryClause.endIndex,
              lineNumber: this.getLineNumber(primaryClause.startIndex),
              columnNumber: 0
            }
          }
        });
      }
    }
    
    // 3. 查找新增的条款
    for (const [type, secondaryClause] of secondaryMap) {
      if (!primaryMap.has(type)) {
        differences.push({
          id: this.generateId(),
          type: 'added',
          severity: this.assessSeverity(type),
          clauseType: type,
          secondaryText: secondaryClause.content,
          description: `新增了${type}条款`,
          recommendation: `请审查新增的${type}条款内容`,
          position: {
            secondary: {
              startIndex: secondaryClause.startIndex,
              endIndex: secondaryClause.endIndex,
              lineNumber: this.getLineNumber(secondaryClause.startIndex),
              columnNumber: 0
            }
          }
        });
      }
    }
    
    // 4. 查找修改的条款
    for (const [type, primaryClause] of primaryMap) {
      const secondaryClause = secondaryMap.get(type);
      if (secondaryClause) {
        const similarity = this.calculateTextSimilarity(
          primaryClause.content,
          secondaryClause.content
        );
        
        if (similarity < 0.9) { // 相似度阈值
          differences.push({
            id: this.generateId(),
            type: 'modified',
            severity: this.assessModificationSeverity(similarity),
            clauseType: type,
            primaryText: primaryClause.content,
            secondaryText: secondaryClause.content,
            description: `${type}条款内容发生变化`,
            recommendation: `请仔细审查${type}条款的修改内容`,
            position: {
              primary: {
                startIndex: primaryClause.startIndex,
                endIndex: primaryClause.endIndex,
                lineNumber: this.getLineNumber(primaryClause.startIndex),
                columnNumber: 0
              },
              secondary: {
                startIndex: secondaryClause.startIndex,
                endIndex: secondaryClause.endIndex,
                lineNumber: this.getLineNumber(secondaryClause.startIndex),
                columnNumber: 0
              }
            }
          });
        }
      }
    }
    
    return differences.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }
  
  /**
   * 文本相似度计算
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    // 使用余弦相似度算法
    const vector1 = this.textToVector(text1);
    const vector2 = this.textToVector(text2);
    
    return this.cosineSimilarity(vector1, vector2);
  }
  
  /**
   * 文本向量化
   */
  private textToVector(text: string): Map<string, number> {
    const words = text.toLowerCase().match(/[\u4e00-\u9fa5\w]+/g) || [];
    const vector = new Map<string, number>();
    
    for (const word of words) {
      vector.set(word, (vector.get(word) || 0) + 1);
    }
    
    return vector;
  }
  
  /**
   * 余弦相似度计算
   */
  private cosineSimilarity(vector1: Map<string, number>, vector2: Map<string, number>): number {
    const allWords = new Set([...vector1.keys(), ...vector2.keys()]);
    
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;
    
    for (const word of allWords) {
      const val1 = vector1.get(word) || 0;
      const val2 = vector2.get(word) || 0;
      
      dotProduct += val1 * val2;
      norm1 += val1 * val1;
      norm2 += val2 * val2;
    }
    
    if (norm1 === 0 || norm2 === 0) return 0;
    
    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }
}
```

### 3. 相似度计算算法

```typescript
class SimilarityCalculator {
  /**
   * 综合相似度计算
   */
  calculateOverallSimilarity(
    primaryContent: string,
    secondaryContent: string,
    differences: DifferenceItem[]
  ): number {
    // 1. 文本相似度 (40%)
    const textSimilarity = this.calculateTextSimilarity(primaryContent, secondaryContent);
    
    // 2. 结构相似度 (30%)
    const structureSimilarity = this.calculateStructureSimilarity(primaryContent, secondaryContent);
    
    // 3. 条款相似度 (30%)
    const clauseSimilarity = this.calculateClauseSimilarity(differences);
    
    // 加权平均
    const overallSimilarity = 
      textSimilarity * 0.4 + 
      structureSimilarity * 0.3 + 
      clauseSimilarity * 0.3;
    
    return Math.round(overallSimilarity * 100);
  }
  
  /**
   * 结构相似度计算
   */
  private calculateStructureSimilarity(content1: string, content2: string): number {
    const structure1 = this.extractStructure(content1);
    const structure2 = this.extractStructure(content2);
    
    // 比较章节数量、层级结构等
    const sectionCountSimilarity = Math.min(structure1.sections.length, structure2.sections.length) / 
                                  Math.max(structure1.sections.length, structure2.sections.length);
    
    return sectionCountSimilarity;
  }
  
  /**
   * 条款相似度计算
   */
  private calculateClauseSimilarity(differences: DifferenceItem[]): number {
    if (differences.length === 0) return 1.0;
    
    const totalWeight = differences.reduce((sum, diff) => {
      const weight = diff.severity === 'high' ? 3 : diff.severity === 'medium' ? 2 : 1;
      return sum + weight;
    }, 0);
    
    // 基于差异的严重程度计算相似度
    const maxPossibleWeight = differences.length * 3; // 假设所有差异都是高严重性
    
    return Math.max(0, 1 - (totalWeight / maxPossibleWeight));
  }
}
```

## 错误处理

### 1. 前端错误处理

```typescript
// 错误类型定义
export enum FileCompareErrorType {
  FILE_UPLOAD_ERROR = 'FILE_UPLOAD_ERROR',
  FILE_FORMAT_ERROR = 'FILE_FORMAT_ERROR',
  FILE_SIZE_ERROR = 'FILE_SIZE_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  COMPARISON_ERROR = 'COMPARISON_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR'
}

// 错误处理类
export class FileCompareErrorHandler {
  static handleError(error: any): { type: FileCompareErrorType; message: string; action?: string } {
    if (error.response) {
      // HTTP错误
      const status = error.response.status;
      const data = error.response.data;
      
      switch (status) {
        case 400:
          return {
            type: FileCompareErrorType.FILE_FORMAT_ERROR,
            message: data.message || '文件格式不支持',
            action: '请上传PDF或Word格式的文件'
          };
        case 413:
          return {
            type: FileCompareErrorType.FILE_SIZE_ERROR,
            message: '文件大小超出限制',
            action: '请上传小于50MB的文件'
          };
        case 500:
          return {
            type: FileCompareErrorType.SERVER_ERROR,
            message: '服务器处理失败',
            action: '请稍后重试或联系技术支持'
          };
        default:
          return {
            type: FileCompareErrorType.NETWORK_ERROR,
            message: '网络请求失败',
            action: '请检查网络连接后重试'
          };
      }
    } else if (error.request) {
      // 网络错误
      return {
        type: FileCompareErrorType.NETWORK_ERROR,
        message: '网络连接失败',
        action: '请检查网络连接后重试'
      };
    } else {
      // 其他错误
      return {
        type: FileCompareErrorType.COMPARISON_ERROR,
        message: error.message || '对比处理失败',
        action: '请重新上传文件并重试'
      };
    }
  }
}

// 错误边界组件
export class FileCompareErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('FileCompare组件错误:', error, errorInfo);
    // 可以在这里上报错误到监控系统
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <Result
          status="error"
          title="文件对比功能异常"
          subTitle="抱歉，文件对比功能遇到了问题"
          extra={[
            <Button type="primary" onClick={() => window.location.reload()}>
              刷新页面
            </Button>,
            <Button onClick={() => this.setState({ hasError: false })}>
              重试
            </Button>
          ]}
        />
      );
    }
    
    return this.props.children;
  }
}
```

### 2. 后端错误处理

```typescript
// 错误类型定义
export class FileCompareError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'FileCompareError';
  }
}

// 错误处理中间件
export const fileCompareErrorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  console.error('文件对比错误:', error);
  
  if (error instanceof FileCompareError) {
    return res.status(error.statusCode).json({
      success: false,
      message: error.message,
      code: error.code,
      details: process.env.NODE_ENV === 'development' ? error.details : undefined
    });
  }
  
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      success: false,
      message: '文件大小超出限制（最大50MB）',
      code: 'FILE_TOO_LARGE'
    });
  }
  
  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      success: false,
      message: '不支持的文件格式',
      code: 'UNSUPPORTED_FORMAT'
    });
  }
  
  // 默认错误处理
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    code: 'INTERNAL_ERROR'
  });
};

// 文档解析错误处理
export class DocumentParsingError extends FileCompareError {
  constructor(message: string, details?: any) {
    super(message, 'DOCUMENT_PARSING_ERROR', 400, details);
  }
}

// 对比算法错误处理
export class ComparisonAlgorithmError extends FileCompareError {
  constructor(message: string, details?: any) {
    super(message, 'COMPARISON_ALGORITHM_ERROR', 500, details);
  }
}
```

## 测试策略

### 1. 单元测试

```typescript
// 文档解析测试
describe('DocumentParserService', () => {
  let service: DocumentParserService;
  
  beforeEach(() => {
    service = new DocumentParserService();
  });
  
  describe('parseDocument', () => {
    it('应该成功解析PDF文件', async () => {
      const buffer = fs.readFileSync('test/fixtures/sample.pdf');
      const result = await service.parseDocument(buffer, 'application/pdf', 'sample.pdf');
      
      expect(result.success).toBe(true);
      expect(result.content).toBeDefined();
      expect(result.content.length).toBeGreaterThan(0);
    });
    
    it('应该成功解析Word文件', async () => {
      const buffer = fs.readFileSync('test/fixtures/sample.docx');
      const result = await service.parseDocument(buffer, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'sample.docx');
      
      expect(result.success).toBe(true);
      expect(result.content).toBeDefined();
    });
    
    it('应该处理不支持的文件格式', async () => {
      const buffer = Buffer.from('test content');
      const result = await service.parseDocument(buffer, 'text/html', 'test.html');
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });
});

// 对比算法测试
describe('FileCompareService', () => {
  let service: FileCompareService;
  
  beforeEach(() => {
    service = new FileCompareService();
  });
  
  describe('compareFiles', () => {
    it('应该识别新增的条款', async () => {
      const primaryBuffer = Buffer.from('第一条 付款条款\n第二条 违约责任');
      const secondaryBuffer = Buffer.from('第一条 付款条款\n第二条 违约责任\n第三条 保密条款');
      
      const result = await service.compareFiles(
        primaryBuffer,
        secondaryBuffer,
        'text/plain',
        'text/plain',
        { highlightDifferences: true, includeMetadata: true, analysisDepth: 'standard' }
      );
      
      expect(result.differences).toHaveLength(1);
      expect(result.differences[0].type).toBe('added');
      expect(result.differences[0].clauseType).toBe('保密条款');
    });
    
    it('应该识别删除的条款', async () => {
      const primaryBuffer = Buffer.from('第一条 付款条款\n第二条 违约责任\n第三条 保密条款');
      const secondaryBuffer = Buffer.from('第一条 付款条款\n第二条 违约责任');
      
      const result = await service.compareFiles(
        primaryBuffer,
        secondaryBuffer,
        'text/plain',
        'text/plain',
        { highlightDifferences: true, includeMetadata: true, analysisDepth: 'standard' }
      );
      
      expect(result.differences).toHaveLength(1);
      expect(result.differences[0].type).toBe('deleted');
      expect(result.differences[0].clauseType).toBe('保密条款');
    });
    
    it('应该计算正确的相似度', async () => {
      const primaryBuffer = Buffer.from('相同的内容');
      const secondaryBuffer = Buffer.from('相同的内容');
      
      const result = await service.compareFiles(
        primaryBuffer,
        secondaryBuffer,
        'text/plain',
        'text/plain',
        { highlightDifferences: true, includeMetadata: true, analysisDepth: 'standard' }
      );
      
      expect(result.summary.similarityScore).toBe(100);
    });
  });
});
```

### 2. 集成测试

```typescript
// API集成测试
describe('File Compare API', () => {
  let app: Express;
  
  beforeAll(async () => {
    app = await createTestApp();
  });
  
  describe('POST /api/review/file-compare', () => {
    it('应该成功处理文件对比请求', async () => {
      const primaryFile = fs.readFileSync('test/fixtures/contract1.pdf');
      const secondaryFile = fs.readFileSync('test/fixtures/contract2.pdf');
      
      const response = await request(app)
        .post('/api/review/file-compare')
        .set('Authorization', 'Bearer valid-token')
        .attach('primaryFile', primaryFile, 'contract1.pdf')
        .attach('secondaryFile', secondaryFile, 'contract2.pdf')
        .expect(200);
      
      expect(response.body.success).toBe(true);
      expect(response.body.result).toBeDefined();
      expect(response.body.result.differences).toBeInstanceOf(Array);
      expect(response.body.result.summary).toBeDefined();
    });
    
    it('应该拒绝不支持的文件格式', async () => {
      const invalidFile = Buffer.from('invalid content');
      
      const response = await request(app)
        .post('/api/review/file-compare')
        .set('Authorization', 'Bearer valid-token')
        .attach('primaryFile', invalidFile, 'test.txt')
        .attach('secondaryFile', invalidFile, 'test.txt')
        .expect(400);
      
      expect(response.body.success).toBe(false);
      expect(response.body.message).toContain('仅支持PDF和Word格式文件');
    });
    
    it('应该要求认证', async () => {
      const response = await request(app)
        .post('/api/review/file-compare')
        .expect(401);
      
      expect(response.body.success).toBe(false);
    });
  });
});
```

### 3. 端到端测试

```typescript
// E2E测试
describe('File Compare E2E', () => {
  let page: Page;
  
  beforeAll(async () => {
    page = await browser.newPage();
    await page.goto('http://localhost:3000/file-compare');
  });
  
  afterAll(async () => {
    await page.close();
  });
  
  it('应该完成完整的文件对比流程', async () => {
    // 1. 上传主板文件
    const primaryFileInput = await page.$('input[data-testid="primary-file-input"]');
    await primaryFileInput.uploadFile('test/fixtures/contract1.pdf');
    
    // 2. 上传副板文件
    const secondaryFileInput = await page.$('input[data-testid="secondary-file-input"]');
    await secondaryFileInput.uploadFile('test/fixtures/contract2.pdf');
    
    // 3. 点击对比按钮
    await page.click('button[data-testid="compare-button"]');
    
    // 4. 等待对比完成
    await page.waitForSelector('[data-testid="comparison-result"]', { timeout: 30000 });
    
    // 5. 验证结果显示
    const resultElement = await page.$('[data-testid="comparison-result"]');
    expect(resultElement).toBeTruthy();
    
    // 6. 验证差异列表
    const differencesCount = await page.$$eval(
      '[data-testid="difference-item"]',
      elements => elements.length
    );
    expect(differencesCount).toBeGreaterThan(0);
    
    // 7. 测试导出功能
    await page.click('button[data-testid="export-button"]');
    // 验证下载开始
  });
  
  it('应该正确处理文件上传错误', async () => {
    // 上传不支持的文件格式
    const fileInput = await page.$('input[data-testid="primary-file-input"]');
    await fileInput.uploadFile('test/fixtures/invalid.txt');
    
    // 验证错误提示
    await page.waitForSelector('[data-testid="error-message"]');
    const errorText = await page.$eval('[data-testid="error-message"]', el => el.textContent);
    expect(errorText).toContain('不支持的文件格式');
  });
});
```

### 4. 性能测试

```typescript
// 性能测试
describe('File Compare Performance', () => {
  it('应该在30秒内完成普通文件对比', async () => {
    const startTime = Date.now();
    
    const primaryBuffer = fs.readFileSync('test/fixtures/medium-contract.pdf');
    const secondaryBuffer = fs.readFileSync('test/fixtures/medium-contract2.pdf');
    
    const service = new FileCompareService();
    const result = await service.compareFiles(
      primaryBuffer,
      secondaryBuffer,
      'application/pdf',
      'application/pdf',
      { highlightDifferences: true, includeMetadata: true, analysisDepth: 'standard' }
    );
    
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    expect(processingTime).toBeLessThan(30000); // 30秒
    expect(result.summary.processingTime).toBeLessThan(30000);
  });
  
  it('应该支持并发处理', async () => {
    const concurrentRequests = 5;
    const promises = [];
    
    for (let i = 0; i < concurrentRequests; i++) {
      const promise = request(app)
        .post('/api/review/file-compare')
        .set('Authorization', 'Bearer valid-token')
        .attach('primaryFile', fs.readFileSync('test/fixtures/contract1.pdf'), 'contract1.pdf')
        .attach('secondaryFile', fs.readFileSync('test/fixtures/contract2.pdf'), 'contract2.pdf');
      
      promises.push(promise);
    }
    
    const responses = await Promise.all(promises);
    
    responses.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });
  });
});
```

## 部署和运维

### 1. 环境配置

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - MAX_FILE_SIZE=52428800  # 50MB
      - COMPARISON_TIMEOUT=60000  # 60秒
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - redis
  
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
```

### 2. 监控和日志

```typescript
// 监控配置
export const monitoringConfig = {
  // 性能监控
  performance: {
    maxProcessingTime: 60000, // 60秒
    maxMemoryUsage: 512 * 1024 * 1024, // 512MB
    maxConcurrentRequests: 10
  },
  
  // 错误监控
  errorTracking: {
    enableErrorReporting: true,
    errorThreshold: 5, // 5分钟内超过5个错误触发告警
    timeWindow: 5 * 60 * 1000 // 5分钟
  },
  
  // 业务监控
  business: {
    trackComparisonSuccess: true,
    trackProcessingTime: true,
    trackFileTypes: true,
    trackUserActivity: true
  }
};

// 日志配置
export const loggingConfig = {
  level: process.env.LOG_LEVEL || 'info',
  format: 'json',
  transports: [
    {
      type: 'console',
      level: 'debug'
    },
    {
      type: 'file',
      filename: 'logs/file-compare.log',
      level: 'info',
      maxSize: '10m',
      maxFiles: 5
    },
    {
      type: 'file',
      filename: 'logs/error.log',
      level: 'error',
      maxSize: '10m',
      maxFiles: 5
    }
  ]
};
```

### 3. 安全配置

```typescript
// 安全配置
export const securityConfig = {
  // 文件上传安全
  fileUpload: {
    allowedMimeTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ],
    maxFileSize: 50 * 1024 * 1024, // 50MB
    virusScanEnabled: true,
    quarantineEnabled: true
  },
  
  // 访问控制
  accessControl: {
    requireAuthentication: true,
    rateLimiting: {
      windowMs: 15 * 60 * 1000, // 15分钟
      maxRequests: 10 // 每15分钟最多10次请求
    }
  },
  
  // 数据保护
  dataProtection: {
    encryptTempFiles: true,
    autoDeleteTempFiles: true,
    tempFileRetention: 1 * 60 * 60 * 1000, // 1小时
    logDataAccess: true
  }
};
```

## 总结

本设计文档详细描述了文件合同对比模块的技术实现方案，包括：

1. **完整的架构设计**：前后端分离，服务化架构
2. **详细的组件设计**：可复用的React组件和TypeScript接口
3. **先进的算法设计**：智能条款提取和差异分析算法
4. **完善的错误处理**：前后端统一的错误处理机制
5. **全面的测试策略**：单元测试、集成测试、E2E测试和性能测试
6. **生产级的部署方案**：容器化部署、监控和安全配置

该设计确保了：
- ✅ **功能独立性**：与现有Compare.tsx完全分离
- ✅ **基础设施复用**：最大化利用现有服务和组件
- ✅ **用户体验优先**：直观的界面和流畅的交互
- ✅ **性能优化**：支持大文件和并发处理
- ✅ **安全保障**：全面的安全防护措施
- ✅ **可维护性**：清晰的代码结构和完善的测试覆盖

该模块将为用户提供强大的文件对比功能，同时保持与现有系统的完美兼容。