# 合同对比模块设计文档

## 概述

合同对比模块是一个全新的文档比对系统，专门设计用于对比两个合同文档并可视化展示差异。该模块采用现代化的前后端分离架构，提供直观的用户界面和高效的文档处理能力。

### 设计目标
- 提供精确的文档差异检测
- 保持原始文档格式和布局
- 实现流畅的用户交互体验
- 确保系统性能和可扩展性
- 与现有系统无缝集成

## 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[合同对比页面] --> B[文件上传组件]
        A --> C[对比结果展示组件]
        A --> D[差异导航组件]
        A --> E[导出功能组件]
    end
    
    subgraph "API层"
        F[合同对比API] --> G[文件上传处理]
        F --> H[文档解析服务]
        F --> I[差异分析服务]
        F --> J[结果格式化服务]
    end
    
    subgraph "服务层"
        K[文档解析器] --> L[Word解析器]
        K --> M[PDF解析器]
        N[差异分析器] --> O[文本对比算法]
        N --> P[格式对比算法]
        Q[结果处理器] --> R[HTML生成器]
        Q --> S[差异标记器]
    end
    
    subgraph "存储层"
        T[临时文件存储]
        U[对比结果缓存]
    end
    
    A --> F
    G --> K
    H --> N
    I --> Q
    K --> T
    Q --> U
```

### 技术栈选择

**前端技术栈**:
- React 18 + TypeScript
- Ant Design 5.x (UI组件库)
- Tailwind CSS (样式框架)
- React Router DOM (路由管理)
- Zustand (状态管理)

**后端技术栈**:
- Express.js + TypeScript
- Multer (文件上传)
- mammoth.js (Word文档解析)
- pdf-parse (PDF文档解析)
- diff (文本差异算法)

**新增依赖**:
- react-window (虚拟滚动优化)
- html-differ (HTML差异检测)
- jsdiff (JavaScript差异算法库)

## 组件设计

### 1. 前端组件架构

#### 1.1 主页面组件 (ContractComparison.tsx)
```typescript
interface ContractComparisonProps {
  // 主页面组件属性
}

interface ContractComparisonState {
  uploadedFiles: {
    primary: File | null;
    secondary: File | null;
  };
  comparisonResult: ComparisonResult | null;
  isProcessing: boolean;
  currentStep: 'upload' | 'processing' | 'result';
  error: string | null;
}
```

#### 1.2 文件上传组件 (DocumentUploader.tsx)
```typescript
interface DocumentUploaderProps {
  onFileSelect: (file: File, type: 'primary' | 'secondary') => void;
  onStartComparison: () => void;
  primaryFile: File | null;
  secondaryFile: File | null;
  isProcessing: boolean;
}
```

#### 1.3 对比结果展示组件 (ComparisonViewer.tsx)
```typescript
interface ComparisonViewerProps {
  result: ComparisonResult;
  onDifferenceSelect: (difference: DifferenceItem) => void;
  selectedDifference: DifferenceItem | null;
}
```

#### 1.4 差异导航组件 (DifferenceNavigator.tsx)
```typescript
interface DifferenceNavigatorProps {
  differences: DifferenceItem[];
  selectedDifference: DifferenceItem | null;
  onDifferenceSelect: (difference: DifferenceItem) => void;
  onFilterChange: (filter: DifferenceFilter) => void;
}
```

### 2. 后端服务架构

#### 2.1 API路由设计
```typescript
// 新的合同对比API路由
POST /api/contract-comparison/upload
POST /api/contract-comparison/compare
GET  /api/contract-comparison/result/:sessionId
POST /api/contract-comparison/export
DELETE /api/contract-comparison/cleanup/:sessionId
```

#### 2.2 服务类设计

**文档解析服务 (DocumentParserService.ts)**
```typescript
class DocumentParserService {
  async parseDocument(file: Express.Multer.File): Promise<ParsedDocument>
  async parseWordDocument(buffer: Buffer): Promise<DocumentContent>
  async parsePdfDocument(buffer: Buffer): Promise<DocumentContent>
  private extractFormatting(html: string): FormattingInfo[]
  private extractStructure(content: string): DocumentStructure
}
```

**差异分析服务 (DifferenceAnalysisService.ts)**
```typescript
class DifferenceAnalysisService {
  async analyzeDifferences(doc1: ParsedDocument, doc2: ParsedDocument): Promise<ComparisonResult>
  private performTextDiff(text1: string, text2: string): DifferenceItem[]
  private performStructuralDiff(struct1: DocumentStructure, struct2: DocumentStructure): DifferenceItem[]
  private calculateSimilarity(doc1: ParsedDocument, doc2: ParsedDocument): number
  private categorizeChanges(differences: DifferenceItem[]): DifferenceStatistics
}
```

## 数据模型

### 1. 核心数据接口

```typescript
// 解析后的文档
interface ParsedDocument {
  id: string;
  originalName: string;
  content: DocumentContent;
  metadata: DocumentMetadata;
  parseTime: Date;
}

// 文档内容
interface DocumentContent {
  html: string;           // 保留格式的HTML内容
  plainText: string;      // 纯文本内容
  structure: DocumentStructure;
  formatting: FormattingInfo[];
}

// 文档结构
interface DocumentStructure {
  paragraphs: ParagraphInfo[];
  tables: TableInfo[];
  headers: HeaderInfo[];
  lists: ListInfo[];
}

// 对比结果
interface ComparisonResult {
  sessionId: string;
  primaryDocument: ParsedDocument;
  secondaryDocument: ParsedDocument;
  differences: DifferenceItem[];
  statistics: DifferenceStatistics;
  similarity: number;
  processTime: number;
  createdAt: Date;
}

// 差异项
interface DifferenceItem {
  id: string;
  type: 'added' | 'deleted' | 'modified' | 'moved';
  severity: 'high' | 'medium' | 'low';
  primaryPosition?: TextPosition;
  secondaryPosition?: TextPosition;
  primaryContent?: string;
  secondaryContent?: string;
  description: string;
  htmlContent?: {
    primary?: string;
    secondary?: string;
  };
}

// 文本位置
interface TextPosition {
  startIndex: number;
  endIndex: number;
  lineNumber: number;
  columnNumber: number;
  paragraphIndex: number;
}

// 差异统计
interface DifferenceStatistics {
  totalDifferences: number;
  addedCount: number;
  deletedCount: number;
  modifiedCount: number;
  movedCount: number;
  affectedParagraphs: number;
  similarityScore: number;
}
```

### 2. 状态管理

使用Zustand进行状态管理：

```typescript
interface ContractComparisonStore {
  // 状态
  uploadedFiles: {
    primary: File | null;
    secondary: File | null;
  };
  comparisonResult: ComparisonResult | null;
  selectedDifference: DifferenceItem | null;
  isProcessing: boolean;
  error: string | null;
  
  // 操作
  setFile: (file: File, type: 'primary' | 'secondary') => void;
  setComparisonResult: (result: ComparisonResult) => void;
  selectDifference: (difference: DifferenceItem | null) => void;
  setProcessing: (processing: boolean) => void;
  setError: (error: string | null) => void;
  reset: () => void;
}
```

## 核心算法设计

### 1. 文档解析算法

```typescript
class DocumentParser {
  async parseDocument(file: File): Promise<ParsedDocument> {
    // 1. 检测文件类型
    const fileType = this.detectFileType(file);
    
    // 2. 根据类型选择解析器
    let content: DocumentContent;
    switch (fileType) {
      case 'docx':
        content = await this.parseWordDocument(file);
        break;
      case 'pdf':
        content = await this.parsePdfDocument(file);
        break;
      default:
        throw new Error('Unsupported file type');
    }
    
    // 3. 提取文档结构
    const structure = this.extractStructure(content.html);
    
    // 4. 生成纯文本版本
    const plainText = this.htmlToPlainText(content.html);
    
    return {
      id: generateId(),
      originalName: file.name,
      content: { ...content, structure, plainText },
      metadata: this.extractMetadata(file),
      parseTime: new Date()
    };
  }
}
```

### 2. 差异分析算法

```typescript
class DifferenceAnalyzer {
  analyzeDifferences(doc1: ParsedDocument, doc2: ParsedDocument): DifferenceItem[] {
    const differences: DifferenceItem[] = [];
    
    // 1. 段落级别对比
    const paragraphDiffs = this.compareParagraphs(
      doc1.content.structure.paragraphs,
      doc2.content.structure.paragraphs
    );
    differences.push(...paragraphDiffs);
    
    // 2. 文本级别对比
    const textDiffs = this.compareText(
      doc1.content.plainText,
      doc2.content.plainText
    );
    differences.push(...textDiffs);
    
    // 3. 格式级别对比
    const formatDiffs = this.compareFormatting(
      doc1.content.formatting,
      doc2.content.formatting
    );
    differences.push(...formatDiffs);
    
    // 4. 合并和去重
    return this.mergeDifferences(differences);
  }
  
  private compareText(text1: string, text2: string): DifferenceItem[] {
    // 使用Myers算法进行文本对比
    const diff = Diff.diffWords(text1, text2);
    return this.convertDiffToItems(diff);
  }
}
```

## 用户界面设计

### 1. 页面布局

```typescript
// 主页面布局结构
const ContractComparison: React.FC = () => {
  return (
    <div className="contract-comparison-container">
      {/* 头部导航 */}
      <header className="comparison-header">
        <Breadcrumb />
        <ActionButtons />
      </header>
      
      {/* 主要内容区域 */}
      <main className="comparison-main">
        {currentStep === 'upload' && <DocumentUploader />}
        {currentStep === 'processing' && <ProcessingIndicator />}
        {currentStep === 'result' && (
          <div className="comparison-result-layout">
            {/* 左侧差异导航 */}
            <aside className="difference-navigator">
              <DifferenceNavigator />
            </aside>
            
            {/* 中间对比视图 */}
            <section className="comparison-viewer">
              <ComparisonViewer />
            </section>
            
            {/* 右侧工具栏 */}
            <aside className="comparison-tools">
              <ExportTools />
              <ViewOptions />
            </aside>
          </div>
        )}
      </main>
    </div>
  );
};
```

### 2. 样式设计

基于参考图片的视觉设计：

```css
/* 主要颜色方案 */
:root {
  --color-added: #d4edda;      /* 新增内容 - 绿色 */
  --color-deleted: #f8d7da;    /* 删除内容 - 红色 */
  --color-modified: #fff3cd;   /* 修改内容 - 黄色 */
  --color-border: #e9ecef;     /* 边框颜色 */
  --color-text: #333333;       /* 主要文本 */
  --color-secondary: #6c757d;  /* 次要文本 */
}

/* 对比视图布局 */
.comparison-viewer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1px;
  background-color: var(--color-border);
  height: 100%;
}

/* 文档面板 */
.document-panel {
  background: white;
  padding: 20px;
  overflow-y: auto;
  position: relative;
}

/* 差异高亮样式 */
.diff-added {
  background-color: var(--color-added);
  border-left: 3px solid #28a745;
}

.diff-deleted {
  background-color: var(--color-deleted);
  border-left: 3px solid #dc3545;
}

.diff-modified {
  background-color: var(--color-modified);
  border-left: 3px solid #ffc107;
}
```

## 错误处理

### 1. 错误类型定义

```typescript
enum ContractComparisonErrorType {
  FILE_UPLOAD_ERROR = 'FILE_UPLOAD_ERROR',
  PARSE_ERROR = 'PARSE_ERROR',
  COMPARISON_ERROR = 'COMPARISON_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

interface ContractComparisonError {
  type: ContractComparisonErrorType;
  message: string;
  details?: string;
  code?: string;
  timestamp: Date;
}
```

### 2. 错误处理策略

```typescript
class ErrorHandler {
  handleError(error: ContractComparisonError): void {
    // 1. 记录错误日志
    this.logError(error);
    
    // 2. 显示用户友好的错误信息
    this.showUserError(error);
    
    // 3. 清理相关资源
    this.cleanup(error);
    
    // 4. 提供恢复选项
    this.offerRecovery(error);
  }
  
  private showUserError(error: ContractComparisonError): void {
    const userMessage = this.getUserFriendlyMessage(error);
    notification.error({
      message: '操作失败',
      description: userMessage,
      duration: 5
    });
  }
}
```

## 测试策略

### 1. 单元测试

```typescript
// 文档解析器测试
describe('DocumentParser', () => {
  test('should parse Word document correctly', async () => {
    const parser = new DocumentParser();
    const mockFile = createMockWordFile();
    const result = await parser.parseDocument(mockFile);
    
    expect(result.content.html).toBeDefined();
    expect(result.content.plainText).toBeDefined();
    expect(result.content.structure).toBeDefined();
  });
});

// 差异分析器测试
describe('DifferenceAnalyzer', () => {
  test('should detect text additions correctly', () => {
    const analyzer = new DifferenceAnalyzer();
    const doc1 = createMockDocument('Hello world');
    const doc2 = createMockDocument('Hello beautiful world');
    
    const differences = analyzer.analyzeDifferences(doc1, doc2);
    
    expect(differences).toHaveLength(1);
    expect(differences[0].type).toBe('added');
    expect(differences[0].secondaryContent).toContain('beautiful');
  });
});
```

### 2. 集成测试

```typescript
// API集成测试
describe('Contract Comparison API', () => {
  test('should handle complete comparison workflow', async () => {
    // 1. 上传文件
    const uploadResponse = await request(app)
      .post('/api/contract-comparison/upload')
      .attach('primaryFile', 'test/fixtures/contract1.docx')
      .attach('secondaryFile', 'test/fixtures/contract2.docx');
    
    expect(uploadResponse.status).toBe(200);
    
    // 2. 执行对比
    const compareResponse = await request(app)
      .post('/api/contract-comparison/compare')
      .send({ sessionId: uploadResponse.body.sessionId });
    
    expect(compareResponse.status).toBe(200);
    expect(compareResponse.body.result.differences).toBeDefined();
  });
});
```

### 3. 端到端测试

```typescript
// E2E测试使用Cypress
describe('Contract Comparison E2E', () => {
  it('should complete full comparison workflow', () => {
    cy.visit('/contract-comparison');
    
    // 上传文件
    cy.get('[data-testid="primary-file-upload"]')
      .selectFile('cypress/fixtures/contract1.docx');
    cy.get('[data-testid="secondary-file-upload"]')
      .selectFile('cypress/fixtures/contract2.docx');
    
    // 开始对比
    cy.get('[data-testid="start-comparison"]').click();
    
    // 验证结果
    cy.get('[data-testid="comparison-result"]').should('be.visible');
    cy.get('[data-testid="difference-item"]').should('have.length.greaterThan', 0);
  });
});
```

## 性能优化

### 1. 前端性能优化

```typescript
// 虚拟滚动实现
const VirtualizedDocumentViewer: React.FC<Props> = ({ content, differences }) => {
  const { height, width } = useWindowSize();
  
  return (
    <FixedSizeList
      height={height - 200}
      width={width / 2}
      itemCount={content.paragraphs.length}
      itemSize={50}
      itemData={{ content, differences }}
    >
      {DocumentParagraph}
    </FixedSizeList>
  );
};

// 差异高亮优化
const useDifferenceHighlight = (content: string, differences: DifferenceItem[]) => {
  return useMemo(() => {
    return highlightDifferences(content, differences);
  }, [content, differences]);
};
```

### 2. 后端性能优化

```typescript
// 分块处理大文档
class DocumentProcessor {
  async processLargeDocument(file: File): Promise<ParsedDocument> {
    const chunks = this.splitIntoChunks(file);
    const processedChunks = await Promise.all(
      chunks.map(chunk => this.processChunk(chunk))
    );
    return this.mergeChunks(processedChunks);
  }
  
  private splitIntoChunks(file: File, chunkSize = 1024 * 1024): Buffer[] {
    // 将大文件分割成小块处理
  }
}

// 缓存机制
class ComparisonCache {
  private cache = new Map<string, ComparisonResult>();
  
  async getOrCompute(key: string, computeFn: () => Promise<ComparisonResult>): Promise<ComparisonResult> {
    if (this.cache.has(key)) {
      return this.cache.get(key)!;
    }
    
    const result = await computeFn();
    this.cache.set(key, result);
    return result;
  }
}
```

## 部署和配置

### 1. 环境配置

```typescript
// 配置文件
interface ContractComparisonConfig {
  maxFileSize: number;
  supportedFormats: string[];
  cacheTimeout: number;
  processingTimeout: number;
  concurrentLimit: number;
}

const config: ContractComparisonConfig = {
  maxFileSize: 50 * 1024 * 1024, // 50MB
  supportedFormats: ['.docx', '.doc', '.pdf'],
  cacheTimeout: 3600000, // 1小时
  processingTimeout: 300000, // 5分钟
  concurrentLimit: 10
};
```

### 2. 路由集成

```typescript
// 在App.tsx中添加新路由
<Route path="/contract-comparison" element={
  <ProtectedRoute>
    <Layout>
      <ContractComparison />
    </Layout>
  </ProtectedRoute>
} />
```

这个设计文档提供了合同对比模块的完整技术架构和实现方案。设计考虑了性能、可维护性、用户体验和系统集成等各个方面。

**设计文档是否符合您的期望？如果满意，我们可以继续进入实现计划阶段。**
