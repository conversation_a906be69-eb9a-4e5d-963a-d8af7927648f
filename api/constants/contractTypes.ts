/**
 * 合同类型常量定义
 * 统一管理前后端合同类型配置
 */

export interface ContractTypeConfig {
  value: string;
  label: string;
  description?: string;
}

// 合同类型配置数组
export const CONTRACT_TYPES: ContractTypeConfig[] = [
  { value: 'service', label: '服务合同', description: '提供服务的合同' },
  { value: 'purchase', label: '采购合同', description: '采购商品或服务的合同' },
  { value: 'sales', label: '销售合同', description: '销售商品的合同' },
  { value: 'lease', label: '租赁合同', description: '租赁资产的合同' },
  { value: 'employment', label: '劳动合同', description: '雇佣关系合同' },
  { value: 'general', label: '通用合同', description: '其他类型合同' }
];

// 合同类型值数组（用于验证）
export const CONTRACT_TYPE_VALUES = CONTRACT_TYPES.map(type => type.value);

// 合同类型标签数组
export const CONTRACT_TYPE_LABELS = CONTRACT_TYPES.map(type => type.label);

// 获取合同类型标签
export const getContractTypeLabel = (value: string): string => {
  const type = CONTRACT_TYPES.find(t => t.value === value);
  return type ? type.label : value;
};

// 验证合同类型是否有效
export const isValidContractType = (value: string): boolean => {
  return CONTRACT_TYPE_VALUES.includes(value);
};

// 获取合同类型选项（用于前端）
export const getContractTypeOptions = () => {
  return CONTRACT_TYPES.map(type => ({
    value: type.value,
    label: type.label
  }));
};