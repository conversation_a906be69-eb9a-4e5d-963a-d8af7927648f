/**
 * This is a API server
 */

import express, { type Request, type Response, type NextFunction }  from 'express';
import cors from 'cors';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import authRoutes from './routes/auth.js';
import contractRoutes from './routes/contracts.js';
import reviewRoutes from './routes/review.js';
import fileRoutes from './routes/files.js';
import contractComparisonRoutes from './routes/contractComparison.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';

// for esm mode
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// load env
dotenv.config();


const app: express.Application = express();

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 添加文件名编码处理中间件
app.use((req: Request, res: Response, next: NextFunction) => {
  // 设置正确的字符编码
  if (req.headers['content-type']?.includes('multipart/form-data')) {
    console.log('🔧 [编码处理] 检测到文件上传请求，设置UTF-8编码');
  }
  next();
});

/**
 * API Routes
 */
app.use('/api/auth', authRoutes);
app.use('/api/contracts', contractRoutes);
app.use('/api/review', reviewRoutes);
app.use('/api/files', fileRoutes);
app.use('/api/contract-comparison', contractComparisonRoutes);

/**
 * health
 */
app.use('/api/health', (req: Request, res: Response, next: NextFunction): void => {
  res.status(200).json({
    success: true,
    message: 'ok'
  });
});

/**
 * 404 handler
 */
app.use(notFoundHandler);

/**
 * Global error handler middleware
 */
app.use(errorHandler);

export default app;