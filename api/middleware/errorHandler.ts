/**
 * 统一错误处理中间件
 * 提供标准化的错误响应和日志记录
 */
import { Request, Response, NextFunction } from 'express';
import { ValidationError } from 'express-validator';

// 自定义错误类型
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    code?: string,
    details?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 业务错误类型
export class BusinessError extends AppError {
  constructor(message: string, code?: string, details?: any) {
    super(message, 400, true, code, details);
  }
}

// 验证错误类型
export class ValidationAppError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, 'VALIDATION_ERROR', details);
  }
}

// 权限错误类型
export class AuthorizationError extends AppError {
  constructor(message: string = '没有权限执行此操作') {
    super(message, 403, true, 'AUTHORIZATION_ERROR');
  }
}

// 认证错误类型
export class AuthenticationError extends AppError {
  constructor(message: string = '用户未认证') {
    super(message, 401, true, 'AUTHENTICATION_ERROR');
  }
}

// 资源不存在错误类型
export class NotFoundError extends AppError {
  constructor(resource: string = '资源') {
    super(`${resource}不存在`, 404, true, 'NOT_FOUND_ERROR');
  }
}

// 冲突错误类型
export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, true, 'CONFLICT_ERROR');
  }
}

// 数据库错误类型
export class DatabaseError extends AppError {
  constructor(message: string, originalError?: any) {
    super(message, 500, true, 'DATABASE_ERROR', originalError);
  }
}

// 文件处理错误类型
export class FileError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, true, 'FILE_ERROR', details);
  }
}

// 外部服务错误类型
export class ExternalServiceError extends AppError {
  constructor(service: string, message: string, originalError?: any) {
    super(`${service}服务错误: ${message}`, 502, true, 'EXTERNAL_SERVICE_ERROR', originalError);
  }
}

// 错误响应接口
interface ErrorResponse {
  success: false;
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
  path: string;
  requestId?: string;
  stack?: string;
}

// 错误日志记录
class ErrorLogger {
  static log(error: Error, req: Request, additionalInfo?: any) {
    const logData = {
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.userId,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        ...(error instanceof AppError && {
          statusCode: error.statusCode,
          code: error.code,
          details: error.details
        })
      },
      ...additionalInfo
    };

    if (error instanceof AppError && error.statusCode < 500) {
      // 客户端错误，记录为警告
      console.warn('Client Error:', JSON.stringify(logData, null, 2));
    } else {
      // 服务器错误，记录为错误
      console.error('Server Error:', JSON.stringify(logData, null, 2));
    }
  }
}

// 错误处理中间件
export const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  // 生成请求ID用于追踪
  const requestId = req.headers['x-request-id'] as string || 
                   `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // 记录错误日志
  ErrorLogger.log(error, req, { requestId });

  // 构建错误响应
  const errorResponse: ErrorResponse = {
    success: false,
    message: '服务器内部错误',
    timestamp: new Date().toISOString(),
    path: req.path,
    requestId
  };

  let statusCode = 500;

  // 处理不同类型的错误
  if (error instanceof AppError) {
    // 自定义应用错误
    statusCode = error.statusCode;
    errorResponse.message = error.message;
    errorResponse.code = error.code;
    errorResponse.details = error.details;
  } else if (error.name === 'ValidationError') {
    // Mongoose验证错误
    statusCode = 400;
    errorResponse.message = '数据验证失败';
    errorResponse.code = 'VALIDATION_ERROR';
    errorResponse.details = error.message;
  } else if (error.name === 'CastError') {
    // Mongoose类型转换错误
    statusCode = 400;
    errorResponse.message = '无效的数据格式';
    errorResponse.code = 'CAST_ERROR';
  } else if (error.name === 'JsonWebTokenError') {
    // JWT错误
    statusCode = 401;
    errorResponse.message = '无效的访问令牌';
    errorResponse.code = 'INVALID_TOKEN';
  } else if (error.name === 'TokenExpiredError') {
    // JWT过期错误
    statusCode = 401;
    errorResponse.message = '访问令牌已过期';
    errorResponse.code = 'TOKEN_EXPIRED';
  } else if (error.message?.includes('duplicate key')) {
    // 数据库唯一约束错误
    statusCode = 409;
    errorResponse.message = '数据已存在';
    errorResponse.code = 'DUPLICATE_ERROR';
  } else if (error.message?.includes('foreign key constraint')) {
    // 外键约束错误
    statusCode = 400;
    errorResponse.message = '数据关联错误';
    errorResponse.code = 'FOREIGN_KEY_ERROR';
  } else if (error.name === 'MulterError') {
    // 文件上传错误
    statusCode = 400;
    errorResponse.message = '文件上传失败';
    errorResponse.code = 'FILE_UPLOAD_ERROR';
    errorResponse.details = error.message;
  } else if (error.message?.includes('ENOENT')) {
    // 文件不存在错误
    statusCode = 404;
    errorResponse.message = '文件不存在';
    errorResponse.code = 'FILE_NOT_FOUND';
  } else if (error.message?.includes('EACCES')) {
    // 文件权限错误
    statusCode = 403;
    errorResponse.message = '文件访问权限不足';
    errorResponse.code = 'FILE_PERMISSION_ERROR';
  }

  // 在开发环境中包含堆栈信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = error.stack;
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
};

// 404错误处理中间件
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new NotFoundError(`路由 ${req.originalUrl}`);
  next(error);
};

// 异步错误捕获包装器
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 错误工厂函数
export const createError = {
  business: (message: string, code?: string, details?: any) => 
    new BusinessError(message, code, details),
  
  validation: (message: string, details?: any) => 
    new ValidationAppError(message, details),
  
  authorization: (message?: string) => 
    new AuthorizationError(message),
  
  authentication: (message?: string) => 
    new AuthenticationError(message),
  
  notFound: (resource?: string) => 
    new NotFoundError(resource),
  
  conflict: (message: string) => 
    new ConflictError(message),
  
  database: (message: string, originalError?: any) => 
    new DatabaseError(message, originalError),
  
  file: (message: string, details?: any) => 
    new FileError(message, details),
  
  externalService: (service: string, message: string, originalError?: any) => 
    new ExternalServiceError(service, message, originalError)
};

// 错误处理工具函数
export const errorUtils = {
  // 检查是否为操作性错误
  isOperationalError: (error: Error): boolean => {
    if (error instanceof AppError) {
      return error.isOperational;
    }
    return false;
  },

  // 格式化验证错误
  formatValidationErrors: (errors: ValidationError[]): any => {
    return errors.map(error => ({
      field: error.type === 'field' ? error.path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? error.value : undefined
    }));
  },

  // 安全地提取错误信息
  extractErrorMessage: (error: any): string => {
    if (typeof error === 'string') {
      return error;
    }
    if (error instanceof Error) {
      return error.message;
    }
    if (error && typeof error.message === 'string') {
      return error.message;
    }
    return '未知错误';
  },

  // 检查是否为重试错误
  isRetryableError: (error: Error): boolean => {
    if (error instanceof AppError) {
      return error.statusCode >= 500 && error.statusCode < 600;
    }
    return false;
  }
};

// 进程级错误处理
export const setupGlobalErrorHandlers = () => {
  // 处理未捕获的异常
  process.on('uncaughtException', (error: Error) => {
    console.error('Uncaught Exception:', error);
    // 在生产环境中，应该优雅地关闭服务器
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  });

  // 处理未处理的Promise拒绝
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    // 在生产环境中，应该优雅地关闭服务器
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  });
};