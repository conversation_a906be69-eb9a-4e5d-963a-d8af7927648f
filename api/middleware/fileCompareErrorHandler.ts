/**
 * 文件对比专用错误处理中间件
 * 扩展通用错误处理，提供文件对比特定的错误处理逻辑
 */
import { Request, Response, NextFunction } from 'express';
import { AppError, FileError, createError } from './errorHandler';
import { 
  FileCompareErrorType, 
  FileValidationErrorCode, 
  FileValidationWarningCode,
  FileCompareError 
} from '../../src/types/fileCompare';

// ==================== 文件对比专用错误类 ====================

/**
 * 文件对比错误基类
 */
export class FileCompareAppError extends AppError {
  public fileCompareType: FileCompareErrorType;
  public fileName?: string;
  public suggestions?: string[];

  constructor(
    message: string,
    fileCompareType: FileCompareErrorType,
    statusCode: number = 400,
    fileName?: string,
    suggestions?: string[]
  ) {
    super(message, statusCode, true, fileCompareType, { fileName, suggestions });
    this.fileCompareType = fileCompareType;
    this.fileName = fileName;
    this.suggestions = suggestions;
  }
}

/**
 * 文件格式错误
 */
export class InvalidFileFormatError extends FileCompareAppError {
  constructor(fileName: string, supportedFormats: string[]) {
    const message = `文件 "${fileName}" 格式不支持`;
    const suggestions = [
      `支持的格式：${supportedFormats.join(', ')}`,
      '请转换文件格式后重新上传',
      '确保文件扩展名正确'
    ];
    super(message, FileCompareErrorType.INVALID_FILE_FORMAT, 400, fileName, suggestions);
  }
}

/**
 * 文件过大错误
 */
export class FileTooLargeError extends FileCompareAppError {
  constructor(fileName: string, fileSize: number, maxSize: number) {
    const message = `文件 "${fileName}" 大小超出限制`;
    const suggestions = [
      `当前大小：${(fileSize / 1024 / 1024).toFixed(2)}MB`,
      `最大允许：${(maxSize / 1024 / 1024).toFixed(2)}MB`,
      '请压缩文件或分割成较小的文件',
      '移除不必要的图片和附件'
    ];
    super(message, FileCompareErrorType.FILE_TOO_LARGE, 413, fileName, suggestions);
  }
}

/**
 * 文件解析错误
 */
export class FileParseError extends FileCompareAppError {
  constructor(fileName: string, parseError: string) {
    const message = `文件 "${fileName}" 解析失败`;
    const suggestions = [
      '检查文件是否损坏',
      '确保文件未被密码保护',
      '尝试重新保存文件',
      '检查文件编码格式'
    ];
    super(message, FileCompareErrorType.PARSE_ERROR, 422, fileName, suggestions);
    this.details = { parseError };
  }
}

/**
 * 文件对比失败错误
 */
export class ComparisonFailedError extends FileCompareAppError {
  constructor(reason: string, sessionId?: string) {
    const message = `文件对比处理失败：${reason}`;
    const suggestions = [
      '检查文件内容是否有效',
      '确保两个文件都包含可对比的文本',
      '尝试重新上传文件',
      '联系技术支持获取帮助'
    ];
    super(message, FileCompareErrorType.COMPARISON_FAILED, 500, undefined, suggestions);
    this.details = { reason, sessionId };
  }
}

/**
 * 文件验证错误
 */
export class FileValidationError extends FileCompareAppError {
  public validationCode: FileValidationErrorCode;

  constructor(
    fileName: string, 
    validationCode: FileValidationErrorCode, 
    details?: string
  ) {
    const message = FileValidationError.getErrorMessage(validationCode, fileName);
    const suggestions = FileValidationError.getSuggestions(validationCode);
    super(message, FileCompareErrorType.INVALID_FILE_FORMAT, 400, fileName, suggestions);
    this.validationCode = validationCode;
    this.details = details;
  }

  private static getErrorMessage(code: FileValidationErrorCode, fileName: string): string {
    const messages = {
      [FileValidationErrorCode.INVALID_FORMAT]: `文件 "${fileName}" 格式无效`,
      [FileValidationErrorCode.FILE_TOO_LARGE]: `文件 "${fileName}" 大小超出限制`,
      [FileValidationErrorCode.FILE_EMPTY]: `文件 "${fileName}" 为空`,
      [FileValidationErrorCode.UNSUPPORTED_TYPE]: `文件 "${fileName}" 类型不支持`,
      [FileValidationErrorCode.CORRUPTED_FILE]: `文件 "${fileName}" 已损坏`,
      [FileValidationErrorCode.PASSWORD_PROTECTED]: `文件 "${fileName}" 受密码保护`,
      [FileValidationErrorCode.INVALID_ENCODING]: `文件 "${fileName}" 编码格式无效`
    };
    return messages[code] || `文件 "${fileName}" 验证失败`;
  }

  private static getSuggestions(code: FileValidationErrorCode): string[] {
    const suggestions = {
      [FileValidationErrorCode.INVALID_FORMAT]: [
        '检查文件扩展名是否正确',
        '确保文件未被重命名',
        '使用原始文件格式'
      ],
      [FileValidationErrorCode.FILE_TOO_LARGE]: [
        '压缩文件大小',
        '移除不必要的内容',
        '分割成多个较小的文件'
      ],
      [FileValidationErrorCode.FILE_EMPTY]: [
        '检查文件是否包含内容',
        '确保文件正确保存',
        '重新创建文件'
      ],
      [FileValidationErrorCode.UNSUPPORTED_TYPE]: [
        '转换为支持的文件格式',
        '查看支持的文件类型列表',
        '联系管理员添加新格式支持'
      ],
      [FileValidationErrorCode.CORRUPTED_FILE]: [
        '重新下载或获取文件',
        '检查文件传输过程',
        '使用文件修复工具'
      ],
      [FileValidationErrorCode.PASSWORD_PROTECTED]: [
        '移除文件密码保护',
        '提供解密后的文件',
        '联系文件提供者'
      ],
      [FileValidationErrorCode.INVALID_ENCODING]: [
        '检查文件编码格式',
        '转换为UTF-8编码',
        '重新保存文件'
      ]
    };
    return suggestions[code] || ['请检查文件并重试'];
  }
}

/**
 * 权限错误
 */
export class FileComparePermissionError extends FileCompareAppError {
  constructor(operation: string) {
    const message = `没有权限执行文件对比操作：${operation}`;
    const suggestions = [
      '检查用户权限设置',
      '联系管理员获取权限',
      '确保已正确登录'
    ];
    super(message, FileCompareErrorType.PERMISSION_DENIED, 403, undefined, suggestions);
  }
}

/**
 * 超时错误
 */
export class FileCompareTimeoutError extends FileCompareAppError {
  constructor(operation: string, timeout: number) {
    const message = `文件对比操作超时：${operation}`;
    const suggestions = [
      `操作超时时间：${timeout}秒`,
      '尝试上传较小的文件',
      '检查网络连接',
      '稍后重试'
    ];
    super(message, FileCompareErrorType.TIMEOUT, 408, undefined, suggestions);
  }
}

// ==================== 错误处理工具函数 ====================

/**
 * 文件对比错误工厂
 */
export const fileCompareErrorFactory = {
  invalidFormat: (fileName: string, supportedFormats: string[]) => 
    new InvalidFileFormatError(fileName, supportedFormats),
  
  fileTooLarge: (fileName: string, fileSize: number, maxSize: number) => 
    new FileTooLargeError(fileName, fileSize, maxSize),
  
  parseError: (fileName: string, parseError: string) => 
    new FileParseError(fileName, parseError),
  
  comparisonFailed: (reason: string, sessionId?: string) => 
    new ComparisonFailedError(reason, sessionId),
  
  validation: (fileName: string, code: FileValidationErrorCode, details?: string) => 
    new FileValidationError(fileName, code, details),
  
  permission: (operation: string) => 
    new FileComparePermissionError(operation),
  
  timeout: (operation: string, timeout: number) => 
    new FileCompareTimeoutError(operation, timeout)
};

/**
 * 错误响应格式化器
 */
export class FileCompareErrorFormatter {
  /**
   * 格式化文件对比错误响应
   */
  static formatErrorResponse(error: Error, req: Request): any {
    const requestId = req.headers['x-request-id'] as string || 
                     `fc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    const baseResponse = {
      success: false,
      timestamp: new Date().toISOString(),
      path: req.path,
      requestId,
      module: 'file-compare'
    };

    if (error instanceof FileCompareAppError) {
      return {
        ...baseResponse,
        message: error.message,
        code: error.fileCompareType,
        details: {
          fileName: error.fileName,
          suggestions: error.suggestions,
          ...error.details
        }
      };
    }

    if (error instanceof FileError) {
      return {
        ...baseResponse,
        message: error.message,
        code: 'FILE_ERROR',
        details: error.details
      };
    }

    // 处理Multer错误
    if (error.name === 'MulterError') {
      const multerError = error as any;
      let message = '文件上传失败';
      let suggestions: string[] = [];

      switch (multerError.code) {
        case 'LIMIT_FILE_SIZE':
          message = '文件大小超出限制';
          suggestions = ['请上传较小的文件', '压缩文件后重试'];
          break;
        case 'LIMIT_FILE_COUNT':
          message = '文件数量超出限制';
          suggestions = ['最多只能上传2个文件', '请重新选择文件'];
          break;
        case 'LIMIT_UNEXPECTED_FILE':
          message = '意外的文件字段';
          suggestions = ['检查文件上传字段名', '确保使用正确的表单'];
          break;
        default:
          suggestions = ['检查文件格式', '重新选择文件'];
      }

      return {
        ...baseResponse,
        message,
        code: 'FILE_UPLOAD_ERROR',
        details: {
          multerCode: multerError.code,
          suggestions
        }
      };
    }

    // 默认错误处理
    return {
      ...baseResponse,
      message: '文件对比服务暂时不可用',
      code: 'SERVER_ERROR',
      details: {
        suggestions: [
          '请稍后重试',
          '检查文件格式是否正确',
          '联系技术支持'
        ]
      }
    };
  }

  /**
   * 格式化警告信息
   */
  static formatWarnings(warnings: Array<{
    code: FileValidationWarningCode;
    message: string;
    fileName?: string;
  }>): any[] {
    return warnings.map(warning => ({
      type: 'warning',
      code: warning.code,
      message: warning.message,
      fileName: warning.fileName,
      suggestions: this.getWarningSuggestions(warning.code)
    }));
  }

  private static getWarningSuggestions(code: FileValidationWarningCode): string[] {
    const suggestions = {
      [FileValidationWarningCode.LARGE_FILE_SIZE]: [
        '文件较大，处理时间可能较长',
        '建议压缩文件以提高处理速度'
      ],
      [FileValidationWarningCode.COMPLEX_FORMATTING]: [
        '文件包含复杂格式，可能影响对比准确性',
        '建议使用纯文本版本进行对比'
      ],
      [FileValidationWarningCode.MULTIPLE_LANGUAGES]: [
        '检测到多种语言，可能影响分析结果',
        '建议分别处理不同语言的内容'
      ],
      [FileValidationWarningCode.LOW_TEXT_CONTENT]: [
        '文件文本内容较少',
        '确保文件包含足够的可对比内容'
      ],
      [FileValidationWarningCode.SCANNED_DOCUMENT]: [
        '检测到扫描文档，OCR识别可能不够准确',
        '建议使用原始电子文档'
      ]
    };
    return suggestions[code] || [];
  }
}

// ==================== 错误处理中间件 ====================

/**
 * 文件对比错误处理中间件
 */
export const fileCompareErrorHandler = (
  error: Error, 
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  // 记录错误日志
  console.error('FileCompare Error:', {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.userId,
    error: {
      name: error.name,
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      ...(error instanceof FileCompareAppError && {
        fileCompareType: error.fileCompareType,
        fileName: error.fileName,
        suggestions: error.suggestions
      })
    }
  });

  // 格式化错误响应
  const errorResponse = FileCompareErrorFormatter.formatErrorResponse(error, req);

  // 确定状态码
  let statusCode = 500;
  if (error instanceof FileCompareAppError) {
    statusCode = error.statusCode;
  } else if (error instanceof AppError) {
    statusCode = error.statusCode;
  } else if (error.name === 'MulterError') {
    statusCode = 400;
  }

  // 发送错误响应
  res.status(statusCode).json(errorResponse);
};

/**
 * 文件对比请求验证中间件
 */
export const validateFileCompareRequest = (
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  try {
    // 检查是否为文件对比相关路由
    if (!req.path.includes('/file-compare')) {
      return next();
    }

    // 检查用户权限
    if (!req.user) {
      throw new FileComparePermissionError('用户未认证');
    }

    // 检查请求方法
    if (req.method === 'POST') {
      // 检查文件上传
      if (!req.files || Object.keys(req.files).length === 0) {
        throw fileCompareErrorFactory.validation(
          '未知文件', 
          FileValidationErrorCode.FILE_EMPTY,
          '请选择要对比的文件'
        );
      }

      // 检查文件数量
      const files = req.files as any;
      const fileCount = Object.keys(files).length;
      if (fileCount !== 2) {
        throw new FileCompareAppError(
          `需要上传2个文件进行对比，当前上传了${fileCount}个文件`,
          FileCompareErrorType.INVALID_FILE_FORMAT,
          400,
          undefined,
          ['请选择两个文件进行对比', '确保文件字段名正确']
        );
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * 异步文件对比错误捕获包装器
 */
export const asyncFileCompareHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch((error: Error) => {
      // 如果是已知的文件对比错误，直接传递
      if (error instanceof FileCompareAppError) {
        return next(error);
      }

      // 转换为文件对比错误
      const fileCompareError = new ComparisonFailedError(
        error.message || '未知错误',
        req.headers['x-request-id'] as string
      );
      next(fileCompareError);
    });
  };
};

// ==================== 导出 ====================

export {
  FileCompareErrorType,
  FileValidationErrorCode,
  FileValidationWarningCode
} from '../../src/types/fileCompare';