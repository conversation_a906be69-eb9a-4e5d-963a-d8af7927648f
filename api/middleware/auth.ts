/**
 * Authentication Middleware
 * 认证中间件，用于保护需要登录的API路由
 */
import { Request, Response, NextFunction } from 'express';
import { authService, TokenPayload } from '../services/authService.js';

// 扩展Request接口，添加用户信息
declare global {
  namespace Express {
    interface Request {
      user?: TokenPayload;
    }
  }
}

/**
 * 认证中间件 - 验证JWT token
 */
export const authenticateToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        success: false,
        message: '访问令牌缺失'
      });
      return;
    }

    const payload = await authService.verifyToken(token);
    if (!payload) {
      res.status(401).json({
        success: false,
        message: '访问令牌无效或已过期'
      });
      return;
    }

    // 将用户信息添加到请求对象
    req.user = payload;
    next();
  } catch (error) {
    console.error('Authentication middleware error:', error);
    res.status(500).json({
      success: false,
      message: '认证服务错误'
    });
  }
};

/**
 * 角色权限中间件 - 检查用户角色权限
 */
export const requireRole = (requiredRole: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: '用户未认证'
        });
        return;
      }

      const hasPermission = authService.hasPermission(req.user.role, requiredRole);
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: '权限不足'
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Role authorization middleware error:', error);
      res.status(500).json({
        success: false,
        message: '权限验证错误'
      });
    }
  };
};

/**
 * 可选认证中间件 - 如果有token则验证，没有则跳过
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const payload = await authService.verifyToken(token);
      if (payload) {
        req.user = payload;
      }
    }

    next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    // 可选认证出错时不阻止请求继续
    next();
  }
};

/**
 * 管理员权限中间件
 */
export const requireAdmin = requireRole('admin');

/**
 * 法务管理权限中间件
 */
export const requireLegalManager = requireRole('legal_manager');

/**
 * 法务人员权限中间件
 */
export const requireLegalStaff = requireRole('legal_staff');

/**
 * 用户自己或管理员权限中间件
 */
export const requireSelfOrAdmin = (userIdParam: string = 'userId') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: '用户未认证'
        });
        return;
      }

      const targetUserId = req.params[userIdParam];
      const isOwner = req.user.userId === targetUserId;
      const isAdmin = authService.hasPermission(req.user.role, 'admin');

      if (!isOwner && !isAdmin) {
        res.status(403).json({
          success: false,
          message: '只能访问自己的资源或需要管理员权限'
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Self or admin authorization middleware error:', error);
      res.status(500).json({
        success: false,
        message: '权限验证错误'
      });
    }
  };
};

/**
 * API限流中间件（简单实现）
 */
const requestCounts = new Map<string, { count: number; resetTime: number }>();

export const rateLimit = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const clientId = req.ip || 'unknown';
      const now = Date.now();
      
      const clientData = requestCounts.get(clientId);
      
      if (!clientData || now > clientData.resetTime) {
        // 重置计数
        requestCounts.set(clientId, {
          count: 1,
          resetTime: now + windowMs
        });
        next();
        return;
      }
      
      if (clientData.count >= maxRequests) {
        res.status(429).json({
          success: false,
          message: '请求过于频繁，请稍后重试'
        });
        return;
      }
      
      clientData.count++;
      next();
    } catch (error) {
      console.error('Rate limit middleware error:', error);
      next(); // 限流出错时不阻止请求
    }
  };
};

/**
 * 清理过期的限流记录
 */
setInterval(() => {
  const now = Date.now();
  for (const [clientId, data] of requestCounts.entries()) {
    if (now > data.resetTime) {
      requestCounts.delete(clientId);
    }
  }
}, 5 * 60 * 1000); // 每5分钟清理一次