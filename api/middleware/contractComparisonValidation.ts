/**
 * 合同对比验证中间件
 * 
 * 提供文件上传验证、格式检查、安全验证等功能
 */

import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import type {
  ContractComparisonError,
  ContractComparisonErrorType,
  ApiResponse
} from '../../src/types/contractComparison';

/**
 * 文件验证配置
 */
interface FileValidationConfig {
  maxFileSize: number; // 最大文件大小（字节）
  allowedMimeTypes: string[]; // 允许的MIME类型
  allowedExtensions: string[]; // 允许的文件扩展名
  maxFiles: number; // 最大文件数量
}

/**
 * 默认验证配置
 */
const defaultConfig: FileValidationConfig = {
  maxFileSize: 50 * 1024 * 1024, // 50MB
  allowedMimeTypes: [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/pdf'
  ],
  allowedExtensions: ['.doc', '.docx', '.pdf'],
  maxFiles: 2
};

/**
 * 创建文件上传中间件
 * @param config 验证配置
 * @returns multer中间件
 */
export function createFileUploadMiddleware(config: Partial<FileValidationConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config };
  
  const storage = multer.memoryStorage();
  
  return multer({
    storage,
    limits: {
      fileSize: finalConfig.maxFileSize,
      files: finalConfig.maxFiles,
      fieldSize: 1024 * 1024, // 1MB field size limit
      fieldNameSize: 100, // field name size limit
      fields: 10 // max number of non-file fields
    },
    fileFilter: (req, file, cb) => {
      console.log(`🔍 [文件验证] 检查文件: ${file.originalname}`, {
        mimetype: file.mimetype,
        fieldname: file.fieldname
      });
      
      try {
        // 验证文件名
        if (!file.originalname || file.originalname.trim() === '') {
          return cb(new Error('文件名不能为空'));
        }
        
        // 验证文件名长度
        if (file.originalname.length > 255) {
          return cb(new Error('文件名过长（最大255字符）'));
        }
        
        // 验证文件扩展名
        const extension = getFileExtension(file.originalname);
        if (!finalConfig.allowedExtensions.includes(extension)) {
          return cb(new Error(`不支持的文件扩展名: ${extension}`));
        }
        
        // 验证MIME类型
        if (!finalConfig.allowedMimeTypes.includes(file.mimetype)) {
          return cb(new Error(`不支持的文件类型: ${file.mimetype}`));
        }
        
        // 验证字段名
        if (!['primaryFile', 'secondaryFile'].includes(file.fieldname)) {
          return cb(new Error(`无效的字段名: ${file.fieldname}`));
        }
        
        console.log(`✅ [文件验证] 文件验证通过: ${file.originalname}`);
        cb(null, true);
        
      } catch (error) {
        console.error(`❌ [文件验证] 文件验证失败: ${file.originalname}`, error);
        cb(error instanceof Error ? error : new Error('文件验证失败'));
      }
    }
  });
}

/**
 * 文件上传验证中间件
 */
export function validateFileUpload(req: Request, res: Response, next: NextFunction) {
  console.log('🔍 [上传验证] 开始验证文件上传...');
  
  try {
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    
    // 检查是否有文件
    if (!files || Object.keys(files).length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 检查是否有必需的文件字段
    if (!files.primaryFile || !files.secondaryFile) {
      return res.status(400).json({
        success: false,
        message: '请上传两个文件进行对比（primaryFile 和 secondaryFile）',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 检查文件数量
    if (files.primaryFile.length !== 1 || files.secondaryFile.length !== 1) {
      return res.status(400).json({
        success: false,
        message: '每个字段只能上传一个文件',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    const primaryFile = files.primaryFile[0];
    const secondaryFile = files.secondaryFile[0];
    
    // 验证文件内容
    const primaryValidation = validateFileContent(primaryFile);
    if (!primaryValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: `主文件验证失败: ${primaryValidation.error}`,
        timestamp: new Date()
      } as ApiResponse);
    }
    
    const secondaryValidation = validateFileContent(secondaryFile);
    if (!secondaryValidation.isValid) {
      return res.status(400).json({
        success: false,
        message: `副文件验证失败: ${secondaryValidation.error}`,
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 检查文件是否相同
    if (primaryFile.originalname === secondaryFile.originalname && 
        primaryFile.size === secondaryFile.size) {
      console.warn('⚠️ [上传验证] 检测到可能相同的文件');
    }
    
    console.log('✅ [上传验证] 文件上传验证通过', {
      primary: primaryFile.originalname,
      secondary: secondaryFile.originalname
    });
    
    next();
    
  } catch (error) {
    console.error('❌ [上传验证] 验证失败:', error);
    
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '文件验证失败',
      timestamp: new Date()
    } as ApiResponse);
  }
}

/**
 * 请求体验证中间件
 */
export function validateComparisonRequest(req: Request, res: Response, next: NextFunction) {
  console.log('🔍 [请求验证] 开始验证对比请求...');
  
  try {
    const { sessionId, options } = req.body;
    
    // 验证会话ID
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: '缺少会话ID',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    if (typeof sessionId !== 'string' || sessionId.trim() === '') {
      return res.status(400).json({
        success: false,
        message: '会话ID格式无效',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 验证UUID格式
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(sessionId)) {
      return res.status(400).json({
        success: false,
        message: '会话ID格式无效',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 验证选项（如果提供）
    if (options && typeof options === 'object') {
      const validationResult = validateComparisonOptions(options);
      if (!validationResult.isValid) {
        return res.status(400).json({
          success: false,
          message: `对比选项无效: ${validationResult.error}`,
          timestamp: new Date()
        } as ApiResponse);
      }
    }
    
    console.log('✅ [请求验证] 对比请求验证通过', { sessionId });
    
    next();
    
  } catch (error) {
    console.error('❌ [请求验证] 验证失败:', error);
    
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '请求验证失败',
      timestamp: new Date()
    } as ApiResponse);
  }
}

/**
 * 安全验证中间件
 */
export function validateSecurity(req: Request, res: Response, next: NextFunction) {
  console.log('🔒 [安全验证] 开始安全检查...');
  
  try {
    // 检查用户认证
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '用户未认证',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 检查用户权限（如果需要）
    // 这里可以添加更多的权限检查逻辑
    
    // 检查请求频率限制
    const userAgent = req.get('User-Agent') || '';
    const clientIP = req.ip || req.connection.remoteAddress || '';
    
    console.log('🔒 [安全验证] 请求信息', {
      userId: req.user.userId,
      userAgent: userAgent.substring(0, 100), // 截断长用户代理
      clientIP
    });
    
    // 检查可疑的用户代理
    const suspiciousPatterns = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i
    ];
    
    if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
      console.warn('⚠️ [安全验证] 检测到可疑的用户代理:', userAgent);
      // 可以选择阻止或记录日志
    }
    
    console.log('✅ [安全验证] 安全检查通过');
    
    next();
    
  } catch (error) {
    console.error('❌ [安全验证] 安全检查失败:', error);
    
    res.status(500).json({
      success: false,
      message: '安全验证失败',
      timestamp: new Date()
    } as ApiResponse);
  }
}

/**
 * 验证文件内容
 * @param file 文件对象
 * @returns 验证结果
 */
function validateFileContent(file: Express.Multer.File): { isValid: boolean; error?: string } {
  try {
    // 检查文件大小
    if (file.size === 0) {
      return { isValid: false, error: '文件为空' };
    }
    
    if (file.size > defaultConfig.maxFileSize) {
      const maxSizeMB = defaultConfig.maxFileSize / (1024 * 1024);
      return { isValid: false, error: `文件过大（最大${maxSizeMB}MB）` };
    }
    
    // 检查文件缓冲区
    if (!file.buffer || file.buffer.length === 0) {
      return { isValid: false, error: '文件内容为空' };
    }
    
    // 检查文件头（魔数）
    const magicNumberValidation = validateFileMagicNumber(file);
    if (!magicNumberValidation.isValid) {
      return magicNumberValidation;
    }
    
    return { isValid: true };
    
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : '文件内容验证失败' 
    };
  }
}

/**
 * 验证文件魔数（文件头）
 * @param file 文件对象
 * @returns 验证结果
 */
function validateFileMagicNumber(file: Express.Multer.File): { isValid: boolean; error?: string } {
  const buffer = file.buffer;
  
  // PDF文件魔数: %PDF
  if (file.mimetype === 'application/pdf') {
    const pdfMagic = Buffer.from([0x25, 0x50, 0x44, 0x46]); // %PDF
    if (buffer.length >= 4 && buffer.subarray(0, 4).equals(pdfMagic)) {
      return { isValid: true };
    }
    return { isValid: false, error: '文件不是有效的PDF格式' };
  }
  
  // Word文档魔数检查
  if (file.mimetype.includes('wordprocessingml') || file.mimetype.includes('msword')) {
    // DOCX文件是ZIP格式，魔数: PK
    if (buffer.length >= 2 && buffer[0] === 0x50 && buffer[1] === 0x4B) {
      return { isValid: true };
    }
    
    // DOC文件魔数: D0CF11E0A1B11AE1
    const docMagic = Buffer.from([0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]);
    if (buffer.length >= 8 && buffer.subarray(0, 8).equals(docMagic)) {
      return { isValid: true };
    }
    
    return { isValid: false, error: '文件不是有效的Word文档格式' };
  }
  
  return { isValid: true }; // 其他类型暂时通过
}

/**
 * 验证对比选项
 * @param options 对比选项
 * @returns 验证结果
 */
function validateComparisonOptions(options: any): { isValid: boolean; error?: string } {
  try {
    // 验证ignoreWhitespace
    if (options.ignoreWhitespace !== undefined && typeof options.ignoreWhitespace !== 'boolean') {
      return { isValid: false, error: 'ignoreWhitespace必须是布尔值' };
    }
    
    // 验证ignoreCase
    if (options.ignoreCase !== undefined && typeof options.ignoreCase !== 'boolean') {
      return { isValid: false, error: 'ignoreCase必须是布尔值' };
    }
    
    // 验证minDifferenceLength
    if (options.minDifferenceLength !== undefined) {
      if (typeof options.minDifferenceLength !== 'number' || 
          options.minDifferenceLength < 0 || 
          options.minDifferenceLength > 1000) {
        return { isValid: false, error: 'minDifferenceLength必须是0-1000之间的数字' };
      }
    }
    
    // 验证similarityThreshold
    if (options.similarityThreshold !== undefined) {
      if (typeof options.similarityThreshold !== 'number' || 
          options.similarityThreshold < 0 || 
          options.similarityThreshold > 1) {
        return { isValid: false, error: 'similarityThreshold必须是0-1之间的数字' };
      }
    }
    
    return { isValid: true };
    
  } catch (error) {
    return { 
      isValid: false, 
      error: error instanceof Error ? error.message : '选项验证失败' 
    };
  }
}

/**
 * 获取文件扩展名
 * @param filename 文件名
 * @returns 扩展名（包含点号）
 */
function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return filename.substring(lastDotIndex).toLowerCase();
}

/**
 * Multer错误处理中间件
 */
export function handleMulterError(error: any, req: Request, res: Response, next: NextFunction) {
  console.error('❌ [Multer错误]', error);
  
  if (error instanceof multer.MulterError) {
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          message: '文件大小超过限制（最大50MB）',
          timestamp: new Date()
        } as ApiResponse);
        
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          message: '文件数量超过限制（最多2个文件）',
          timestamp: new Date()
        } as ApiResponse);
        
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          message: '意外的文件字段',
          timestamp: new Date()
        } as ApiResponse);
        
      case 'LIMIT_FIELD_COUNT':
        return res.status(400).json({
          success: false,
          message: '字段数量超过限制',
          timestamp: new Date()
        } as ApiResponse);
        
      default:
        return res.status(400).json({
          success: false,
          message: `文件上传错误: ${error.message}`,
          timestamp: new Date()
        } as ApiResponse);
    }
  }
  
  // 其他错误
  res.status(500).json({
    success: false,
    message: error.message || '文件处理失败',
    timestamp: new Date()
  } as ApiResponse);
}
