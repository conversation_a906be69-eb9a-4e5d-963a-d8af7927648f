/**
 * 输入验证中间件
 * 提供统一的数据验证和清理功能
 */
import { Request, Response, NextFunction } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { CONTRACT_TYPE_VALUES } from '../constants/contractTypes';

// 验证错误处理中间件
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '输入数据验证失败',
      errors: errors.array().map(error => ({
        field: error.type === 'field' ? error.path : 'unknown',
        message: error.msg,
        value: error.type === 'field' ? error.value : undefined
      }))
    });
  }
  next();
};

// 合同创建验证规则
export const validateContractCreation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('合同标题长度必须在1-200字符之间')
    .matches(/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）【】\[\]]+$/)
    .withMessage('合同标题包含非法字符'),
  
  body('type')
    .optional()
    .trim()
    .isIn(CONTRACT_TYPE_VALUES)
    .withMessage('合同类型无效'),
  
  body('content')
    .optional()
    .trim()
    .isLength({ max: 50000 })
    .withMessage('合同内容不能超过50000字符'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('合同描述不能超过1000字符'),
  
  body('counterparty')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('对方当事人名称长度必须在1-100字符之间'),
  
  body('amount')
    .optional()
    .isFloat({ min: 0, max: ************ })
    .withMessage('合同金额必须是有效的正数'),
  
  handleValidationErrors
];

// 合同更新验证规则
export const validateContractUpdate = [
  param('id')
    .isUUID()
    .withMessage('合同ID格式无效'),
  
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('合同标题长度必须在1-200字符之间')
    .matches(/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）【】\[\]]+$/)
    .withMessage('合同标题包含非法字符'),
  
  body('type')
    .optional()
    .trim()
    .isIn(CONTRACT_TYPE_VALUES)
    .withMessage('合同类型无效'),
  
  body('content')
    .optional()
    .trim()
    .isLength({ max: 50000 })
    .withMessage('合同内容不能超过50000字符'),
  
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('合同描述不能超过1000字符'),
  
  body('status')
    .optional()
    .isIn(['uploaded', 'processing', 'reviewed', 'archived', 'draft', 'reviewing', 'approved', 'rejected', 'signed', 'expired'])
    .withMessage('合同状态无效'),
  
  body('counterparty')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('对方当事人名称长度必须在1-100字符之间'),
  
  body('amount')
    .optional()
    .isFloat({ min: 0, max: ************ })
    .withMessage('合同金额必须是有效的正数'),
  
  handleValidationErrors
];

// 合同查询验证规则
export const validateContractQuery = [
  query('page')
    .optional()
    .isInt({ min: 1, max: 10000 })
    .withMessage('页码必须是1-10000之间的整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('搜索关键词长度必须在1-100字符之间')
    .matches(/^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）【】\[\]]+$/)
    .withMessage('搜索关键词包含非法字符'),
  
  query('status')
    .optional()
    .isIn(['uploaded', 'processing', 'reviewed', 'archived', 'draft', 'reviewing', 'approved', 'rejected', 'signed', 'expired'])
    .withMessage('状态筛选值无效'),
  
  query('type')
    .optional()
    .isIn(CONTRACT_TYPE_VALUES)
    .withMessage('类型筛选值无效'),
  
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('开始日期格式无效'),
  
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('结束日期格式无效'),
  
  // 自定义验证：结束日期不能早于开始日期
  body().custom((value, { req }) => {
    const startDate = req.query.start_date;
    const endDate = req.query.end_date;
    
    if (startDate && endDate) {
      const start = new Date(startDate as string);
      const end = new Date(endDate as string);
      
      if (end < start) {
        throw new Error('结束日期不能早于开始日期');
      }
      
      // 日期范围不能超过1年
      const oneYear = 365 * 24 * 60 * 60 * 1000;
      if (end.getTime() - start.getTime() > oneYear) {
        throw new Error('日期范围不能超过1年');
      }
    }
    
    return true;
  }),
  
  handleValidationErrors
];

// 合同ID验证规则
export const validateContractId = [
  param('id')
    .isUUID()
    .withMessage('合同ID格式无效'),
  
  handleValidationErrors
];

// 批量操作验证规则
export const validateBatchOperation = [
  body('contractIds')
    .isArray({ min: 1, max: 100 })
    .withMessage('合同ID列表必须包含1-100个元素'),
  
  body('contractIds.*')
    .isUUID()
    .withMessage('合同ID格式无效'),
  
  body('operation')
    .isIn(['delete', 'updateStatus', 'archive'])
    .withMessage('批量操作类型无效'),
  
  body('status')
    .if(body('operation').equals('updateStatus'))
    .isIn(['uploaded', 'processing', 'reviewed', 'archived', 'draft', 'reviewing', 'approved', 'rejected', 'signed', 'expired'])
    .withMessage('状态值无效'),
  
  handleValidationErrors
];

// 文件上传验证
export const validateFileUpload = (req: Request, res: Response, next: NextFunction) => {
  if (!req.file) {
    return next(); // 文件上传是可选的
  }
  
  const file = req.file;
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/bmp',
    'image/tiff'
  ];

  // 文件类型验证
  if (!allowedTypes.includes(file.mimetype)) {
    return res.status(400).json({
      success: false,
      message: '不支持的文件类型',
      allowedTypes: ['PDF', 'Word文档', '文本文件', '图片文件']
    });
  }
  
  // 文件大小验证 (10MB)
  const maxSize = 10 * 1024 * 1024;
  if (file.size > maxSize) {
    return res.status(400).json({
      success: false,
      message: '文件大小超过限制',
      maxSize: '10MB',
      actualSize: `${(file.size / 1024 / 1024).toFixed(2)}MB`
    });
  }
  
  // 文件名验证和编码处理
  let originalName = file.originalname;

  // 尝试修复编码问题
  try {
    // 如果文件名包含乱码，尝试重新编码
    if (originalName.includes('\\x')) {
      // 尝试从Latin-1解码为UTF-8
      const buffer = Buffer.from(originalName, 'latin1');
      const decodedName = buffer.toString('utf8');
      if (decodedName && decodedName.length > 0) {
        originalName = decodedName;
        console.log('🔧 [文件名修复] 编码修复成功:', {
          original: file.originalname,
          fixed: originalName
        });
      }
    }
  } catch (error) {
    console.warn('⚠️ [文件名修复] 编码修复失败，使用原始文件名:', error);
  }

  // 清理文件名，移除或替换非法字符
  const cleanedName = sanitizeInput.cleanFileName(originalName);

  // 验证清理后的文件名
  if (!cleanedName || cleanedName.length === 0) {
    return res.status(400).json({
      success: false,
      message: '文件名无效，请使用包含中文、英文、数字的有效文件名'
    });
  }

  // 更新文件对象的文件名
  file.originalname = cleanedName;
  
  next();
};

// 数据清理函数
export const sanitizeInput = {
  // 清理HTML标签和特殊字符
  cleanText: (text: string): string => {
    if (!text) return '';
    return text
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/[<>"'&]/g, '') // 移除潜在的XSS字符
      .trim();
  },
  
  // 清理搜索关键词
  cleanSearchTerm: (term: string): string => {
    if (!term) return '';
    return term
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s\-_]/g, '') // 只保留中文、英文、数字、空格、连字符、下划线
      .replace(/\s+/g, ' ') // 合并多个空格
      .trim();
  },
  
  // 清理数字输入
  cleanNumber: (value: any): number | null => {
    if (value === null || value === undefined || value === '') return null;
    const num = parseFloat(value);
    return isNaN(num) ? null : num;
  },

  // 清理文件名
  cleanFileName: (fileName: string): string => {
    if (!fileName) return '';

    // 移除路径分隔符和其他危险字符
    let cleaned = fileName
      .replace(/[\/\\:*?"<>|]/g, '') // 移除文件系统非法字符
      .replace(/[\x00-\x1f\x7f]/g, '') // 移除控制字符
      .trim();

    // 确保文件名不为空且有扩展名
    if (!cleaned || !cleaned.includes('.')) {
      return '';
    }

    // 限制文件名长度
    if (cleaned.length > 255) {
      const parts = cleaned.split('.');
      const extension = parts.pop() || '';
      const baseName = parts.join('.');
      const maxBaseLength = 255 - extension.length - 1;
      cleaned = baseName.substring(0, maxBaseLength) + '.' + extension;
    }

    return cleaned;
  }
};

// 权限验证辅助函数
export const checkPermissions = {
  // 检查是否可以查看合同
  canViewContract: (user: any, contract: any): boolean => {
    if (!user || !contract) return false;
    
    // 管理员和法务人员可以查看所有合同
    if (user.role === 'admin' || user.role === 'legal_staff' || user.role === 'legal_manager') {
      return true;
    }
    
    // 普通用户只能查看自己创建的合同
    return contract.user_id === user.userId;
  },
  
  // 检查是否可以编辑合同
  canEditContract: (user: any, contract: any): boolean => {
    if (!user || !contract) return false;
    
    // 管理员可以编辑所有合同
    if (user.role === 'admin') {
      return true;
    }
    
    // 法务人员可以编辑状态
    if (user.role === 'legal_staff' || user.role === 'legal_manager') {
      return true;
    }
    
    // 合同创建者可以编辑自己的合同（除非已签署或归档）
    if (contract.user_id === user.userId) {
      return !['signed', 'archived'].includes(contract.status);
    }
    
    return false;
  },
  
  // 检查是否可以删除合同
  canDeleteContract: (user: any, contract: any): boolean => {
    if (!user || !contract) return false;
    
    // 管理员可以删除所有合同
    if (user.role === 'admin') {
      return true;
    }
    
    // 合同创建者可以删除草稿状态的合同
    if (contract.user_id === user.userId && contract.status === 'draft') {
      return true;
    }
    
    return false;
  }
};