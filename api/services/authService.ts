import { supabase, supabaseAdmin } from './supabaseClient';
import { supabaseService } from './supabaseService';
import type { User, Session, AuthError } from '@supabase/supabase-js';
import type { Database } from './supabaseClient';

type UserRow = Database['public']['Tables']['users']['Row'];

// 认证响应类型
export interface AuthResponse {
  user: User | null;
  session: Session | null;
  error: AuthError | null;
}

// 用户注册数据类型
export interface SignUpData {
  email: string;
  password: string;
  name: string;
  role?: 'user' | 'legal_staff' | 'legal_manager' | 'admin';
}

// 用户登录数据类型
export interface SignInData {
  email: string;
  password: string;
}

// Token载荷类型
export interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// 认证服务类
export class AuthService {
  /**
   * 用户注册
   */
  static async signUp(userData: SignUpData): Promise<AuthResponse> {
    try {
      // 1. 使用 Supabase Auth 创建用户
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          data: {
            name: userData.name,
            role: userData.role || 'user'
          }
        }
      });

      if (authError) {
        return {
          user: null,
          session: null,
          error: authError
        };
      }

      // 2. 如果认证用户创建成功，在 users 表中创建用户记录
      if (authData.user) {
        const userRecord = await supabaseService.createUser({
          email: userData.email,
          name: userData.name,
          role: userData.role || 'user'
        });

        if (!userRecord) {
          console.error('Failed to create user record in database');
        }
      }

      return {
        user: authData.user,
        session: authData.session,
        error: null
      };
    } catch (error) {
      console.error('Sign up error:', error);
      return {
        user: null,
        session: null,
        error: error as AuthError
      };
    }
  }

  /**
   * 用户登录
   */
  static async signIn(credentials: SignInData): Promise<AuthResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password
      });

      return {
        user: data.user,
        session: data.session,
        error: error
      };
    } catch (error) {
      console.error('Sign in error:', error);
      return {
        user: null,
        session: null,
        error: error as AuthError
      };
    }
  }

  /**
   * 用户登出
   */
  static async signOut(): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (error) {
      console.error('Sign out error:', error);
      return { error: error as AuthError };
    }
  }

  /**
   * 获取当前用户
   */
  static async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('Get current user error:', error);
        return null;
      }
      
      return user;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * 获取当前会话
   */
  static async getCurrentSession(): Promise<Session | null> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Get current session error:', error);
        return null;
      }
      
      return session;
    } catch (error) {
      console.error('Get current session error:', error);
      return null;
    }
  }

  /**
   * 获取当前用户的完整信息（包括数据库中的用户记录）
   */
  static async getCurrentUserProfile(): Promise<UserRow | null> {
    try {
      const user = await this.getCurrentUser();
      
      if (!user) {
        return null;
      }
      
      return await supabaseService.getUserById(user.id);
    } catch (error) {
      console.error('Get current user profile error:', error);
      return null;
    }
  }

  /**
   * 重置密码
   */
  static async resetPassword(email: string): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.FRONTEND_URL}/reset-password`
      });
      
      return { error };
    } catch (error) {
      console.error('Reset password error:', error);
      return { error: error as AuthError };
    }
  }

  /**
   * 更新密码
   */
  static async updatePassword(newPassword: string): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });
      
      return { error };
    } catch (error) {
      console.error('Update password error:', error);
      return { error: error as AuthError };
    }
  }

  /**
   * 更新用户邮箱
   */
  static async updateEmail(newEmail: string): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabase.auth.updateUser({
        email: newEmail
      });
      
      return { error };
    } catch (error) {
      console.error('Update email error:', error);
      return { error: error as AuthError };
    }
  }

  /**
   * 验证用户是否有特定角色权限
   */
  static async hasRole(requiredRole: string): Promise<boolean> {
    try {
      const userProfile = await this.getCurrentUserProfile();
      
      if (!userProfile) {
        return false;
      }
      
      // 角色层级：admin > legal_manager > legal_staff > user
      const roleHierarchy = {
        'user': 1,
        'legal_staff': 2,
        'legal_manager': 3,
        'admin': 4
      };
      
      const userRoleLevel = roleHierarchy[userProfile.role as keyof typeof roleHierarchy] || 0;
      const requiredRoleLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
      
      return userRoleLevel >= requiredRoleLevel;
    } catch (error) {
      console.error('Check role error:', error);
      return false;
    }
  }

  /**
   * 验证用户是否为管理员
   */
  static async isAdmin(): Promise<boolean> {
    return await this.hasRole('admin');
  }

  /**
   * 验证用户是否为法务经理或更高级别
   */
  static async isLegalManager(): Promise<boolean> {
    return await this.hasRole('legal_manager');
  }

  /**
   * 验证用户是否为法务人员或更高级别
   */
  static async isLegalStaff(): Promise<boolean> {
    return await this.hasRole('legal_staff');
  }

  /**
   * 监听认证状态变化
   */
  static onAuthStateChange(callback: (event: string, session: Session | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  }

  /**
   * 刷新会话
   */
  static async refreshSession(): Promise<{ session: Session | null; error: AuthError | null }> {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      
      return {
        session: data.session,
        error: error
      };
    } catch (error) {
      console.error('Refresh session error:', error);
      return {
        session: null,
        error: error as AuthError
      };
    }
  }

  /**
   * 管理员创建用户（跳过邮箱验证）
   */
  static async adminCreateUser(userData: SignUpData): Promise<{ user: User | null; error: AuthError | null }> {
    try {
      // 使用管理员客户端创建用户
      const { data, error } = await supabaseAdmin.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true, // 跳过邮箱验证
        user_metadata: {
          name: userData.name,
          role: userData.role || 'user'
        }
      });

      if (error) {
        return { user: null, error };
      }

      // 在 users 表中创建用户记录
      if (data.user) {
        const userRecord = await supabaseService.createUser({
          email: userData.email,
          name: userData.name,
          role: userData.role || 'user'
        });

        if (!userRecord) {
          console.error('Failed to create user record in database');
        }
      }

      return { user: data.user, error: null };
    } catch (error) {
      console.error('Admin create user error:', error);
      return { user: null, error: error as AuthError };
    }
  }

  /**
   * 管理员删除用户
   */
  static async adminDeleteUser(userId: string): Promise<{ error: AuthError | null }> {
    try {
      const { error } = await supabaseAdmin.auth.admin.deleteUser(userId);
      return { error };
    } catch (error) {
      console.error('Admin delete user error:', error);
      return { error: error as AuthError };
    }
  }

  /**
   * 验证JWT token
   */
  static async verifyToken(token: string): Promise<TokenPayload | null> {
    try {
      const { data, error } = await supabase.auth.getUser(token);
      
      if (error || !data.user) {
        return null;
      }

      // 获取用户完整信息
      const userProfile = await supabaseService.getUserById(data.user.id);
      
      if (!userProfile) {
        return null;
      }

      return {
        userId: data.user.id,
        email: data.user.email || '',
        role: userProfile.role
      };
    } catch (error) {
      console.error('Verify token error:', error);
      return null;
    }
  }

  /**
   * 检查用户权限
   */
  static hasPermission(userRole: string, requiredRole: string): boolean {
    // 角色层级：admin > legal_manager > legal_staff > user
    const roleHierarchy = {
      'user': 1,
      'legal_staff': 2,
      'legal_manager': 3,
      'admin': 4
    };
    
    const userRoleLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredRoleLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
    
    return userRoleLevel >= requiredRoleLevel;
  }
}

// 导出类和实例
export const authService = AuthService;
export default AuthService;