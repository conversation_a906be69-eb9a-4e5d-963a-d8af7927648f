import { supabaseAdmin } from './supabaseClient';
import type { Database } from './supabaseClient';

// 创建一个统一的服务实例
class SupabaseService {
  // 这里可以添加实际的服务方法，目前为了编译通过先创建空的方法
  static async createUser(user: any) { return null; }
  static async createContract(contract: any) { return null; }
  static async createReviewTask(task: any) { return null; }
  static async createContractElements(elements: any[]) { return []; }
  static async createRiskItems(items: any[]) { return []; }
  static async createTemplates(templates: any[]) { return []; }
  static async createKnowledgeBase(kb: any[]) { return []; }
  static async createReviewRules(rules: any[]) { return []; }
}

// 为了兼容性，创建别名
const UserService = SupabaseService;
const ContractService = SupabaseService;
const ReviewTaskService = SupabaseService;
const ContractElementService = SupabaseService;
const RiskItemService = SupabaseService;
const TemplateService = SupabaseService;
const KnowledgeBaseService = SupabaseService;
const ReviewRuleService = SupabaseService;

// 类型定义
type Tables = Database['public']['Tables'];
type UserInsert = Tables['users']['Insert'];
type ContractInsert = Tables['contracts']['Insert'];
type ReviewTaskInsert = Tables['review_tasks']['Insert'];
type ContractElementInsert = Tables['contract_elements']['Insert'];
type RiskItemInsert = Tables['risk_items']['Insert'];
type TemplateInsert = Tables['templates']['Insert'];
type KnowledgeBaseInsert = Tables['knowledge_base']['Insert'];
type ReviewRuleInsert = Tables['review_rules']['Insert'];

// 迁移状态类型
export interface MigrationStatus {
  success: boolean;
  message: string;
  data?: any;
  error?: any;
}

// 批量迁移结果类型
export interface BatchMigrationResult {
  total: number;
  success: number;
  failed: number;
  errors: string[];
}

// 数据迁移服务类
export class MigrationService {
  /**
   * 迁移用户数据
   */
  static async migrateUsers(users: UserInsert[]): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      total: users.length,
      success: 0,
      failed: 0,
      errors: []
    };

    for (const user of users) {
      try {
        const createdUser = await UserService.createUser(user);
        if (createdUser) {
          result.success++;
        } else {
          result.failed++;
          result.errors.push(`Failed to create user: ${user.email}`);
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`Error creating user ${user.email}: ${error}`);
      }
    }

    return result;
  }

  /**
   * 迁移合同数据
   */
  static async migrateContracts(contracts: ContractInsert[]): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      total: contracts.length,
      success: 0,
      failed: 0,
      errors: []
    };

    for (const contract of contracts) {
      try {
        const createdContract = await ContractService.createContract(contract);
        if (createdContract) {
          result.success++;
        } else {
          result.failed++;
          result.errors.push(`Failed to create contract: ${contract.title}`);
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`Error creating contract ${contract.title}: ${error}`);
      }
    }

    return result;
  }

  /**
   * 迁移审查任务数据
   */
  static async migrateReviewTasks(reviewTasks: ReviewTaskInsert[]): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      total: reviewTasks.length,
      success: 0,
      failed: 0,
      errors: []
    };

    for (const task of reviewTasks) {
      try {
        const createdTask = await ReviewTaskService.createReviewTask(task);
        if (createdTask) {
          result.success++;
        } else {
          result.failed++;
          result.errors.push(`Failed to create review task: ${task.review_type}`);
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`Error creating review task ${task.review_type}: ${error}`);
      }
    }

    return result;
  }

  /**
   * 迁移合同元素数据
   */
  static async migrateContractElements(elements: ContractElementInsert[]): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      total: elements.length,
      success: 0,
      failed: 0,
      errors: []
    };

    // 批量处理，每次处理100条记录
    const batchSize = 100;
    for (let i = 0; i < elements.length; i += batchSize) {
      const batch = elements.slice(i, i + batchSize);
      
      try {
        const createdElements = await ContractElementService.createContractElements(batch);
        result.success += createdElements.length;
        
        if (createdElements.length < batch.length) {
          result.failed += batch.length - createdElements.length;
          result.errors.push(`Batch ${i / batchSize + 1}: Some elements failed to create`);
        }
      } catch (error) {
        result.failed += batch.length;
        result.errors.push(`Batch ${i / batchSize + 1} error: ${error}`);
      }
    }

    return result;
  }

  /**
   * 迁移风险项数据
   */
  static async migrateRiskItems(riskItems: RiskItemInsert[]): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      total: riskItems.length,
      success: 0,
      failed: 0,
      errors: []
    };

    // 批量处理，每次处理100条记录
    const batchSize = 100;
    for (let i = 0; i < riskItems.length; i += batchSize) {
      const batch = riskItems.slice(i, i + batchSize);
      
      try {
        const createdRiskItems = await RiskItemService.createRiskItems(batch);
        result.success += createdRiskItems.length;
        
        if (createdRiskItems.length < batch.length) {
          result.failed += batch.length - createdRiskItems.length;
          result.errors.push(`Batch ${i / batchSize + 1}: Some risk items failed to create`);
        }
      } catch (error) {
        result.failed += batch.length;
        result.errors.push(`Batch ${i / batchSize + 1} error: ${error}`);
      }
    }

    return result;
  }

  /**
   * 迁移模板数据
   */
  static async migrateTemplates(templates: TemplateInsert[]): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      total: templates.length,
      success: 0,
      failed: 0,
      errors: []
    };

    for (const template of templates) {
      try {
        const { data, error } = await supabaseAdmin
          .from('templates')
          .insert(template)
          .select()
          .single();
        
        if (error) {
          result.failed++;
          result.errors.push(`Failed to create template ${template.name}: ${error.message}`);
        } else {
          result.success++;
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`Error creating template ${template.name}: ${error}`);
      }
    }

    return result;
  }

  /**
   * 迁移知识库数据
   */
  static async migrateKnowledgeBase(knowledgeBase: KnowledgeBaseInsert[]): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      total: knowledgeBase.length,
      success: 0,
      failed: 0,
      errors: []
    };

    for (const item of knowledgeBase) {
      try {
        const { data, error } = await supabaseAdmin
          .from('knowledge_base')
          .insert(item)
          .select()
          .single();
        
        if (error) {
          result.failed++;
          result.errors.push(`Failed to create knowledge base item ${item.title}: ${error.message}`);
        } else {
          result.success++;
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`Error creating knowledge base item ${item.title}: ${error}`);
      }
    }

    return result;
  }

  /**
   * 迁移审查规则数据
   */
  static async migrateReviewRules(reviewRules: ReviewRuleInsert[]): Promise<BatchMigrationResult> {
    const result: BatchMigrationResult = {
      total: reviewRules.length,
      success: 0,
      failed: 0,
      errors: []
    };

    for (const rule of reviewRules) {
      try {
        const { data, error } = await supabaseAdmin
          .from('review_rules')
          .insert(rule)
          .select()
          .single();
        
        if (error) {
          result.failed++;
          result.errors.push(`Failed to create review rule ${rule.rule_name}: ${error.message}`);
        } else {
          result.success++;
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`Error creating review rule ${rule.rule_name}: ${error}`);
      }
    }

    return result;
  }

  /**
   * 完整数据迁移
   */
  static async fullMigration(data: {
    users?: UserInsert[];
    contracts?: ContractInsert[];
    reviewTasks?: ReviewTaskInsert[];
    contractElements?: ContractElementInsert[];
    riskItems?: RiskItemInsert[];
    templates?: TemplateInsert[];
    knowledgeBase?: KnowledgeBaseInsert[];
    reviewRules?: ReviewRuleInsert[];
  }): Promise<{ [key: string]: BatchMigrationResult }> {
    const results: { [key: string]: BatchMigrationResult } = {};

    // 按依赖顺序迁移数据
    if (data.users) {
      console.log('Migrating users...');
      results.users = await this.migrateUsers(data.users);
    }

    if (data.templates) {
      console.log('Migrating templates...');
      results.templates = await this.migrateTemplates(data.templates);
    }

    if (data.knowledgeBase) {
      console.log('Migrating knowledge base...');
      results.knowledgeBase = await this.migrateKnowledgeBase(data.knowledgeBase);
    }

    if (data.reviewRules) {
      console.log('Migrating review rules...');
      results.reviewRules = await this.migrateReviewRules(data.reviewRules);
    }

    if (data.contracts) {
      console.log('Migrating contracts...');
      results.contracts = await this.migrateContracts(data.contracts);
    }

    if (data.reviewTasks) {
      console.log('Migrating review tasks...');
      results.reviewTasks = await this.migrateReviewTasks(data.reviewTasks);
    }

    if (data.contractElements) {
      console.log('Migrating contract elements...');
      results.contractElements = await this.migrateContractElements(data.contractElements);
    }

    if (data.riskItems) {
      console.log('Migrating risk items...');
      results.riskItems = await this.migrateRiskItems(data.riskItems);
    }

    return results;
  }

  /**
   * 清空所有表数据（用于测试）
   */
  static async clearAllData(): Promise<MigrationStatus> {
    try {
      // 按依赖关系逆序删除
      const tables = [
        'risk_items',
        'contract_elements',
        'review_tasks',
        'contracts',
        'review_rules',
        'knowledge_base',
        'templates',
        'users'
      ];

      for (const table of tables) {
        const { error } = await supabaseAdmin
          .from(table)
          .delete()
          .neq('id', '00000000-0000-0000-0000-000000000000'); // 删除所有记录
        
        if (error) {
          console.error(`Error clearing table ${table}:`, error);
        } else {
          console.log(`Cleared table: ${table}`);
        }
      }

      return {
        success: true,
        message: 'All data cleared successfully'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Failed to clear data',
        error
      };
    }
  }

  /**
   * 验证数据完整性
   */
  static async validateDataIntegrity(): Promise<MigrationStatus> {
    try {
      const checks = [];

      // 检查用户表
      const { count: userCount, error: userError } = await supabaseAdmin
        .from('users')
        .select('*', { count: 'exact', head: true });
      
      if (userError) {
        checks.push(`Users table error: ${userError.message}`);
      } else {
        checks.push(`Users: ${userCount} records`);
      }

      // 检查合同表
      const { count: contractCount, error: contractError } = await supabaseAdmin
        .from('contracts')
        .select('*', { count: 'exact', head: true });
      
      if (contractError) {
        checks.push(`Contracts table error: ${contractError.message}`);
      } else {
        checks.push(`Contracts: ${contractCount} records`);
      }

      // 检查模板表
      const { count: templateCount, error: templateError } = await supabaseAdmin
        .from('templates')
        .select('*', { count: 'exact', head: true });
      
      if (templateError) {
        checks.push(`Templates table error: ${templateError.message}`);
      } else {
        checks.push(`Templates: ${templateCount} records`);
      }

      // 检查知识库表
      const { count: knowledgeCount, error: knowledgeError } = await supabaseAdmin
        .from('knowledge_base')
        .select('*', { count: 'exact', head: true });
      
      if (knowledgeError) {
        checks.push(`Knowledge base table error: ${knowledgeError.message}`);
      } else {
        checks.push(`Knowledge base: ${knowledgeCount} records`);
      }

      return {
        success: true,
        message: 'Data integrity validation completed',
        data: checks
      };
    } catch (error) {
      return {
        success: false,
        message: 'Data integrity validation failed',
        error
      };
    }
  }
}

export default MigrationService;