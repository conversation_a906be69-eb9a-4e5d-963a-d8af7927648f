/**
 * 数据完整性检查服务
 * 确保数据库操作的一致性和完整性
 */
import supabaseService from './supabaseService';
import { CONTRACT_TYPE_VALUES } from '../constants/contractTypes';

export interface IntegrityCheckResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ContractIntegrityData {
  id?: string;
  user_id: string;
  title: string;
  category?: string;
  status?: string;
  file_path?: string;
  file_url?: string;
}

class DataIntegrityService {
  /**
   * 检查合同数据完整性
   */
  async checkContractIntegrity(contractData: ContractIntegrityData, isUpdate: boolean = false): Promise<IntegrityCheckResult> {
    const result: IntegrityCheckResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // 1. 检查必填字段
    await this.checkRequiredFields(contractData, result, isUpdate);

    // 2. 检查用户存在性
    await this.checkUserExists(contractData.user_id, result);

    // 3. 检查文件路径一致性
    if (contractData.file_path || contractData.file_url) {
      await this.checkFileConsistency(contractData, result);
    }

    // 4. 检查状态转换合法性
    // if (contractData.id && contractData.status) {
    //   await this.checkStatusTransition(contractData.id, contractData.status, result);
    // }

    // 5. 检查重复合同
    await this.checkDuplicateContract(contractData, result);

    // 6. 检查数据格式
    this.checkDataFormat(contractData, result);

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * 检查必填字段
   */
  private async checkRequiredFields(contractData: ContractIntegrityData, result: IntegrityCheckResult, isUpdate: boolean = false): Promise<void> {
    // 在更新操作时，user_id由后端自动设置，不需要检查
    if (!isUpdate && !contractData.user_id) {
      result.errors.push('用户ID不能为空');
    }

    if (!contractData.title || contractData.title.trim().length === 0) {
      result.errors.push('合同标题不能为空');
    }

    if (contractData.title && contractData.title.length > 200) {
      result.errors.push('合同标题长度不能超过200字符');
    }
  }

  /**
   * 检查用户是否存在
   */
  private async checkUserExists(userId: string, result: IntegrityCheckResult): Promise<void> {
    // 如果没有提供用户ID，跳过检查
    if (!userId) {
      return;
    }
    
    try {
      const user = await supabaseService.getUserById(userId);
      
      if (!user) {
        result.errors.push(`用户ID ${userId} 不存在`);
      }
    } catch (error) {
      result.warnings.push(`无法验证用户ID ${userId} 的存在性`);
    }
  }

  /**
   * 检查文件路径一致性
   */
  private async checkFileConsistency(contractData: ContractIntegrityData, result: IntegrityCheckResult): Promise<void> {
    // 检查文件路径和URL的一致性
    if (contractData.file_path && contractData.file_url) {
      const pathFromUrl = this.extractPathFromUrl(contractData.file_url);
      if (pathFromUrl && pathFromUrl !== contractData.file_path) {
        result.warnings.push('文件路径与文件URL不一致');
      }
    }

    // 检查文件是否真实存在（如果有文件路径）
    if (contractData.file_path) {
      try {
        const exists = await supabaseService.checkFileExists(contractData.file_path);

        if (!exists) {
          result.warnings.push(`文件 ${contractData.file_path} 在存储中不存在`);
        }
      } catch (error) {
        result.warnings.push(`无法验证文件 ${contractData.file_path} 的存在性`);
      }
    }
  }

  /**
   * 检查状态转换的合法性
   */
  private async checkStatusTransition(contractId: string, newStatus: string, result: IntegrityCheckResult): Promise<void> {
    try {
      const currentContract = await supabaseService.getContractById(contractId);

      if (!currentContract) {
        result.errors.push(`合同ID ${contractId} 不存在`);
        return;
      }

      const currentStatus = currentContract.status;
      
      // 允许相同状态的更新（用于修改其他字段）
      if (currentStatus === newStatus) {
        return;
      }
      
      const validTransitions = this.getValidStatusTransitions(currentStatus);

      if (!validTransitions.includes(newStatus)) {
        result.errors.push(`不能从状态 "${currentStatus}" 转换到 "${newStatus}"`);
      }
    } catch (error) {
      result.warnings.push(`无法验证状态转换的合法性`);
    }
  }

  /**
   * 检查重复合同
   */
  private async checkDuplicateContract(contractData: ContractIntegrityData, result: IntegrityCheckResult): Promise<void> {
    try {
      const contractsResult = await supabaseService.getContracts({
        page: 1,
        limit: 100,
        search: contractData.title,
        userId: contractData.user_id
      });

      let duplicates = contractsResult.contracts.filter(contract => 
        contract.title === contractData.title
      );

      // 如果是更新操作，排除当前合同
      if (contractData.id) {
        duplicates = duplicates.filter(contract => contract.id !== contractData.id);
      }

      if (duplicates.length > 0) {
        result.warnings.push(`用户已存在同名合同: "${contractData.title}"`);
      }
    } catch (error) {
      result.warnings.push('无法检查重复合同');
    }
  }

  /**
   * 检查数据格式
   */
  private checkDataFormat(contractData: ContractIntegrityData, result: IntegrityCheckResult): void {
    // 检查UUID格式
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (contractData.id && !uuidRegex.test(contractData.id)) {
      result.errors.push('合同ID格式无效');
    }

    if (contractData.user_id && !uuidRegex.test(contractData.user_id)) {
      result.errors.push('用户ID格式无效');
    }

    // 检查状态值
    const validStatuses = ['uploaded', 'processing', 'reviewed', 'archived', 'draft', 'reviewing', 'approved', 'rejected', 'signed', 'expired'];
    if (contractData.status && !validStatuses.includes(contractData.status)) {
      result.errors.push(`无效的合同状态: ${contractData.status}`);
    }

    // 检查类型值
    if (contractData.category && !CONTRACT_TYPE_VALUES.includes(contractData.category)) {
      result.errors.push(`无效的合同类型: ${contractData.category}`);
    }

    // 检查标题格式
    if (contractData.title) {
      const titleRegex = /^[\u4e00-\u9fa5a-zA-Z0-9\s\-_()（）【】\[\]]+$/;
      if (!titleRegex.test(contractData.title)) {
        result.errors.push('合同标题包含非法字符');
      }
    }
  }

  /**
   * 获取有效的状态转换
   */
  private getValidStatusTransitions(currentStatus: string): string[] {
    const transitions: Record<string, string[]> = {
      'draft': ['uploaded', 'archived'],
      'uploaded': ['processing', 'archived', 'draft'],
      'processing': ['reviewed', 'rejected', 'archived'],
      'reviewed': ['approved', 'rejected', 'reviewing'],
      'reviewing': ['approved', 'rejected', 'reviewed'],
      'approved': ['signed', 'rejected', 'archived'],
      'rejected': ['draft', 'reviewing', 'archived'],
      'signed': ['archived', 'expired'],
      'expired': ['archived'],
      'archived': [] // 归档状态不能转换到其他状态
    };

    return transitions[currentStatus] || [];
  }

  /**
   * 从URL中提取文件路径
   */
  private extractPathFromUrl(url: string): string | null {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      // 假设URL格式为: /storage/v1/object/public/contracts/path/to/file
      const contractsIndex = pathParts.indexOf('contracts');
      if (contractsIndex !== -1 && contractsIndex < pathParts.length - 1) {
        return pathParts.slice(contractsIndex + 1).join('/');
      }
    } catch (error) {
      // URL格式无效
    }
    return null;
  }

  /**
   * 批量检查合同数据完整性
   */
  async batchCheckContractIntegrity(contractsData: ContractIntegrityData[]): Promise<IntegrityCheckResult[]> {
    const results: IntegrityCheckResult[] = [];
    
    for (const contractData of contractsData) {
      const result = await this.checkContractIntegrity(contractData);
      results.push(result);
    }
    
    return results;
  }

  /**
   * 检查数据库约束
   */
  async checkDatabaseConstraints(): Promise<IntegrityCheckResult> {
    const result: IntegrityCheckResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // 检查孤立的合同记录（用户不存在）
      const contractsResult = await supabaseService.getContracts({
        page: 1,
        limit: 100
      });

      if (contractsResult.contracts) {
        for (const contract of contractsResult.contracts) {
          try {
            const user = await supabaseService.getUserById(contract.user_id);
            if (!user) {
              result.warnings.push(`发现孤立合同记录: ${contract.id}，用户 ${contract.user_id} 不存在`);
            }
          } catch (error) {
            // 跳过单个用户检查错误
          }
        }
      } else {
        result.warnings.push('无法检查孤立合同记录');
      }

      // 检查文件引用完整性
       const contractsWithFilesResult = await supabaseService.getContracts({
         page: 1,
         limit: 50
       });

      if (contractsWithFilesResult.contracts) {
         const contractsWithFiles = contractsWithFilesResult.contracts.filter(contract => contract.file_path);
        
        for (const contract of contractsWithFiles) {
          if (contract.file_path) {
            try {
              const exists = await supabaseService.checkFileExists(contract.file_path);

              if (!exists) {
                result.warnings.push(`合同 ${contract.id} 引用的文件 ${contract.file_path} 不存在`);
              }
            } catch (error) {
              // 跳过单个文件检查错误
            }
          }
        }
      } else {
        result.warnings.push('无法检查文件引用完整性');
      }

    } catch (error) {
      result.warnings.push('数据库约束检查过程中发生错误');
    }

    result.isValid = result.errors.length === 0;
    return result;
  }

  /**
   * 修复数据完整性问题
   */
  async repairDataIntegrity(options: {
    removeOrphanContracts?: boolean;
    updateFileReferences?: boolean;
    fixStatusInconsistencies?: boolean;
  } = {}): Promise<{
    repaired: number;
    errors: string[];
  }> {
    const repairResult = {
      repaired: 0,
      errors: []
    };

    try {
      // 修复孤立合同记录
      if (options.removeOrphanContracts) {
        // 这里可以实现删除孤立记录的逻辑
        // 注意：这是危险操作，需要谨慎处理
      }

      // 修复文件引用
      if (options.updateFileReferences) {
        // 这里可以实现修复文件引用的逻辑
      }

      // 修复状态不一致
      if (options.fixStatusInconsistencies) {
        // 这里可以实现修复状态的逻辑
      }

    } catch (error) {
      repairResult.errors.push('数据修复过程中发生错误');
    }

    return repairResult;
  }
}

export const dataIntegrityService = new DataIntegrityService();