/**
 * 合同差异分析服务
 * 
 * 负责分析两个文档之间的差异，实现文本差异检测算法
 * 支持段落级别和句子级别对比，计算相似度
 */

import * as jsdiff from 'jsdiff';
import { v4 as uuidv4 } from 'uuid';
import type {
  ParsedDocument,
  DifferenceItem,
  DifferenceType,
  DifferenceSeverity,
  TextPosition,
  ParagraphInfo,
  ContractComparisonError,
  ContractComparisonErrorType
} from '../../src/types/contractComparison';

/**
 * 差异分析配置接口
 */
interface DifferenceAnalysisOptions {
  /** 是否忽略空白字符 */
  ignoreWhitespace: boolean;
  /** 是否忽略大小写 */
  ignoreCase: boolean;
  /** 最小差异长度 */
  minDifferenceLength: number;
  /** 相似度阈值 */
  similarityThreshold: number;
}

/**
 * 合同差异分析器类
 */
export class ContractDifferenceAnalyzer {
  
  private readonly defaultOptions: DifferenceAnalysisOptions = {
    ignoreWhitespace: true,
    ignoreCase: false,
    minDifferenceLength: 3,
    similarityThreshold: 0.8
  };
  
  /**
   * 分析两个文档的差异
   * @param primaryDoc 主文档
   * @param secondaryDoc 副文档
   * @param options 分析选项
   * @returns 差异项列表
   */
  async analyzeDifferences(
    primaryDoc: ParsedDocument,
    secondaryDoc: ParsedDocument,
    options: Partial<DifferenceAnalysisOptions> = {}
  ): Promise<DifferenceItem[]> {
    console.log('🔍 [差异分析] 开始分析文档差异...');
    
    const analysisOptions = { ...this.defaultOptions, ...options };
    const differences: DifferenceItem[] = [];
    
    try {
      // 1. 文本级别差异分析
      const textDifferences = await this.performTextDiff(
        primaryDoc.content.plainText,
        secondaryDoc.content.plainText,
        analysisOptions
      );
      differences.push(...textDifferences);
      
      // 2. 段落级别差异分析
      const paragraphDifferences = await this.performParagraphDiff(
        primaryDoc.content.structure.paragraphs,
        secondaryDoc.content.structure.paragraphs,
        analysisOptions
      );
      differences.push(...paragraphDifferences);
      
      // 3. 结构级别差异分析
      const structuralDifferences = await this.performStructuralDiff(
        primaryDoc,
        secondaryDoc,
        analysisOptions
      );
      differences.push(...structuralDifferences);
      
      // 4. 合并和去重差异
      const mergedDifferences = this.mergeDifferences(differences);
      
      // 5. 评估差异严重程度
      const evaluatedDifferences = this.evaluateDifferenceSeverity(mergedDifferences);
      
      console.log(`✅ [差异分析] 差异分析完成，发现 ${evaluatedDifferences.length} 个差异`);
      
      return evaluatedDifferences;
      
    } catch (error) {
      console.error('❌ [差异分析] 差异分析失败:', error);
      throw this.createError(
        ContractComparisonErrorType.COMPARISON_FAILED,
        `差异分析失败: ${error instanceof Error ? error.message : '未知错误'}`
      );
    }
  }
  
  /**
   * 执行文本级别差异分析
   * @param primaryText 主文档文本
   * @param secondaryText 副文档文本
   * @param options 分析选项
   * @returns 差异项列表
   */
  private async performTextDiff(
    primaryText: string,
    secondaryText: string,
    options: DifferenceAnalysisOptions
  ): Promise<DifferenceItem[]> {
    console.log('📝 [文本差异] 执行文本级别差异分析...');
    
    const differences: DifferenceItem[] = [];
    
    // 预处理文本
    const processedPrimary = this.preprocessText(primaryText, options);
    const processedSecondary = this.preprocessText(secondaryText, options);
    
    // 使用jsdiff进行差异检测
    const diffResult = jsdiff.diffWords(processedPrimary, processedSecondary);
    
    let primaryIndex = 0;
    let secondaryIndex = 0;
    
    for (const part of diffResult) {
      const value = part.value;
      
      if (part.added) {
        // 新增内容
        differences.push({
          id: uuidv4(),
          type: DifferenceType.ADDED,
          severity: DifferenceSeverity.MEDIUM,
          secondaryPosition: {
            startIndex: secondaryIndex,
            endIndex: secondaryIndex + value.length,
            lineNumber: this.getLineNumber(secondaryText, secondaryIndex),
            columnNumber: this.getColumnNumber(secondaryText, secondaryIndex),
            paragraphIndex: this.getParagraphIndex(secondaryText, secondaryIndex)
          },
          secondaryContent: value,
          description: `新增内容: "${this.truncateText(value)}"`
        });
        secondaryIndex += value.length;
        
      } else if (part.removed) {
        // 删除内容
        differences.push({
          id: uuidv4(),
          type: DifferenceType.DELETED,
          severity: DifferenceSeverity.MEDIUM,
          primaryPosition: {
            startIndex: primaryIndex,
            endIndex: primaryIndex + value.length,
            lineNumber: this.getLineNumber(primaryText, primaryIndex),
            columnNumber: this.getColumnNumber(primaryText, primaryIndex),
            paragraphIndex: this.getParagraphIndex(primaryText, primaryIndex)
          },
          primaryContent: value,
          description: `删除内容: "${this.truncateText(value)}"`
        });
        primaryIndex += value.length;
        
      } else {
        // 相同内容，更新索引
        primaryIndex += value.length;
        secondaryIndex += value.length;
      }
    }
    
    console.log(`📝 [文本差异] 文本差异分析完成，发现 ${differences.length} 个差异`);
    return differences;
  }
  
  /**
   * 执行段落级别差异分析
   * @param primaryParagraphs 主文档段落
   * @param secondaryParagraphs 副文档段落
   * @param options 分析选项
   * @returns 差异项列表
   */
  private async performParagraphDiff(
    primaryParagraphs: ParagraphInfo[],
    secondaryParagraphs: ParagraphInfo[],
    options: DifferenceAnalysisOptions
  ): Promise<DifferenceItem[]> {
    console.log('📄 [段落差异] 执行段落级别差异分析...');
    
    const differences: DifferenceItem[] = [];
    
    // 创建段落内容数组用于比较
    const primaryContents = primaryParagraphs.map(p => this.preprocessText(p.content, options));
    const secondaryContents = secondaryParagraphs.map(p => this.preprocessText(p.content, options));
    
    // 使用最长公共子序列算法找到匹配的段落
    const matches = this.findParagraphMatches(primaryContents, secondaryContents, options);
    
    // 标记未匹配的段落为新增或删除
    const matchedPrimary = new Set(matches.map(m => m.primaryIndex));
    const matchedSecondary = new Set(matches.map(m => m.secondaryIndex));
    
    // 处理删除的段落
    for (let i = 0; i < primaryParagraphs.length; i++) {
      if (!matchedPrimary.has(i)) {
        differences.push({
          id: uuidv4(),
          type: DifferenceType.DELETED,
          severity: DifferenceSeverity.HIGH,
          primaryPosition: primaryParagraphs[i].position,
          primaryContent: primaryParagraphs[i].content,
          description: `删除段落: "${this.truncateText(primaryParagraphs[i].content)}"`
        });
      }
    }
    
    // 处理新增的段落
    for (let i = 0; i < secondaryParagraphs.length; i++) {
      if (!matchedSecondary.has(i)) {
        differences.push({
          id: uuidv4(),
          type: DifferenceType.ADDED,
          severity: DifferenceSeverity.HIGH,
          secondaryPosition: secondaryParagraphs[i].position,
          secondaryContent: secondaryParagraphs[i].content,
          description: `新增段落: "${this.truncateText(secondaryParagraphs[i].content)}"`
        });
      }
    }
    
    // 处理修改的段落
    for (const match of matches) {
      const primaryPara = primaryParagraphs[match.primaryIndex];
      const secondaryPara = secondaryParagraphs[match.secondaryIndex];
      
      if (match.similarity < options.similarityThreshold) {
        differences.push({
          id: uuidv4(),
          type: DifferenceType.MODIFIED,
          severity: DifferenceSeverity.MEDIUM,
          primaryPosition: primaryPara.position,
          secondaryPosition: secondaryPara.position,
          primaryContent: primaryPara.content,
          secondaryContent: secondaryPara.content,
          description: `修改段落: 相似度 ${(match.similarity * 100).toFixed(1)}%`
        });
      }
    }
    
    console.log(`📄 [段落差异] 段落差异分析完成，发现 ${differences.length} 个差异`);
    return differences;
  }
  
  /**
   * 执行结构级别差异分析
   * @param primaryDoc 主文档
   * @param secondaryDoc 副文档
   * @param options 分析选项
   * @returns 差异项列表
   */
  private async performStructuralDiff(
    primaryDoc: ParsedDocument,
    secondaryDoc: ParsedDocument,
    options: DifferenceAnalysisOptions
  ): Promise<DifferenceItem[]> {
    console.log('🏗️ [结构差异] 执行结构级别差异分析...');
    
    const differences: DifferenceItem[] = [];
    
    const primaryStructure = primaryDoc.content.structure;
    const secondaryStructure = secondaryDoc.content.structure;
    
    // 比较表格数量
    if (primaryStructure.tables.length !== secondaryStructure.tables.length) {
      differences.push({
        id: uuidv4(),
        type: primaryStructure.tables.length > secondaryStructure.tables.length 
          ? DifferenceType.DELETED 
          : DifferenceType.ADDED,
        severity: DifferenceSeverity.HIGH,
        description: `表格数量变化: ${primaryStructure.tables.length} → ${secondaryStructure.tables.length}`
      });
    }
    
    // 比较标题数量
    if (primaryStructure.headers.length !== secondaryStructure.headers.length) {
      differences.push({
        id: uuidv4(),
        type: primaryStructure.headers.length > secondaryStructure.headers.length 
          ? DifferenceType.DELETED 
          : DifferenceType.ADDED,
        severity: DifferenceSeverity.MEDIUM,
        description: `标题数量变化: ${primaryStructure.headers.length} → ${secondaryStructure.headers.length}`
      });
    }
    
    // 比较列表数量
    if (primaryStructure.lists.length !== secondaryStructure.lists.length) {
      differences.push({
        id: uuidv4(),
        type: primaryStructure.lists.length > secondaryStructure.lists.length 
          ? DifferenceType.DELETED 
          : DifferenceType.ADDED,
        severity: DifferenceSeverity.LOW,
        description: `列表数量变化: ${primaryStructure.lists.length} → ${secondaryStructure.lists.length}`
      });
    }
    
    console.log(`🏗️ [结构差异] 结构差异分析完成，发现 ${differences.length} 个差异`);
    return differences;
  }
  
  /**
   * 计算两个文档的相似度
   * @param primaryDoc 主文档
   * @param secondaryDoc 副文档
   * @returns 相似度（0-1）
   */
  calculateSimilarity(primaryDoc: ParsedDocument, secondaryDoc: ParsedDocument): number {
    const primaryText = primaryDoc.content.plainText;
    const secondaryText = secondaryDoc.content.plainText;
    
    // 使用Jaccard相似度算法
    const primaryWords = new Set(primaryText.toLowerCase().split(/\s+/));
    const secondaryWords = new Set(secondaryText.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...primaryWords].filter(word => secondaryWords.has(word)));
    const union = new Set([...primaryWords, ...secondaryWords]);
    
    return intersection.size / union.size;
  }
  
  /**
   * 预处理文本
   * @param text 原始文本
   * @param options 处理选项
   * @returns 处理后的文本
   */
  private preprocessText(text: string, options: DifferenceAnalysisOptions): string {
    let processed = text;
    
    if (options.ignoreWhitespace) {
      processed = processed.replace(/\s+/g, ' ').trim();
    }
    
    if (options.ignoreCase) {
      processed = processed.toLowerCase();
    }
    
    return processed;
  }
  
  /**
   * 查找段落匹配
   * @param primaryContents 主文档段落内容
   * @param secondaryContents 副文档段落内容
   * @param options 分析选项
   * @returns 匹配结果
   */
  private findParagraphMatches(
    primaryContents: string[],
    secondaryContents: string[],
    options: DifferenceAnalysisOptions
  ): Array<{ primaryIndex: number; secondaryIndex: number; similarity: number }> {
    const matches: Array<{ primaryIndex: number; secondaryIndex: number; similarity: number }> = [];
    
    for (let i = 0; i < primaryContents.length; i++) {
      let bestMatch = -1;
      let bestSimilarity = 0;
      
      for (let j = 0; j < secondaryContents.length; j++) {
        const similarity = this.calculateTextSimilarity(primaryContents[i], secondaryContents[j]);
        if (similarity > bestSimilarity && similarity >= options.similarityThreshold) {
          bestSimilarity = similarity;
          bestMatch = j;
        }
      }
      
      if (bestMatch !== -1) {
        matches.push({
          primaryIndex: i,
          secondaryIndex: bestMatch,
          similarity: bestSimilarity
        });
      }
    }
    
    return matches;
  }
  
  /**
   * 计算文本相似度
   * @param text1 文本1
   * @param text2 文本2
   * @returns 相似度（0-1）
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    if (text1 === text2) return 1;
    if (text1.length === 0 || text2.length === 0) return 0;
    
    // 使用编辑距离计算相似度
    const distance = this.levenshteinDistance(text1, text2);
    const maxLength = Math.max(text1.length, text2.length);
    
    return 1 - (distance / maxLength);
  }
  
  /**
   * 计算编辑距离
   * @param str1 字符串1
   * @param str2 字符串2
   * @returns 编辑距离
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length][str1.length];
  }
  
  /**
   * 合并重复的差异
   * @param differences 差异列表
   * @returns 合并后的差异列表
   */
  private mergeDifferences(differences: DifferenceItem[]): DifferenceItem[] {
    // 简化的合并逻辑，实际应用中可以更复杂
    const uniqueDifferences = new Map<string, DifferenceItem>();
    
    for (const diff of differences) {
      const key = `${diff.type}-${diff.primaryContent || ''}-${diff.secondaryContent || ''}`;
      if (!uniqueDifferences.has(key)) {
        uniqueDifferences.set(key, diff);
      }
    }
    
    return Array.from(uniqueDifferences.values());
  }
  
  /**
   * 评估差异严重程度
   * @param differences 差异列表
   * @returns 评估后的差异列表
   */
  private evaluateDifferenceSeverity(differences: DifferenceItem[]): DifferenceItem[] {
    return differences.map(diff => {
      // 根据差异类型和内容长度评估严重程度
      const contentLength = Math.max(
        diff.primaryContent?.length || 0,
        diff.secondaryContent?.length || 0
      );
      
      if (contentLength > 100) {
        diff.severity = DifferenceSeverity.HIGH;
      } else if (contentLength > 20) {
        diff.severity = DifferenceSeverity.MEDIUM;
      } else {
        diff.severity = DifferenceSeverity.LOW;
      }
      
      return diff;
    });
  }
  
  /**
   * 获取行号
   * @param text 文本
   * @param index 字符索引
   * @returns 行号
   */
  private getLineNumber(text: string, index: number): number {
    return text.substring(0, index).split('\n').length;
  }
  
  /**
   * 获取列号
   * @param text 文本
   * @param index 字符索引
   * @returns 列号
   */
  private getColumnNumber(text: string, index: number): number {
    const beforeIndex = text.substring(0, index);
    const lastNewlineIndex = beforeIndex.lastIndexOf('\n');
    return index - lastNewlineIndex;
  }
  
  /**
   * 获取段落索引
   * @param text 文本
   * @param index 字符索引
   * @returns 段落索引
   */
  private getParagraphIndex(text: string, index: number): number {
    return text.substring(0, index).split('\n\n').length - 1;
  }
  
  /**
   * 截断文本用于显示
   * @param text 文本
   * @param maxLength 最大长度
   * @returns 截断后的文本
   */
  private truncateText(text: string, maxLength: number = 50): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }
  
  /**
   * 创建错误对象
   * @param type 错误类型
   * @param message 错误消息
   * @returns 错误对象
   */
  private createError(
    type: ContractComparisonErrorType,
    message: string
  ): ContractComparisonError {
    const error = new Error(message) as any;
    error.type = type;
    error.message = message;
    error.timestamp = new Date();
    return error;
  }
}
