import { createClient, SupabaseClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceRoleKey) {
  throw new Error('Missing Supabase environment variables');
}

// 客户端实例（使用匿名密钥，用于前端交互）
export const supabaseClient: SupabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  }
});

// 服务端实例（使用服务角色密钥，用于后端操作）
export const supabaseAdmin: SupabaseClient = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// 数据库类型定义
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string;
          role: 'user' | 'legal_staff' | 'legal_manager' | 'admin';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          name: string;
          role?: 'user' | 'legal_staff' | 'legal_manager' | 'admin';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          role?: 'user' | 'legal_staff' | 'legal_manager' | 'admin';
          created_at?: string;
          updated_at?: string;
        };
      };
      contracts: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          category: string | null;
          file_path: string | null;
          content: string | null;
          ocr_content: string | null;
          status: 'uploaded' | 'processing' | 'reviewed' | 'archived';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          category?: string | null;
          file_path?: string | null;
          content?: string | null;
          ocr_content?: string | null;
          status?: 'uploaded' | 'processing' | 'reviewed' | 'archived';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          category?: string | null;
          file_path?: string | null;
          content?: string | null;
          ocr_content?: string | null;
          status?: 'uploaded' | 'processing' | 'reviewed' | 'archived';
          created_at?: string;
          updated_at?: string;
        };
      };
      review_tasks: {
        Row: {
          id: string;
          contract_id: string;
          user_id: string;
          review_type: string;
          analysis_result: any;
          status: 'pending' | 'processing' | 'completed' | 'failed';
          created_at: string;
          completed_at: string | null;
        };
        Insert: {
          id?: string;
          contract_id: string;
          user_id: string;
          review_type: string;
          analysis_result?: any;
          status?: 'pending' | 'processing' | 'completed' | 'failed';
          created_at?: string;
          completed_at?: string | null;
        };
        Update: {
          id?: string;
          contract_id?: string;
          user_id?: string;
          review_type?: string;
          analysis_result?: any;
          status?: 'pending' | 'processing' | 'completed' | 'failed';
          created_at?: string;
          completed_at?: string | null;
        };
      };
      contract_elements: {
        Row: {
          id: string;
          contract_id: string;
          element_type: string;
          element_value: string;
          confidence_score: number | null;
          extracted_at: string;
        };
        Insert: {
          id?: string;
          contract_id: string;
          element_type: string;
          element_value: string;
          confidence_score?: number | null;
          extracted_at?: string;
        };
        Update: {
          id?: string;
          contract_id?: string;
          element_type?: string;
          element_value?: string;
          confidence_score?: number | null;
          extracted_at?: string;
        };
      };
      risk_items: {
        Row: {
          id: string;
          review_task_id: string;
          risk_type: string;
          risk_level: 'low' | 'medium' | 'high' | 'critical';
          description: string;
          suggestion: string | null;
          identified_at: string;
        };
        Insert: {
          id?: string;
          review_task_id: string;
          risk_type: string;
          risk_level: 'low' | 'medium' | 'high' | 'critical';
          description: string;
          suggestion?: string | null;
          identified_at?: string;
        };
        Update: {
          id?: string;
          review_task_id?: string;
          risk_type?: string;
          risk_level?: 'low' | 'medium' | 'high' | 'critical';
          description?: string;
          suggestion?: string | null;
          identified_at?: string;
        };
      };
      templates: {
        Row: {
          id: string;
          name: string;
          category: string;
          content: string;
          parameters: any;
          is_active: boolean;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          category: string;
          content: string;
          parameters?: any;
          is_active?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          category?: string;
          content?: string;
          parameters?: any;
          is_active?: boolean;
          created_at?: string;
        };
      };
      knowledge_base: {
        Row: {
          id: string;
          type: 'regulation' | 'case_law' | 'template' | 'guideline';
          title: string;
          content: string;
          metadata: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          type: 'regulation' | 'case_law' | 'template' | 'guideline';
          title: string;
          content: string;
          metadata?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          type?: 'regulation' | 'case_law' | 'template' | 'guideline';
          title?: string;
          content?: string;
          metadata?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      review_rules: {
        Row: {
          id: string;
          knowledge_base_id: string;
          rule_name: string;
          rule_content: string;
          severity: 'info' | 'warning' | 'error' | 'critical';
          is_active: boolean;
        };
        Insert: {
          id?: string;
          knowledge_base_id: string;
          rule_name: string;
          rule_content: string;
          severity: 'info' | 'warning' | 'error' | 'critical';
          is_active?: boolean;
        };
        Update: {
          id?: string;
          knowledge_base_id?: string;
          rule_name?: string;
          rule_content?: string;
          severity?: 'info' | 'warning' | 'error' | 'critical';
          is_active?: boolean;
        };
      };
    };
  };
}

// 导出类型化的客户端
export const supabase = supabaseClient as SupabaseClient<Database>;
export const supabaseAdminClient = supabaseAdmin as SupabaseClient<Database>;