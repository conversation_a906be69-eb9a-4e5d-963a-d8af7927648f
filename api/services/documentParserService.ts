/**
 * Document Parser Service
 * 文档解析服务，支持PDF、Word文档和图片的文本提取
 */
import fs from 'fs';
import path from 'path';
import mammoth from 'mammoth';
import Tesseract from 'tesseract.js';
import crypto from 'crypto';

// 位置映射接口 - 用于HTML位置到纯文本位置的转换
export interface PositionMapping {
  htmlStart: number;
  htmlEnd: number;
  textStart: number;
  textEnd: number;
  type: 'text' | 'tag' | 'whitespace';
}

// 段落信息
export interface ParagraphInfo {
  id: string;
  htmlStart: number;
  htmlEnd: number;
  textStart: number;
  textEnd: number;
  level?: number; // 标题级别
  style?: string;
}

// 表格信息
export interface TableInfo {
  id: string;
  htmlStart: number;
  htmlEnd: number;
  textStart: number;
  textEnd: number;
  rows: number;
  columns: number;
}

// 文档结构信息
export interface DocumentStructure {
  paragraphs: ParagraphInfo[];
  tables: TableInfo[];
  headings: ParagraphInfo[];
}

// 双轨制解析结果
export interface ParseResult {
  success: boolean;

  // 显示内容（HTML格式，保留完整格式）
  displayContent: string;

  // 对比内容（纯文本，用于差异计算）
  compareContent: string;

  // 文档结构信息
  structure: DocumentStructure;

  // 位置映射（HTML位置 -> 纯文本位置）
  positionMap: PositionMapping[];

  // 错误信息
  error?: string;

  // 元数据
  metadata?: {
    pageCount?: number;
    wordCount?: number;
    confidence?: number;
    hasHtmlContent?: boolean;
    originalTextLength?: number;
    displayContentLength?: number;
    compareContentLength?: number;
  };
}

export class DocumentParserService {

  /**
   * 创建HTML到纯文本的位置映射
   * @param htmlContent HTML内容
   * @param plainText 纯文本内容
   * @returns 位置映射数组
   */
  private createPositionMapping(htmlContent: string, plainText: string): PositionMapping[] {
    const mappings: PositionMapping[] = [];
    let htmlIndex = 0;
    let textIndex = 0;

    console.log('🔧 [位置映射] 开始创建位置映射:', {
      htmlLength: htmlContent.length,
      textLength: plainText.length
    });

    while (htmlIndex < htmlContent.length) {
      const htmlChar = htmlContent[htmlIndex];

      if (htmlChar === '<') {
        // 处理HTML标签
        const tagStart = htmlIndex;
        let tagEnd = htmlIndex;

        // 找到标签结束位置
        while (tagEnd < htmlContent.length && htmlContent[tagEnd] !== '>') {
          tagEnd++;
        }
        tagEnd++; // 包含 '>'

        // 记录标签映射（标签不对应文本内容）
        mappings.push({
          htmlStart: tagStart,
          htmlEnd: tagEnd,
          textStart: textIndex,
          textEnd: textIndex,
          type: 'tag'
        });

        htmlIndex = tagEnd;
      } else {
        // 处理文本内容
        const textStart = textIndex;
        const htmlStart = htmlIndex;

        // 收集连续的文本字符
        while (htmlIndex < htmlContent.length &&
               htmlContent[htmlIndex] !== '<' &&
               textIndex < plainText.length) {

          const htmlChar = htmlContent[htmlIndex];
          const textChar = plainText[textIndex];

          // 处理HTML实体和特殊字符
          if (this.isMatchingChar(htmlChar, textChar)) {
            htmlIndex++;
            textIndex++;
          } else if (this.isWhitespace(htmlChar) && this.isWhitespace(textChar)) {
            // 处理空白字符的差异
            htmlIndex++;
            textIndex++;
          } else if (this.isWhitespace(htmlChar)) {
            // HTML中的额外空白
            htmlIndex++;
          } else if (this.isWhitespace(textChar)) {
            // 纯文本中的额外空白
            textIndex++;
          } else {
            // 字符不匹配，尝试跳过
            htmlIndex++;
          }
        }

        // 记录文本映射
        if (htmlStart < htmlIndex && textStart < textIndex) {
          mappings.push({
            htmlStart: htmlStart,
            htmlEnd: htmlIndex,
            textStart: textStart,
            textEnd: textIndex,
            type: 'text'
          });
        }
      }
    }

    console.log('🔧 [位置映射] 映射创建完成:', {
      mappingsCount: mappings.length,
      textMappings: mappings.filter(m => m.type === 'text').length,
      tagMappings: mappings.filter(m => m.type === 'tag').length
    });

    return mappings;
  }

  /**
   * 检查两个字符是否匹配（考虑HTML实体）
   */
  private isMatchingChar(htmlChar: string, textChar: string): boolean {
    return htmlChar === textChar;
  }

  /**
   * 检查字符是否为空白字符
   */
  private isWhitespace(char: string): boolean {
    return /\s/.test(char);
  }

  /**
   * 提取文档结构信息
   * @param htmlContent HTML内容
   * @param positionMap 位置映射
   * @returns 文档结构
   */
  private extractDocumentStructure(htmlContent: string, positionMap: PositionMapping[]): DocumentStructure {
    const structure: DocumentStructure = {
      paragraphs: [],
      tables: [],
      headings: []
    };

    // 提取段落
    const paragraphRegex = /<p[^>]*>(.*?)<\/p>/gs;
    let match;
    let paragraphId = 0;

    while ((match = paragraphRegex.exec(htmlContent)) !== null) {
      const htmlStart = match.index;
      const htmlEnd = match.index + match[0].length;
      const textPosition = this.mapHtmlToText(htmlStart, htmlEnd, positionMap);

      structure.paragraphs.push({
        id: `p-${paragraphId++}`,
        htmlStart,
        htmlEnd,
        textStart: textPosition.start,
        textEnd: textPosition.end
      });
    }

    // 提取标题
    const headingRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gs;
    let headingId = 0;

    while ((match = headingRegex.exec(htmlContent)) !== null) {
      const level = parseInt(match[1]);
      const htmlStart = match.index;
      const htmlEnd = match.index + match[0].length;
      const textPosition = this.mapHtmlToText(htmlStart, htmlEnd, positionMap);

      const heading: ParagraphInfo = {
        id: `h-${headingId++}`,
        htmlStart,
        htmlEnd,
        textStart: textPosition.start,
        textEnd: textPosition.end,
        level
      };

      structure.headings.push(heading);
    }

    // 提取表格
    const tableRegex = /<table[^>]*>(.*?)<\/table>/gs;
    let tableId = 0;

    while ((match = tableRegex.exec(htmlContent)) !== null) {
      const htmlStart = match.index;
      const htmlEnd = match.index + match[0].length;
      const textPosition = this.mapHtmlToText(htmlStart, htmlEnd, positionMap);

      // 计算行数和列数
      const tableContent = match[1];
      const rows = (tableContent.match(/<tr[^>]*>/g) || []).length;
      const firstRow = tableContent.match(/<tr[^>]*>(.*?)<\/tr>/s);
      const columns = firstRow ? (firstRow[1].match(/<t[dh][^>]*>/g) || []).length : 0;

      structure.tables.push({
        id: `table-${tableId++}`,
        htmlStart,
        htmlEnd,
        textStart: textPosition.start,
        textEnd: textPosition.end,
        rows,
        columns
      });
    }

    console.log('🔧 [文档结构] 结构提取完成:', {
      paragraphs: structure.paragraphs.length,
      headings: structure.headings.length,
      tables: structure.tables.length
    });

    return structure;
  }

  /**
   * 将HTML位置映射到纯文本位置
   */
  private mapHtmlToText(htmlStart: number, htmlEnd: number, positionMap: PositionMapping[]): { start: number; end: number } {
    let textStart = 0;
    let textEnd = 0;

    for (const mapping of positionMap) {
      if (mapping.type === 'text') {
        if (mapping.htmlStart <= htmlStart && htmlStart < mapping.htmlEnd) {
          const offset = htmlStart - mapping.htmlStart;
          textStart = mapping.textStart + offset;
        }
        if (mapping.htmlStart < htmlEnd && htmlEnd <= mapping.htmlEnd) {
          const offset = htmlEnd - mapping.htmlStart;
          textEnd = mapping.textStart + offset;
        }
      }
    }

    return { start: textStart, end: textEnd };
  }
  /**
   * 解析文档内容
   * @param buffer 文件缓冲区
   * @param mimeType 文件MIME类型
   * @param fileName 文件名
   * @returns 解析结果
   */
  async parseDocument(buffer: Buffer, mimeType: string, fileName: string): Promise<ParseResult> {
    console.log('🔧 [解析服务调试] === DocumentParserService.parseDocument 开始 ===');
    console.log('🔧 [解析服务调试] 接收到解析请求:', {
      mimeType,
      fileName,
      bufferSize: buffer?.length,
      bufferExists: !!buffer,
      supportedType: DocumentParserService.isSupportedFileType(mimeType),
      timestamp: new Date().toISOString()
    });
    
    // 验证输入参数
    if (!buffer || buffer.length === 0) {
      console.error('❌ [解析服务调试] 无效的文件缓冲区');
      return {
        success: false,
        displayContent: '',
        compareContent: '',
        structure: { paragraphs: [], tables: [], headings: [] },
        positionMap: [],
        error: '文件缓冲区为空或无效'
      };
    }

    if (!DocumentParserService.isSupportedFileType(mimeType)) {
      console.error('❌ [解析服务调试] 不支持的文件类型:', mimeType);
      return {
        success: false,
        displayContent: '',
        compareContent: '',
        structure: { paragraphs: [], tables: [], headings: [] },
        positionMap: [],
        error: `不支持的文件类型: ${mimeType}`
      };
    }
    
    try {
      console.log('✅ [解析服务调试] 文件类型验证通过，开始解析...');
      
      let result: ParseResult;
      const parseStartTime = Date.now();
      
      switch (mimeType) {
        case 'application/pdf':
          console.log('📄 [解析服务调试] 使用PDF解析器...');
          result = await this.parsePDF(buffer);
          break;
        
        case 'application/msword':
        case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          console.log('📝 [解析服务调试] 使用Word解析器...');
          result = await this.parseWord(buffer);
          break;
        
        case 'text/plain':
          console.log('📃 [解析服务调试] 使用文本解析器...');
          result = this.parseText(buffer);
          break;
        
        case 'image/jpeg':
        case 'image/png':
        case 'image/gif':
        case 'image/bmp':
        case 'image/tiff':
          console.log('🖼️ [解析服务调试] 使用图片OCR解析器...');
          result = await this.parseImage(buffer);
          break;
        
        default:
          console.log('❌ [解析服务调试] 文件类型不支持:', mimeType);
          result = {
            success: false,
            displayContent: '',
            compareContent: '',
            structure: { paragraphs: [], tables: [], headings: [] },
            positionMap: [],
            error: `不支持的文件类型: ${mimeType}`
          };
      }
      
      const parseEndTime = Date.now();
      const parseDuration = parseEndTime - parseStartTime;
      
      console.log('📊 [解析服务调试] 解析完成，统计信息:', {
        success: result.success,
        displayContentLength: result.displayContent?.length || 0,
        compareContentLength: result.compareContent?.length || 0,
        hasError: !!result.error,
        parseDuration: parseDuration + 'ms',
        wordCount: result.metadata?.wordCount || 0,
        positionMappings: result.positionMap?.length || 0,
        structureElements: (result.structure?.paragraphs?.length || 0) +
                          (result.structure?.tables?.length || 0) +
                          (result.structure?.headings?.length || 0)
      });

      if (result.success && result.displayContent) {
        console.log('📖 [解析服务调试] 显示内容预览 (前100字符):', result.displayContent.substring(0, 100) + (result.displayContent.length > 100 ? '...' : ''));
        console.log('📖 [解析服务调试] 对比内容预览 (前100字符):', result.compareContent.substring(0, 100) + (result.compareContent.length > 100 ? '...' : ''));
      }
      
      console.log('🏁 [解析服务调试] === DocumentParserService.parseDocument 成功结束 ===');
      return result;
    } catch (error) {
      console.error('💥 [解析服务调试] === DocumentParserService.parseDocument 异常 ===');
      console.error('💥 [解析服务调试] 异常类型:', error.constructor.name);
      console.error('💥 [解析服务调试] 错误消息:', error.message);
      console.error('💥 [解析服务调试] 错误详情:', error);
      if (error.stack) {
        console.error('💥 [解析服务调试] 错误堆栈:', error.stack);
      }
      
      const result = {
        success: false,
        displayContent: '',
        compareContent: '',
        structure: { paragraphs: [], tables: [], headings: [] },
        positionMap: [],
        error: error instanceof Error ? error.message : '文档解析失败'
      };
      
      console.log('🏁 [解析服务调试] === DocumentParserService.parseDocument 异常结束 ===');
      return result;
    }
  }

  /**
   * 解析PDF文档 - 双轨制架构
   */
  private async parsePDF(buffer: Buffer): Promise<ParseResult> {
    console.log('🔧 [PDF解析调试] 开始PDF解析（双轨制），缓冲区大小:', buffer.length);
    try {
      const pdfParse = (await import('pdf-parse')).default;
      console.log('🔧 [PDF解析调试] PDF解析库加载成功，开始解析...');
      const data = await pdfParse(buffer);

      const content = data.text.trim();

      // 对于PDF文本，显示内容和对比内容相同
      const displayContent = content;
      const compareContent = content;

      // 创建简单的位置映射（1:1映射）
      const positionMap: PositionMapping[] = [{
        htmlStart: 0,
        htmlEnd: content.length,
        textStart: 0,
        textEnd: content.length,
        type: 'text'
      }];

      // 创建简单的文档结构（将整个文本作为一个段落）
      const structure: DocumentStructure = {
        paragraphs: [{
          id: 'p-0',
          htmlStart: 0,
          htmlEnd: content.length,
          textStart: 0,
          textEnd: content.length
        }],
        tables: [],
        headings: []
      };

      console.log('🔧 [PDF解析调试] PDF解析完成:', {
        pageCount: data.numpages,
        textLength: content.length,
        wordCount: content.split(/\s+/).length
      });

      return {
        success: true,
        displayContent,
        compareContent,
        structure,
        positionMap,
        metadata: {
          pageCount: data.numpages,
          wordCount: content.split(/\s+/).length,
          hasHtmlContent: false,
          displayContentLength: content.length,
          compareContentLength: content.length
        }
      };
    } catch (error) {
      console.error('🔧 [PDF解析调试] PDF解析失败:', error);
      throw new Error(`PDF解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 解析Word文档
   */
  private async parseWord(buffer: Buffer): Promise<ParseResult> {
    console.log('🔧 [Word解析调试] 开始Word文档解析，缓冲区大小:', buffer.length);
    try {
      console.log('🔧 [Word解析调试] 使用mammoth库转换Word文档为HTML格式...');
      const result = await mammoth.convertToHtml({ buffer });

      // 提取纯文本用于字数统计和高亮计算
      const textResult = await mammoth.extractRawText({ buffer });

      console.log('🔧 [Word解析调试] Word文档解析完成:', {
        htmlLength: result.value.length,
        textLength: textResult.value.length,
        wordCount: textResult.value.split(/\s+/).length,
        hasMessages: result.messages?.length > 0,
        hasHtmlTags: result.value.includes('<table>') || result.value.includes('<p>'),
        htmlPreview: result.value.substring(0, 300),
        textPreview: textResult.value.substring(0, 200)
      });

      // 如果有转换消息，记录日志
      if (result.messages && result.messages.length > 0) {
        console.log('🔧 [Word解析调试] Word转换消息:', result.messages.map(m => m.message));
      }

      // 检查HTML内容的质量
      const htmlTagCount = (result.value.match(/<[^>]+>/g) || []).length;
      const isValidHtml = htmlTagCount > 0 && (result.value.includes('<p>') || result.value.includes('<table>'));

      console.log('🔧 [Word解析调试] HTML质量检查:', {
        htmlTagCount,
        isValidHtml,
        containsTable: result.value.includes('<table>'),
        containsParagraph: result.value.includes('<p>'),
        containsSpan: result.value.includes('<span>'),
        containsStrong: result.value.includes('<strong>')
      });

      const displayContent = result.value.trim();
      const compareContent = textResult.value.trim();

      console.log('🔧 [Word解析调试] 开始创建位置映射...');
      const positionMap = this.createPositionMapping(displayContent, compareContent);

      // 提取文档结构
      console.log('🔧 [Word解析调试] 开始提取文档结构...');
      const structure = this.extractDocumentStructure(displayContent, positionMap);

      console.log('🔧 [Word解析调试] 双轨制解析完成:', {
        htmlTagCount,
        isValidHtml,
        positionMappings: positionMap.length,
        structureParagraphs: structure.paragraphs.length,
        structureHeadings: structure.headings.length,
        structureTables: structure.tables.length,
        containsTable: displayContent.includes('<table>'),
        containsParagraph: displayContent.includes('<p>')
      });

      return {
        success: true,
        displayContent,
        compareContent,
        structure,
        positionMap,
        metadata: {
          wordCount: compareContent.split(/\s+/).length,
          hasHtmlContent: isValidHtml,
          originalTextLength: compareContent.length,
          displayContentLength: displayContent.length,
          compareContentLength: compareContent.length
        }
      };
    } catch (error) {
      console.error('🔧 [Word解析调试] Word文档解析失败:', error);
      throw new Error(`Word文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 解析纯文本文件 - 双轨制架构
   */
  private parseText(buffer: Buffer): ParseResult {
    console.log('🔧 [文本解析调试] 开始文本文件解析（双轨制），缓冲区大小:', buffer.length);
    try {
      console.log('🔧 [文本解析调试] 使用UTF-8编码解析文本...');
      const content = buffer.toString('utf-8').trim();

      // 对于纯文本，显示内容和对比内容相同
      const displayContent = content;
      const compareContent = content;

      // 创建简单的位置映射（1:1映射）
      const positionMap: PositionMapping[] = [{
        htmlStart: 0,
        htmlEnd: content.length,
        textStart: 0,
        textEnd: content.length,
        type: 'text'
      }];

      // 创建简单的文档结构（将整个文本作为一个段落）
      const structure: DocumentStructure = {
        paragraphs: [{
          id: 'p-0',
          htmlStart: 0,
          htmlEnd: content.length,
          textStart: 0,
          textEnd: content.length
        }],
        tables: [],
        headings: []
      };

      console.log('🔧 [文本解析调试] 文本解析完成:', {
        contentLength: content.length,
        wordCount: content.split(/\s+/).length
      });

      return {
        success: true,
        displayContent,
        compareContent,
        structure,
        positionMap,
        metadata: {
          wordCount: content.split(/\s+/).length,
          hasHtmlContent: false,
          displayContentLength: content.length,
          compareContentLength: content.length
        }
      };
    } catch (error) {
      console.error('🔧 [文本解析调试] 文本文件解析失败:', error);
      throw new Error(`文本文件解析失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 解析图片文件（OCR）- 双轨制架构
   */
  private async parseImage(buffer: Buffer): Promise<ParseResult> {
    console.log('🔧 [图片解析调试] 开始图片OCR解析（双轨制），缓冲区大小:', buffer.length);
    try {
      console.log('🔧 [图片解析调试] 使用Tesseract进行OCR识别，语言: chi_sim+eng');
      const { data: { text, confidence } } = await Tesseract.recognize(buffer, 'chi_sim+eng', {
        logger: m => console.log('🔧 [图片解析调试] OCR进度:', m) // 显示OCR进度
      });

      const content = text.trim();

      // 对于OCR文本，显示内容和对比内容相同
      const displayContent = content;
      const compareContent = content;

      // 创建简单的位置映射（1:1映射）
      const positionMap: PositionMapping[] = [{
        htmlStart: 0,
        htmlEnd: content.length,
        textStart: 0,
        textEnd: content.length,
        type: 'text'
      }];

      // 创建简单的文档结构（将整个文本作为一个段落）
      const structure: DocumentStructure = {
        paragraphs: [{
          id: 'p-0',
          htmlStart: 0,
          htmlEnd: content.length,
          textStart: 0,
          textEnd: content.length
        }],
        tables: [],
        headings: []
      };

      console.log('🔧 [图片解析调试] OCR识别完成:', {
        textLength: content.length,
        confidence: Math.round(confidence),
        wordCount: content.split(/\s+/).length
      });

      return {
        success: true,
        displayContent,
        compareContent,
        structure,
        positionMap,
        metadata: {
          confidence: Math.round(confidence),
          wordCount: content.split(/\s+/).length,
          hasHtmlContent: false,
          displayContentLength: content.length,
          compareContentLength: content.length
        }
      };
    } catch (error) {
      console.error('🔧 [图片解析调试] 图片OCR识别失败:', error);
      throw new Error(`图片OCR识别失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 清理和格式化提取的文本
   */
  private cleanText(text: string): string {
    return text
      .replace(/\r\n/g, '\n') // 统一换行符
      .replace(/\r/g, '\n')
      .replace(/\n{3,}/g, '\n\n') // 合并多余的空行
      .replace(/[ \t]+/g, ' ') // 合并多余的空格
      .trim();
  }

  /**
   * 验证文件类型是否支持
   */
  static isSupportedFileType(mimeType: string): boolean {
    const supportedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/tiff'
    ];
    
    return supportedTypes.includes(mimeType);
  }

  /**
   * 获取支持的文件扩展名
   */
  static getSupportedExtensions(): string[] {
    return ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'];
  }


}

// 导出单例实例
export const documentParserService = new DocumentParserService();