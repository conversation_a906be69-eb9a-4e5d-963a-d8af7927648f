/**
 * Review Service
 * 智能审查服务，处理合同智能分析、风险识别和条款对比
 */
import { mockDB, Contract, ReviewTask, ContractElement, RiskItem, ReviewRule } from './mockDatabase.js';
import { v4 as uuidv4 } from 'uuid';

export interface ReviewRequest {
  contract_id: string;
  review_type: 'full' | 'quick' | 'custom';
  focus_areas?: string[];
  custom_rules?: string[];
}

export interface ReviewResult {
  task_id: string;
  contract_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  risk_score: number;
  elements: ContractElement[];
  risks: RiskItem[];
  suggestions: ReviewSuggestion[];
  summary: ReviewSummary;
  created_at: string;
  completed_at?: string;
}

export interface ReviewSuggestion {
  id: string;
  type: 'warning' | 'error' | 'info' | 'suggestion';
  category: string;
  title: string;
  description: string;
  location: {
    paragraph?: number;
    line?: number;
    text_snippet?: string;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendation: string;
  auto_fixable: boolean;
}

export interface ReviewSummary {
  total_issues: number;
  critical_issues: number;
  high_issues: number;
  medium_issues: number;
  low_issues: number;
  compliance_score: number;
  readability_score: number;
  completeness_score: number;
  key_findings: string[];
  missing_clauses: string[];
  recommended_actions: string[];
}

export interface ComparisonRequest {
  contract_id: string;
  template_id?: string;
  comparison_type: 'template' | 'standard' | 'custom';
  focus_areas?: string[];
}

export interface ComparisonResult {
  contract_id: string;
  template_id?: string;
  differences: ContractDifference[];
  similarity_score: number;
  missing_clauses: string[];
  extra_clauses: string[];
  recommendations: string[];
}

export interface ContractDifference {
  type: 'missing' | 'different' | 'extra';
  clause_type: string;
  contract_text?: string;
  template_text?: string;
  severity: 'low' | 'medium' | 'high';
  description: string;
}

class ReviewService {
  // 开始智能审查
  async startReview(userId: string, request: ReviewRequest): Promise<{ success: boolean; task?: ReviewTask; message?: string }> {
    try {
      // 验证合同是否存在
      const contract = await mockDB.getContractById(request.contract_id);
      if (!contract) {
        return {
          success: false,
          message: '合同不存在'
        };
      }

      // 创建审查任务
      const task = await mockDB.createReviewTask({
        contract_id: request.contract_id,
        user_id: userId,
        reviewer_id: userId,
        review_type: request.review_type,
        task_type: 'element_extraction',
        status: 'pending',
        progress: 0
      });

      // 异步执行审查（模拟）
      this.performReview(task.id, contract, request).catch(error => {
        console.error('Review execution error:', error);
      });

      return {
        success: true,
        task
      };
    } catch (error) {
      console.error('Start review error:', error);
      return {
        success: false,
        message: '启动审查失败'
      };
    }
  }

  // 执行审查（模拟异步处理）
  private async performReview(taskId: string, contract: Contract, request: ReviewRequest): Promise<void> {
    try {
      // 更新状态为处理中
      await mockDB.updateReviewTask(taskId, {
        status: 'processing',
        progress: 10
      });

      // 模拟处理时间
      await this.delay(1000);

      // 分析合同要素
      const elements = await this.analyzeContractElements(contract);
      await mockDB.updateReviewTask(taskId, { progress: 30 });

      // 识别风险项
      const risks = await this.identifyRisks(contract, elements);
      await mockDB.updateReviewTask(taskId, { progress: 60 });

      // 生成建议
      const suggestions = await this.generateSuggestions(contract, elements, risks);
      await mockDB.updateReviewTask(taskId, { progress: 80 });

      // 生成摘要
      const summary = this.generateSummary(elements, risks, suggestions);
      
      // 计算风险等级和分数
      const { riskLevel, riskScore } = this.calculateRiskLevel(risks);

      // 完成审查
      await mockDB.updateReviewTask(taskId, {
        status: 'completed',
        progress: 100,
        risk_level: riskLevel,
        risk_score: riskScore,
        result: {
          elements,
          risks,
          suggestions,
          summary
        },
        completed_at: new Date().toISOString()
      });

    } catch (error) {
      console.error('Perform review error:', error);
      await mockDB.updateReviewTask(taskId, {
        status: 'failed',
        error_message: '审查处理失败'
      });
    }
  }

  // 分析合同要素
  private async analyzeContractElements(contract: Contract): Promise<ContractElement[]> {
    const elements: ContractElement[] = [];
    
    // 模拟合同要素分析
    const commonElements = [
      { type: 'parties', name: '合同主体', required: true },
      { type: 'subject', name: '合同标的', required: true },
      { type: 'price', name: '价格条款', required: true },
      { type: 'payment', name: '付款方式', required: true },
      { type: 'delivery', name: '交付条款', required: false },
      { type: 'warranty', name: '保证条款', required: false },
      { type: 'liability', name: '违约责任', required: true },
      { type: 'termination', name: '终止条款', required: true },
      { type: 'dispute', name: '争议解决', required: true }
    ];

    for (const elementType of commonElements) {
      const found = this.findElementInContract(contract.content, elementType.type);
      
      elements.push({
        id: uuidv4(),
        contract_id: contract.id,
        element_type: elementType.type,
        element_name: elementType.name,
        element_value: found.content,
        content: found.content,
        location: found.location,
        is_present: found.isPresent,
        description: `${elementType.name}条款`,
        confidence_score: found.confidence,
        extracted_at: new Date().toISOString()
      });
    }

    return elements;
  }

  // 在合同中查找要素（模拟）
  private findElementInContract(content: string, elementType: string): {
    content: string;
    location: string;
    isPresent: boolean;
    isComplete: boolean;
    confidence: number;
  } {
    // 简化的要素识别逻辑
    const keywords: Record<string, string[]> = {
      'parties': ['甲方', '乙方', '当事人', '签约方'],
      'subject': ['标的', '服务内容', '商品', '项目'],
      'price': ['价格', '费用', '金额', '报酬'],
      'payment': ['付款', '支付', '结算'],
      'delivery': ['交付', '交货', '履行'],
      'warranty': ['保证', '担保', '质保'],
      'liability': ['违约', '责任', '赔偿'],
      'termination': ['终止', '解除', '期限'],
      'dispute': ['争议', '纠纷', '仲裁', '诉讼']
    };

    const elementKeywords = keywords[elementType] || [];
    let found = false;
    let foundContent = '';
    let location = '';

    for (const keyword of elementKeywords) {
      const index = content.indexOf(keyword);
      if (index !== -1) {
        found = true;
        const start = Math.max(0, index - 50);
        const end = Math.min(content.length, index + 100);
        foundContent = content.substring(start, end);
        location = `第${Math.floor(index / 100) + 1}段`;
        break;
      }
    }

    return {
      content: foundContent,
      location,
      isPresent: found,
      isComplete: found && foundContent.length > 20,
      confidence: found ? 0.8 : 0.1
    };
  }

  // 识别风险项
  private async identifyRisks(contract: Contract, elements: ContractElement[]): Promise<RiskItem[]> {
    const risks: RiskItem[] = [];
    const rules = await mockDB.getAllReviewRules();

    for (const rule of rules) {
      if (rule.is_active) {
        const riskFound = this.checkRiskRule(contract, elements, rule);
        if (riskFound) {
          risks.push({
            id: uuidv4(),
            contract_id: contract.id,
            review_task_id: 'temp-task-id',
            risk_type: rule.rule_type,
            risk_level: rule.severity as 'low' | 'medium' | 'high' | 'critical',
            description: rule.description,
            location: riskFound.location,
            suggestion: rule.suggestion || '建议咨询法务专家',
            auto_fixable: false,
            identified_at: new Date().toISOString(),
            created_at: new Date().toISOString()
          });
        }
      }
    }

    // 检查缺失的必要条款
    const missingElements = elements.filter(e => !e.is_present && this.isRequiredElement(e.element_type));
    for (const element of missingElements) {
      risks.push({
        id: uuidv4(),
        contract_id: contract.id,
        review_task_id: 'temp-task-id',
        risk_type: 'missing_clause',
        risk_level: 'high',
        description: `缺少必要条款：${element.element_type}`,
        location: '整个合同',
        suggestion: `建议添加${element.element_type}相关条款`,
        auto_fixable: false,
        identified_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      });
    }

    return risks;
  }

  // 检查风险规则
  private checkRiskRule(contract: Contract, elements: ContractElement[], rule: ReviewRule): { location: string } | null {
    // 简化的规则检查逻辑
    const content = contract.content.toLowerCase();
    
    if (rule.description.includes('liability')) {
      const hasLiability = elements.some(e => e.element_type === 'liability' && e.confidence_score > 0.5);
      if (!hasLiability) {
        return { location: '整个合同' };
      }
    }
    
    if (rule.description.includes('clarity')) {
      const hasTerms = content.includes('期限') || content.includes('有效期');
      if (!hasTerms) {
        return { location: '合同期限部分' };
      }
    }

    return null;
  }

  // 生成建议
  private async generateSuggestions(contract: Contract, elements: ContractElement[], risks: RiskItem[]): Promise<ReviewSuggestion[]> {
    const suggestions: ReviewSuggestion[] = [];

    // 基于风险生成建议
    for (const risk of risks) {
      suggestions.push({
        id: uuidv4(),
        type: this.mapRiskLevelToSuggestionType(risk.risk_level),
        category: risk.risk_type,
        title: `${risk.risk_type}风险`,
        description: risk.description,
        location: {
          text_snippet: risk.description
        },
        severity: risk.risk_level,
        recommendation: risk.suggestion,
        auto_fixable: risk.risk_level === 'low'
      });
    }

    // 基于要素完整性生成建议
    const incompleteElements = elements.filter(e => e.confidence_score > 0.5 && e.confidence_score < 0.8);
    for (const element of incompleteElements) {
      suggestions.push({
        id: uuidv4(),
        type: 'warning',
        category: 'completeness',
        title: `${element.element_type}不完整`,
        description: `${element.element_type}条款存在但内容不够完整`,
        location: {
          text_snippet: element.description || '未找到相关内容'
        },
        severity: 'medium',
        recommendation: `建议完善${element.element_type}的具体内容`,
        auto_fixable: false
      });
    }

    return suggestions;
  }

  // 生成审查摘要
  private generateSummary(elements: ContractElement[], risks: RiskItem[], suggestions: ReviewSuggestion[]): ReviewSummary {
    const criticalIssues = suggestions.filter(s => s.severity === 'critical').length;
    const highIssues = suggestions.filter(s => s.severity === 'high').length;
    const mediumIssues = suggestions.filter(s => s.severity === 'medium').length;
    const lowIssues = suggestions.filter(s => s.severity === 'low').length;

    const totalElements = elements.length;
    const presentElements = elements.filter(e => e.confidence_score > 0.5).length;
    const completeElements = elements.filter(e => e.confidence_score > 0.8).length;

    const complianceScore = Math.round((presentElements / totalElements) * 100);
    const completenessScore = Math.round((completeElements / totalElements) * 100);
    const readabilityScore = Math.round(Math.random() * 30 + 70); // 模拟可读性分数

    return {
      total_issues: suggestions.length,
      critical_issues: criticalIssues,
      high_issues: highIssues,
      medium_issues: mediumIssues,
      low_issues: lowIssues,
      compliance_score: complianceScore,
      readability_score: readabilityScore,
      completeness_score: completenessScore,
      key_findings: this.generateKeyFindings(risks, suggestions),
      missing_clauses: elements.filter(e => e.confidence_score < 0.5 && this.isRequiredElement(e.element_type)).map(e => e.element_type),
      recommended_actions: this.generateRecommendedActions(risks, suggestions)
    };
  }

  // 获取审查结果
  async getReviewResult(taskId: string): Promise<{ success: boolean; result?: ReviewResult; message?: string }> {
    try {
      const task = await mockDB.getReviewTaskById(taskId);
      if (!task) {
        return {
          success: false,
          message: '审查任务不存在'
        };
      }

      const result: ReviewResult = {
        task_id: task.id,
        contract_id: task.contract_id,
        status: task.status,
        progress: task.status === 'completed' ? 100 : task.status === 'processing' ? 50 : 0,
        risk_level: (task.analysis_result?.risk_level as any) || 'low',
        risk_score: (task.analysis_result?.risk_score as any) || 0,
        elements: task.analysis_result?.elements || [],
        risks: task.analysis_result?.risks || [],
        suggestions: task.analysis_result?.suggestions || [],
        summary: task.analysis_result?.summary || this.getDefaultSummary(),
        created_at: task.created_at,
        completed_at: task.completed_at
      };

      return {
        success: true,
        result
      };
    } catch (error) {
      console.error('Get review result error:', error);
      return {
        success: false,
        message: '获取审查结果失败'
      };
    }
  }

  // 条款对比
  async compareContract(request: ComparisonRequest): Promise<{ success: boolean; result?: ComparisonResult; message?: string }> {
    try {
      const contract = await mockDB.getContractById(request.contract_id);
      if (!contract) {
        return {
          success: false,
          message: '合同不存在'
        };
      }

      let template = null;
      if (request.template_id) {
        template = await mockDB.getTemplateById(request.template_id);
        if (!template) {
          return {
            success: false,
            message: '模板不存在'
          };
        }
      }

      // 执行对比分析（模拟）
      const result = await this.performComparison(contract, template, request);

      return {
        success: true,
        result
      };
    } catch (error) {
      console.error('Compare contract error:', error);
      return {
        success: false,
        message: '合同对比失败'
      };
    }
  }

  // 执行对比分析
  private async performComparison(contract: Contract, template: any, request: ComparisonRequest): Promise<ComparisonResult> {
    // 模拟对比逻辑
    const differences: ContractDifference[] = [];
    const missingClauses: string[] = [];
    const extraClauses: string[] = [];
    
    if (template) {
      // 与模板对比
      const templateClauses = this.extractClauses(template.content);
      const contractClauses = this.extractClauses(contract.content);
      
      // 查找缺失的条款
      for (const templateClause of templateClauses) {
        const found = contractClauses.find(c => c.type === templateClause.type);
        if (!found) {
          missingClauses.push(templateClause.name);
          differences.push({
            type: 'missing',
            clause_type: templateClause.type,
            template_text: templateClause.content,
            severity: 'high',
            description: `缺少${templateClause.name}条款`
          });
        } else if (found.content !== templateClause.content) {
          differences.push({
            type: 'different',
            clause_type: templateClause.type,
            contract_text: found.content,
            template_text: templateClause.content,
            severity: 'medium',
            description: `${templateClause.name}条款与模板不同`
          });
        }
      }
      
      // 查找额外的条款
      for (const contractClause of contractClauses) {
        const found = templateClauses.find(t => t.type === contractClause.type);
        if (!found) {
          extraClauses.push(contractClause.name);
          differences.push({
            type: 'extra',
            clause_type: contractClause.type,
            contract_text: contractClause.content,
            severity: 'low',
            description: `包含额外的${contractClause.name}条款`
          });
        }
      }
    }

    const similarityScore = this.calculateSimilarityScore(differences.length, template ? this.extractClauses(template.content).length : 10);

    return {
      contract_id: contract.id,
      template_id: template?.id,
      differences,
      similarity_score: similarityScore,
      missing_clauses: missingClauses,
      extra_clauses: extraClauses,
      recommendations: this.generateComparisonRecommendations(differences)
    };
  }

  // 辅助方法
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isRequiredElement(elementType: string): boolean {
    const requiredElements = ['parties', 'subject', 'price', 'payment', 'liability', 'termination', 'dispute'];
    return requiredElements.includes(elementType);
  }

  private mapRiskLevelToSuggestionType(riskLevel: string): 'warning' | 'error' | 'info' | 'suggestion' {
    switch (riskLevel) {
      case 'critical': return 'error';
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'suggestion';
    }
  }

  private calculateRiskLevel(risks: RiskItem[]): { riskLevel: 'low' | 'medium' | 'high' | 'critical'; riskScore: number } {
    if (risks.some(r => r.risk_level === 'critical')) {
      return { riskLevel: 'critical', riskScore: 90 + Math.random() * 10 };
    }
    if (risks.some(r => r.risk_level === 'high')) {
      return { riskLevel: 'high', riskScore: 70 + Math.random() * 20 };
    }
    if (risks.some(r => r.risk_level === 'medium')) {
      return { riskLevel: 'medium', riskScore: 40 + Math.random() * 30 };
    }
    return { riskLevel: 'low', riskScore: Math.random() * 40 };
  }

  private generateKeyFindings(risks: RiskItem[], suggestions: ReviewSuggestion[]): string[] {
    const findings: string[] = [];
    
    if (risks.length === 0) {
      findings.push('未发现明显风险项');
    } else {
      const criticalRisks = risks.filter(r => r.risk_level === 'critical');
      const highRisks = risks.filter(r => r.risk_level === 'high');
      
      if (criticalRisks.length > 0) {
        findings.push(`发现${criticalRisks.length}个严重风险项`);
      }
      if (highRisks.length > 0) {
        findings.push(`发现${highRisks.length}个高风险项`);
      }
    }
    
    return findings;
  }

  private generateRecommendedActions(risks: RiskItem[], suggestions: ReviewSuggestion[]): string[] {
    const actions: string[] = [];
    
    const criticalSuggestions = suggestions.filter(s => s.severity === 'critical');
    const highSuggestions = suggestions.filter(s => s.severity === 'high');
    
    if (criticalSuggestions.length > 0) {
      actions.push('立即处理严重问题');
    }
    if (highSuggestions.length > 0) {
      actions.push('优先处理高风险项');
    }
    if (suggestions.length > 5) {
      actions.push('建议进行全面的合同修订');
    }
    
    return actions;
  }

  private getDefaultSummary(): ReviewSummary {
    return {
      total_issues: 0,
      critical_issues: 0,
      high_issues: 0,
      medium_issues: 0,
      low_issues: 0,
      compliance_score: 0,
      readability_score: 0,
      completeness_score: 0,
      key_findings: [],
      missing_clauses: [],
      recommended_actions: []
    };
  }

  private extractClauses(content: string): Array<{ type: string; name: string; content: string }> {
    // 简化的条款提取逻辑
    return [
      { type: 'parties', name: '合同主体', content: '甲方乙方相关内容' },
      { type: 'subject', name: '合同标的', content: '标的物相关内容' },
      { type: 'payment', name: '付款条款', content: '付款方式相关内容' }
    ];
  }

  private calculateSimilarityScore(differences: number, totalClauses: number): number {
    if (totalClauses === 0) return 100;
    const similarity = Math.max(0, (totalClauses - differences) / totalClauses * 100);
    return Math.round(similarity);
  }

  private generateComparisonRecommendations(differences: ContractDifference[]): string[] {
    const recommendations: string[] = [];
    
    const missingCount = differences.filter(d => d.type === 'missing').length;
    const differentCount = differences.filter(d => d.type === 'different').length;
    
    if (missingCount > 0) {
      recommendations.push(`建议添加${missingCount}个缺失的条款`);
    }
    if (differentCount > 0) {
      recommendations.push(`建议审查${differentCount}个不同的条款`);
    }
    
    return recommendations;
  }
}

// 导出单例实例
export const reviewService = new ReviewService();
export default reviewService;