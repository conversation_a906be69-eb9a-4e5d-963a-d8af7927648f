/**
 * File Storage Service
 * 文件存储服务 - 集成Supabase Storage
 */
import { supabaseAdmin } from './supabaseClient.js';
import { v4 as uuidv4 } from 'uuid';

export interface FileUploadResult {
  success: boolean;
  filePath?: string;
  publicUrl?: string;
  error?: string;
}

export interface FileMetadata {
  originalName: string;
  mimeType: string;
  size: number;
  uploadedBy: string;
  contractId?: string;
}

export class FileStorageService {
  private readonly bucketName = 'contracts';

  /**
   * 上传文件到Supabase Storage
   */
  async uploadFile(
    file: Buffer,
    metadata: FileMetadata
  ): Promise<FileUploadResult> {
    try {
      // 生成唯一文件名
      const fileExtension = this.getFileExtension(metadata.originalName);
      const fileName = `${metadata.contractId || 'temp'}/${uuidv4()}${fileExtension}`;

      // 上传文件到Supabase Storage
      const { data, error } = await supabaseAdmin.storage
        .from(this.bucketName)
        .upload(fileName, file, {
          contentType: metadata.mimeType,
          metadata: {
            originalName: metadata.originalName,
            uploadedBy: metadata.uploadedBy,
            contractId: metadata.contractId || '',
            uploadedAt: new Date().toISOString()
          }
        });

      if (error) {
        console.error('Supabase storage upload error:', error);
        return {
          success: false,
          error: `文件上传失败: ${error.message}`
        };
      }

      // 获取公共访问URL
      const { data: urlData } = supabaseAdmin.storage
        .from(this.bucketName)
        .getPublicUrl(fileName);

      return {
        success: true,
        filePath: fileName,
        publicUrl: urlData.publicUrl
      };
    } catch (error) {
      console.error('File upload service error:', error);
      return {
        success: false,
        error: '文件上传服务异常'
      };
    }
  }

  /**
   * 下载文件
   */
  async downloadFile(filePath: string): Promise<{ success: boolean; data?: Buffer; error?: string }> {
    try {
      const { data, error } = await supabaseAdmin.storage
        .from(this.bucketName)
        .download(filePath);

      if (error) {
        return {
          success: false,
          error: `文件下载失败: ${error.message}`
        };
      }

      const buffer = Buffer.from(await data.arrayBuffer());
      return {
        success: true,
        data: buffer
      };
    } catch (error) {
      console.error('File download service error:', error);
      return {
        success: false,
        error: '文件下载服务异常'
      };
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(filePath: string): Promise<{ success: boolean; error?: string }> {
    try {
      const { error } = await supabaseAdmin.storage
        .from(this.bucketName)
        .remove([filePath]);

      if (error) {
        return {
          success: false,
          error: `文件删除失败: ${error.message}`
        };
      }

      return { success: true };
    } catch (error) {
      console.error('File delete service error:', error);
      return {
        success: false,
        error: '文件删除服务异常'
      };
    }
  }

  /**
   * 获取文件公共URL
   */
  getPublicUrl(filePath: string): string {
    console.log('🔍 [FileStorage] getPublicUrl 被调用');
    console.log('🔍 [FileStorage] 参数:', {
      filePath,
      bucketName: this.bucketName
    });
    
    const { data } = supabaseAdmin.storage
      .from(this.bucketName)
      .getPublicUrl(filePath);
    
    console.log('✅ [FileStorage] 生成的公共URL:', data.publicUrl);
    return data.publicUrl;
  }

  /**
   * 获取文件签名URL（用于私有访问）
   */
  async getSignedUrl(filePath: string, expiresIn: number = 3600): Promise<{ success: boolean; url?: string; error?: string }> {
    console.log('🔍 [FileStorage] getSignedUrl 被调用');
    console.log('🔍 [FileStorage] 参数:', {
      filePath,
      expiresIn,
      bucketName: this.bucketName
    });
    
    try {
      console.log('🔍 [FileStorage] 调用 Supabase createSignedUrl');
      const { data, error } = await supabaseAdmin.storage
        .from(this.bucketName)
        .createSignedUrl(filePath, expiresIn);

      console.log('🔍 [FileStorage] Supabase 响应:', {
        hasData: !!data,
        hasError: !!error,
        errorMessage: error?.message,
        signedUrl: data?.signedUrl
      });

      if (error) {
        console.error('❌ [FileStorage] Supabase 返回错误:', error);
        return {
          success: false,
          error: `获取签名URL失败: ${error.message}`
        };
      }

      console.log('✅ [FileStorage] 成功生成签名URL:', data.signedUrl);
      return {
        success: true,
        url: data.signedUrl
      };
    } catch (error) {
      console.error('❌ [FileStorage] 获取签名URL服务异常:', error);
      console.error('❌ [FileStorage] 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
      return {
        success: false,
        error: '获取签名URL服务异常'
      };
    }
  }

  /**
   * 列出文件夹中的文件
   */
  async listFiles(folderPath: string = ''): Promise<{ success: boolean; files?: any[]; error?: string }> {
    try {
      const { data, error } = await supabaseAdmin.storage
        .from(this.bucketName)
        .list(folderPath);

      if (error) {
        return {
          success: false,
          error: `获取文件列表失败: ${error.message}`
        };
      }

      return {
        success: true,
        files: data
      };
    } catch (error) {
      console.error('List files service error:', error);
      return {
        success: false,
        error: '获取文件列表服务异常'
      };
    }
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex !== -1 ? filename.substring(lastDotIndex) : '';
  }

  /**
   * 验证文件类型
   */
  validateFileType(mimeType: string): boolean {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
    
    return allowedTypes.includes(mimeType);
  }

  /**
   * 验证文件大小
   */
  validateFileSize(size: number, maxSize: number = 10 * 1024 * 1024): boolean {
    return size <= maxSize;
  }
}

// 导出单例实例
export const fileStorageService = new FileStorageService();