/**
 * Supabase Database Service
 * 使用Supabase作为数据库后端的数据访问服务
 */
import { supabaseAdminClient, Database } from './supabaseClient.js';
import { v4 as uuidv4 } from 'uuid';
import { dataIntegrityService, ContractIntegrityData } from './dataIntegrityService';

// 重新导出接口定义以保持兼容性
export interface User {
  id: string;
  email: string;
  password_hash?: string; // Supabase Auth处理密码，这里保留兼容性
  name: string;
  role: 'user' | 'legal_staff' | 'legal_manager' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface Contract {
  id: string;
  user_id: string;
  title: string;
  category: string;
  type?: string; // 映射到category字段，可选
  file_path: string;
  file_url?: string;
  content?: string;
  ocr_content?: string;
  status: 'draft' | 'reviewing' | 'approved' | 'rejected' | 'signed' | 'expired' | 'uploaded' | 'processing' | 'reviewed' | 'archived';
  counterparty?: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  created_at: string;
  updated_at: string;
  // 前端期望的字段映射
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  riskLevel?: 'low' | 'medium' | 'high';
  startDate?: string;
  endDate?: string;
}

export interface ReviewTask {
  id: string;
  contract_id: string;
  user_id: string;
  reviewer_id?: string;
  review_type: string;
  focus_areas?: string[];
  custom_rules?: any[];
  analysis_result?: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  risk_level?: 'low' | 'medium' | 'high' | 'critical' | 'unknown';
  risk_score?: number;
  reviewer_name?: string;
  created_at: string;
  completed_at?: string;
}

export interface ContractElement {
  id: string;
  contract_id: string;
  element_type: string;
  element_value: string;
  confidence_score: number;
  extracted_at: string;
}

export interface RiskItem {
  id: string;
  review_task_id: string;
  risk_type: string;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  suggestion?: string;
  identified_at: string;
}

export interface Template {
  id: string;
  name: string;
  category: string;
  content: string;
  parameters?: any;
  is_active: boolean;
  created_at: string;
}

export interface KnowledgeBase {
  id: string;
  type: 'regulation' | 'case_law' | 'template' | 'guideline';
  title: string;
  content: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface ReviewRule {
  id: string;
  knowledge_base_id: string;
  rule_name: string;
  rule_content: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  is_active: boolean;
}

class SupabaseService {
  private supabase = supabaseAdminClient;

  // 用户相关操作
  async getUsers(): Promise<User[]> {
    const { data, error } = await this.supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  async getUserById(id: string): Promise<User | null> {
    const { data, error } = await this.supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // No rows returned
      throw error;
    }
    return data;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    const { data, error } = await this.supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // No rows returned
      throw error;
    }
    return data;
  }

  async createUser(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<User> {
    const now = new Date().toISOString();
    const user = {
      id: uuidv4(),
      ...userData,
      created_at: now,
      updated_at: now
    };

    const { data, error } = await this.supabase
      .from('users')
      .insert(user)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  // 合同相关操作
  async getContracts(options: {
    userId?: string;
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    type?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<{ contracts: Contract[]; total: number }> {
    try {
      const { 
        userId, 
        page = 1, 
        limit = 10, 
        search, 
        status, 
        type, 
        startDate, 
        endDate 
      } = options;

      // 构建查询
      let query = this.supabase
        .from('contracts')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false });

      // 用户过滤
      if (userId) {
        query = query.eq('user_id', userId);
      }

      // 搜索过滤 - 使用全文搜索索引优化
      if (search) {
        // 使用PostgreSQL全文搜索功能，利用GIN索引
        const searchTerm = search.replace(/[^\w\s]/g, '').trim();
        if (searchTerm) {
          query = query.or(
            `title.ilike.%${search}%,` +
            `content.ilike.%${search}%,` +
            `ocr_content.ilike.%${search}%,` +
            `category.ilike.%${search}%,` +
            `counterparty.ilike.%${search}%`
          );
        }
      }

      // 状态过滤
      if (status) {
        query = query.eq('status', status);
      }

      // 类型过滤
      if (type) {
        query = query.eq('category', type);
      }

      // 日期范围过滤 - 使用合同的实际开始和结束日期
      if (startDate) {
        query = query.gte('start_date', startDate);
      }
      if (endDate) {
        query = query.lte('end_date', endDate);
      }

      // 分页
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching contracts:', error);
        throw error;
      }

      // 映射数据库字段到前端期望的字段格式
      const mappedContracts = (data || []).map(contract => ({
        ...contract,
        type: contract.category, // 映射category到type
        createdAt: contract.created_at,
        updatedAt: contract.updated_at,
        createdBy: contract.user_id,
        riskLevel: contract.risk_level,
        startDate: contract.start_date,
        endDate: contract.end_date
      }));

      return {
        contracts: mappedContracts,
        total: count || 0
      };
    } catch (error) {
      console.error('Error in getContracts:', error);
      throw error;
    }
  }

  async getAllContracts(options: {
    search?: string;
    status?: string;
    type?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<Contract[]> {
    try {
      const { search, status, type, startDate, endDate } = options;

      let query = this.supabase
        .from('contracts')
        .select('*')
        .order('created_at', { ascending: false });

      // 搜索过滤 - 使用全文搜索索引优化
      if (search) {
        // 使用PostgreSQL全文搜索功能，利用GIN索引
        const searchTerm = search.replace(/[^\w\s]/g, '').trim();
        if (searchTerm) {
          query = query.or(
            `title.ilike.%${search}%,` +
            `content.ilike.%${search}%,` +
            `ocr_content.ilike.%${search}%,` +
            `category.ilike.%${search}%,` +
            `counterparty.ilike.%${search}%`
          );
        }
      }

      // 状态过滤
      if (status) {
        query = query.eq('status', status);
      }

      // 类型过滤
      if (type) {
        query = query.eq('category', type);
      }

      // 日期范围过滤 - 使用合同的实际开始和结束日期
      if (startDate) {
        query = query.gte('start_date', startDate);
      }
      if (endDate) {
        query = query.lte('end_date', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching all contracts:', error);
        throw error;
      }

      // 映射数据库字段到前端期望的字段格式
      const mappedContracts = (data || []).map(contract => ({
        ...contract,
        type: contract.category, // 映射category到type
        createdAt: contract.created_at,
        updatedAt: contract.updated_at,
        createdBy: contract.user_id,
        riskLevel: contract.risk_level,
        startDate: contract.start_date,
        endDate: contract.end_date
      }));

      return mappedContracts;
    } catch (error) {
      console.error('Error in getAllContracts:', error);
      throw error;
    }
  }

  async getContractById(id: string): Promise<Contract | null> {
    const { data, error } = await this.supabase
      .from('contracts')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    
    if (!data) return null;
    
    // 映射数据库字段到前端期望的字段格式
    return {
      ...data,
      type: data.category, // 映射category到type
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      createdBy: data.user_id,
      riskLevel: data.risk_level,
      startDate: data.start_date,
      endDate: data.end_date
    };
  }

  async createContract(contractData: Omit<Contract, 'id' | 'created_at' | 'updated_at'>): Promise<Contract> {
    console.log('🔄 [数据库调试] === createContract开始 ===');
    console.log('🔄 [数据库调试] 接收到的合同数据:', {
      user_id: contractData.user_id,
      title: contractData.title,
      category: contractData.category,
      counterparty: contractData.counterparty,
      amount: contractData.amount,
      start_date: contractData.start_date,
      end_date: contractData.end_date,
      risk_level: contractData.risk_level,
      status: contractData.status
    });
    
    // 数据完整性检查
    const integrityCheck = await dataIntegrityService.checkContractIntegrity(contractData as ContractIntegrityData);
    
    if (!integrityCheck.isValid) {
      throw new Error(`数据完整性检查失败: ${integrityCheck.errors.join(', ')}`);
    }
    
    // 记录警告信息
    if (integrityCheck.warnings.length > 0) {
      console.warn('合同创建警告:', integrityCheck.warnings);
    }

    const now = new Date().toISOString();
    
    // 直接使用snake_case字段，与数据库字段一致
    const contract = {
      id: uuidv4(),
      title: contractData.title,
      content: contractData.content,
      category: contractData.category,
      file_path: contractData.file_path,
      file_url: contractData.file_url,
      ocr_content: contractData.ocr_content,
      status: contractData.status || 'draft',
      counterparty: contractData.counterparty,
      amount: contractData.amount,
      start_date: contractData.start_date,
      end_date: contractData.end_date,
      risk_level: contractData.risk_level || 'medium',
      user_id: contractData.user_id,
      created_at: now,
      updated_at: now
    };
    
    console.log('🔄 [数据库调试] 最终数据库插入数据:', {
      id: contract.id,
      user_id: contract.user_id,
      title: contract.title,
      category: contract.category,
      counterparty: contract.counterparty,
      amount: contract.amount,
      start_date: contract.start_date,
      end_date: contract.end_date,
      risk_level: contract.risk_level,
      status: contract.status
    });

    const { data, error } = await this.supabase
      .from('contracts')
      .insert(contract)
      .select()
      .single();
    
    if (error) {
      console.error('💥 [数据库调试] 插入失败:', error);
      throw error;
    }
    
    console.log('✅ [数据库调试] 插入成功，返回数据:', {
      id: data.id,
      user_id: data.user_id,
      title: data.title,
      category: data.category,
      counterparty: data.counterparty,
      amount: data.amount,
      start_date: data.start_date,
      end_date: data.end_date,
      risk_level: data.risk_level,
      status: data.status,
      created_at: data.created_at
    });
    
    // 映射数据库字段到前端期望的字段格式
    const mappedResult = {
      ...data,
      type: data.category, // 映射category到type
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      createdBy: data.user_id,
      riskLevel: data.risk_level,
      startDate: data.start_date,
      endDate: data.end_date
    };
    
    console.log('🔄 [数据库调试] 字段映射后的结果:', {
      id: mappedResult.id,
      createdBy: mappedResult.createdBy,
      title: mappedResult.title,
      type: mappedResult.type,
      counterparty: mappedResult.counterparty,
      amount: mappedResult.amount,
      startDate: mappedResult.startDate,
      endDate: mappedResult.endDate,
      riskLevel: mappedResult.riskLevel,
      status: mappedResult.status
    });
    
    console.log('🏁 [数据库调试] === createContract结束 ===');
    
    return mappedResult;
  }

  async updateContract(id: string, updates: Partial<Contract>): Promise<Contract | null> {
    // 获取现有合同数据
    const existingContract = await this.getContractById(id);
    if (!existingContract) {
      throw new Error('合同不存在');
    }
    
    // 合并数据进行完整性检查
    const mergedData = { ...existingContract, ...updates, id } as ContractIntegrityData;
    
    const integrityCheck = await dataIntegrityService.checkContractIntegrity(mergedData, true);
    
    if (!integrityCheck.isValid) {
      throw new Error(`数据完整性检查失败: ${integrityCheck.errors.join(', ')}`);
    }
    
    // 记录警告信息
    if (integrityCheck.warnings.length > 0) {
      console.warn('合同更新警告:', integrityCheck.warnings);
    }

    // 映射前端字段到数据库字段
    const { type, createdBy, riskLevel, startDate, endDate, ...otherUpdates } = updates;
    const dbUpdates: any = {
      ...otherUpdates,
      updated_at: new Date().toISOString()
    };
    
    // 只有当字段存在时才进行映射
    if (type !== undefined) dbUpdates.category = type;
    if (createdBy !== undefined) dbUpdates.user_id = createdBy;
    if (riskLevel !== undefined) dbUpdates.risk_level = riskLevel;
    if (startDate !== undefined) dbUpdates.start_date = startDate;
    if (endDate !== undefined) dbUpdates.end_date = endDate;

    const { data, error } = await this.supabase
      .from('contracts')
      .update(dbUpdates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    
    // 映射数据库字段到前端期望的字段格式
    return {
      ...data,
      type: data.category, // 映射category到type
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      createdBy: data.user_id,
      riskLevel: data.risk_level,
      startDate: data.start_date,
      endDate: data.end_date
    };
  }

  async deleteContract(id: string): Promise<void> {
    // 检查合同是否存在
    const existingContract = await this.getContractById(id);
    if (!existingContract) {
      throw new Error('合同不存在');
    }
    
    // 检查是否可以删除（业务规则检查）
    if (existingContract.status === 'reviewed') {
      throw new Error('已审查的合同不能删除');
    }
    
    if (existingContract.status === 'archived') {
      throw new Error('已归档的合同不能删除');
    }

    const { error } = await this.supabase
      .from('contracts')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }

  // 审查任务相关操作
  async getReviewTasks(userId?: string): Promise<ReviewTask[]> {
    let query = this.supabase
      .from('review_tasks')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (userId) {
      query = query.eq('user_id', userId);
    }
    
    const { data, error } = await query;
    if (error) throw error;
    return data || [];
  }

  async getReviewTaskById(id: string): Promise<ReviewTask | null> {
    const { data, error } = await this.supabase
      .from('review_tasks')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  async createReviewTask(taskData: Omit<ReviewTask, 'id' | 'created_at'>): Promise<ReviewTask> {
    const task = {
      id: uuidv4(),
      ...taskData,
      created_at: new Date().toISOString()
    };

    const { data, error } = await this.supabase
      .from('review_tasks')
      .insert(task)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }

  async updateReviewTask(id: string, updates: Partial<ReviewTask>): Promise<ReviewTask | null> {
    const { data, error } = await this.supabase
      .from('review_tasks')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  async getReviewTasksByContractId(contractId: string): Promise<ReviewTask[]> {
    const { data, error } = await this.supabase
      .from('review_tasks')
      .select('*')
      .eq('contract_id', contractId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  // 模板相关操作
  async getTemplates(): Promise<Template[]> {
    const { data, error } = await this.supabase
      .from('templates')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  async getTemplateById(id: string): Promise<Template | null> {
    const { data, error } = await this.supabase
      .from('templates')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }
    return data;
  }

  // 知识库相关操作
  async getKnowledgeBase(): Promise<KnowledgeBase[]> {
    const { data, error } = await this.supabase
      .from('knowledge_base')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }

  // 审查规则相关操作
  async getReviewRules(): Promise<ReviewRule[]> {
    const { data, error } = await this.supabase
      .from('review_rules')
      .select('*')
      .eq('is_active', true);
    
    if (error) throw error;
    return data || [];
  }

  async getAllReviewRules(): Promise<ReviewRule[]> {
    return this.getReviewRules();
  }

  // 合同要素相关操作
  async getContractElements(contractId: string): Promise<any[]> {
    // 暂时返回模拟数据，保持兼容性
    // TODO: 实现真实的合同要素提取逻辑
    return [
      { type: 'party', content: '甲方：示例公司', confidence: 0.95 },
      { type: 'amount', content: '合同金额：100万元', confidence: 0.90 },
      { type: 'term', content: '合同期限：1年', confidence: 0.85 }
    ];
  }

  // 风险项相关操作
  async getRiskItems(contractId: string): Promise<any[]> {
    // 暂时返回模拟数据，保持兼容性
    // TODO: 实现真实的风险识别逻辑
    return [
      { type: 'legal', description: '违约责任条款不够明确', severity: 'medium', suggestion: '建议明确违约责任的具体承担方式' },
      { type: 'commercial', description: '付款条件对己方不利', severity: 'high', suggestion: '建议调整付款周期' }
    ];
  }

  // 统计数据
  async getStatistics(userId?: string): Promise<any> {
    try {
      // 获取合同统计
      let contractQuery = this.supabase
        .from('contracts')
        .select('id, category, status');
      
      if (userId) {
        contractQuery = contractQuery.eq('user_id', userId);
      }
      
      const { data: contracts, error: contractError } = await contractQuery;
      if (contractError) throw contractError;

      // 获取审查任务统计
      let reviewQuery = this.supabase
        .from('review_tasks')
        .select('id, status');
      
      if (userId) {
        reviewQuery = reviewQuery.eq('user_id', userId);
      }
      
      const { data: reviewTasks, error: reviewError } = await reviewQuery;
      if (reviewError) throw reviewError;

      // 获取风险项统计
      const { data: riskItems, error: riskError } = await this.supabase
        .from('risk_items')
        .select('risk_level');
      
      if (riskError) throw riskError;

      return {
        totalContracts: contracts?.length || 0,
        pendingReviews: reviewTasks?.filter(task => task.status === 'pending').length || 0,
        completedReviews: reviewTasks?.filter(task => task.status === 'completed').length || 0,
        contractsByCategory: this.groupBy(contracts || [], 'category'),
        contractsByStatus: this.groupBy(contracts || [], 'status'),
        riskDistribution: this.groupBy(riskItems || [], 'risk_level')
      };
    } catch (error) {
      console.error('Error getting statistics:', error);
      // 返回默认统计数据
      return {
        totalContracts: 0,
        pendingReviews: 0,
        completedReviews: 0,
        contractsByCategory: {},
        contractsByStatus: {},
        riskDistribution: {}
      };
    }
  }

  // 辅助方法
  private groupBy(array: any[], key: string): Record<string, number> {
    return array.reduce((acc, item) => {
      const value = item[key] || 'unknown';
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {});
  }

  // 文件存在性检查
  async checkFileExists(filePath: string): Promise<boolean> {
    try {
      const { data, error } = await this.supabase.storage
        .from('contracts')
        .list('', {
          search: filePath
        });
      
      return !error && data && data.length > 0;
    } catch (error) {
      console.error('检查文件存在性失败:', error);
      return false;
    }
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      const { data, error } = await this.supabase
        .from('users')
        .select('count')
        .limit(1);
      
      return !error;
    } catch {
      return false;
    }
  }
}

// 导出单例实例
export const supabaseService = new SupabaseService();
export default supabaseService;