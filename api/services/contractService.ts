/**
 * Contract Service
 * 合同管理服务，处理合同的增删改查和相关业务逻辑
 */
import { mockDB, Contract, ContractElement, RiskItem } from './mockDatabase.js';
import { v4 as uuidv4 } from 'uuid';

export interface CreateContractRequest {
  title: string;
  type: string;
  content?: string;
  file_path?: string;
  file_name?: string;
  file_size?: number;
  template_id?: string;
  description?: string;
}

export interface UpdateContractRequest {
  title?: string;
  type?: string;
  content?: string;
  description?: string;
  status?: 'uploaded' | 'processing' | 'reviewed' | 'archived';
}

export interface ContractListQuery {
  page?: number;
  limit?: number;
  status?: string;
  type?: string;
  search?: string;
  created_by?: string;
  start_date?: string;
  end_date?: string;
}

export interface ContractResponse {
  success: boolean;
  contract?: Contract;
  contracts?: Contract[];
  total?: number;
  page?: number;
  limit?: number;
  message?: string;
}

export interface ContractStats {
  total: number;
  uploaded: number;
  processing: number;
  reviewed: number;
  archived: number;
  by_type: Record<string, number>;
  recent_activity: Array<{
    id: string;
    title: string;
    action: string;
    timestamp: string;
  }>;
}

class ContractService {
  // 创建合同
  async createContract(userId: string, data: CreateContractRequest): Promise<ContractResponse> {
    try {
      // 验证必填字段
      if (!data.title || !data.type) {
        return {
          success: false,
          message: '合同标题和类型为必填项'
        };
      }

      // 验证合同类型
      const validTypes = ['service', 'purchase', 'employment', 'lease', 'partnership', 'nda', 'other'];
      if (!validTypes.includes(data.type)) {
        return {
          success: false,
          message: '无效的合同类型'
        };
      }

      const contract = await mockDB.createContract({
        user_id: userId,
        title: data.title,
        category: data.type || 'general',
        file_path: data.file_path || '',
        content: data.content || '',
        status: 'uploaded'
      });

      return {
        success: true,
        contract
      };
    } catch (error) {
      console.error('Create contract error:', error);
      return {
        success: false,
        message: '创建合同失败'
      };
    }
  }

  // 获取合同列表
  async getContracts(query: ContractListQuery = {}): Promise<ContractResponse> {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        type,
        search,
        created_by,
        start_date,
        end_date
      } = query;

      let contracts = await mockDB.getAllContracts();

      // 应用过滤条件
      if (status) {
        contracts = contracts.filter(c => c.status === status);
      }

      if (type) {
        contracts = contracts.filter(c => c.category === type);
      }

      if (created_by) {
        contracts = contracts.filter(c => c.user_id === created_by);
      }

      // 搜索过滤 - 仅搜索合同标题
      if (search) {
        const searchLower = search.toLowerCase();
        contracts = contracts.filter(c =>
          c.title.toLowerCase().includes(searchLower)
        );
      }

      if (start_date) {
        contracts = contracts.filter(c => new Date(c.created_at) >= new Date(start_date));
      }

      if (end_date) {
        contracts = contracts.filter(c => new Date(c.created_at) <= new Date(end_date));
      }

      // 排序（最新的在前）
      contracts.sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());

      // 分页
      const total = contracts.length;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedContracts = contracts.slice(startIndex, endIndex);

      return {
        success: true,
        contracts: paginatedContracts,
        total,
        page,
        limit
      };
    } catch (error) {
      console.error('Get contracts error:', error);
      return {
        success: false,
        message: '获取合同列表失败'
      };
    }
  }

  // 获取单个合同详情
  async getContractById(contractId: string): Promise<ContractResponse> {
    try {
      const contract = await mockDB.getContractById(contractId);
      
      if (!contract) {
        return {
          success: false,
          message: '合同不存在'
        };
      }

      return {
        success: true,
        contract
      };
    } catch (error) {
      console.error('Get contract by id error:', error);
      return {
        success: false,
        message: '获取合同详情失败'
      };
    }
  }

  // 更新合同
  async updateContract(contractId: string, userId: string, data: UpdateContractRequest): Promise<ContractResponse> {
    try {
      const existingContract = await mockDB.getContractById(contractId);
      
      if (!existingContract) {
        return {
          success: false,
          message: '合同不存在'
        };
      }

      // 检查权限（只有创建者或管理员可以修改）
      if (existingContract.user_id !== userId) {
        // 这里应该检查用户角色，暂时简化处理
        // 在实际应用中需要传入用户角色信息
      }

      const updatedContract = await mockDB.updateContract(contractId, {
        ...data,
        updated_at: new Date().toISOString()
      });

      return {
        success: true,
        contract: updatedContract
      };
    } catch (error) {
      console.error('Update contract error:', error);
      return {
        success: false,
        message: '更新合同失败'
      };
    }
  }

  // 删除合同
  async deleteContract(contractId: string, userId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const existingContract = await mockDB.getContractById(contractId);
      
      if (!existingContract) {
        return {
          success: false,
          message: '合同不存在'
        };
      }

      // 检查权限
      if (existingContract.user_id !== userId) {
        return {
          success: false,
          message: '无权限删除此合同'
        };
      }

      // 检查合同状态（已审查的合同不能删除，只能归档）
      if (existingContract.status === 'reviewed') {
        return {
          success: false,
          message: '已审查的合同不能删除，请使用归档功能'
        };
      }

      await mockDB.deleteContract(contractId);

      return {
        success: true,
        message: '合同删除成功'
      };
    } catch (error) {
      console.error('Delete contract error:', error);
      return {
        success: false,
        message: '删除合同失败'
      };
    }
  }

  // 获取合同统计信息
  async getContractStats(userId?: string): Promise<{ success: boolean; stats?: ContractStats; message?: string }> {
    try {
      let contracts = await mockDB.getAllContracts();
      
      // 如果指定了用户ID，只统计该用户的合同
      if (userId) {
        contracts = contracts.filter(c => c.user_id === userId);
      }

      const stats: ContractStats = {
        total: contracts.length,
        uploaded: contracts.filter(c => c.status === 'uploaded').length,
        processing: contracts.filter(c => c.status === 'processing').length,
        reviewed: contracts.filter(c => c.status === 'reviewed').length,
         archived: contracts.filter(c => c.status === 'archived').length,
        by_type: {},
        recent_activity: []
      };

      // 按类型统计
      contracts.forEach(contract => {
        stats.by_type[contract.category] = (stats.by_type[contract.category] || 0) + 1;
      });

      // 最近活动（最近10个更新的合同）
      const recentContracts = contracts
        .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
        .slice(0, 10);

      stats.recent_activity = recentContracts.map(contract => ({
        id: contract.id,
        title: contract.title,
        action: this.getActionByStatus(contract.status),
        timestamp: contract.updated_at
      }));

      return {
        success: true,
        stats
      };
    } catch (error) {
      console.error('Get contract stats error:', error);
      return {
        success: false,
        message: '获取统计信息失败'
      };
    }
  }

  // 获取合同要素
  async getContractElements(contractId: string): Promise<{ success: boolean; elements?: ContractElement[]; message?: string }> {
    try {
      const elements = await mockDB.getContractElements(contractId);
      
      return {
        success: true,
        elements
      };
    } catch (error) {
      console.error('Get contract elements error:', error);
      return {
        success: false,
        message: '获取合同要素失败'
      };
    }
  }

  // 获取风险项
  async getRiskItems(contractId: string): Promise<{ success: boolean; risks?: RiskItem[]; message?: string }> {
    try {
      const risks = await mockDB.getRiskItems(contractId);
      
      return {
        success: true,
        risks
      };
    } catch (error) {
      console.error('Get risk items error:', error);
      return {
        success: false,
        message: '获取风险项失败'
      };
    }
  }

  // 批量操作
  async batchUpdateStatus(contractIds: string[], status: string, userId: string): Promise<{ success: boolean; message?: string }> {
    try {
      const validStatuses = ['uploaded', 'processing', 'reviewed', 'archived'];
      if (!validStatuses.includes(status)) {
        return {
          success: false,
          message: '无效的状态'
        };
      }

      for (const contractId of contractIds) {
        await mockDB.updateContract(contractId, {
          status: status as any,
          updated_at: new Date().toISOString()
        });
      }

      return {
        success: true,
        message: `成功更新 ${contractIds.length} 个合同的状态`
      };
    } catch (error) {
      console.error('Batch update status error:', error);
      return {
        success: false,
        message: '批量更新失败'
      };
    }
  }

  // 根据状态获取操作描述
  private getActionByStatus(status: string): string {
    const actionMap: Record<string, string> = {
      'uploaded': '上传合同',
      'processing': '正在处理',
      'reviewed': '审查完成',
      'archived': '已归档'
    };
    return actionMap[status] || '未知操作';
  }

  // 复制合同
  async duplicateContract(contractId: string, userId: string): Promise<ContractResponse> {
    try {
      const originalContract = await mockDB.getContractById(contractId);
      
      if (!originalContract) {
        return {
          success: false,
          message: '原合同不存在'
        };
      }

      const duplicatedContract = await mockDB.createContract({
        title: `${originalContract.title} (副本)`,
        user_id: userId,
        category: originalContract.category,
        content: originalContract.content,
        file_path: originalContract.file_path,
        status: 'uploaded'
      });

      return {
        success: true,
        contract: duplicatedContract
      };
    } catch (error) {
      console.error('Duplicate contract error:', error);
      return {
        success: false,
        message: '复制合同失败'
      };
    }
  }
}

// 导出单例实例
export const contractService = new ContractService();
export default contractService;