/**
 * 合同文档解析服务
 * 
 * 负责解析Word和PDF文档，提取文本内容、格式信息和文档结构
 * 专为合同对比模块设计
 */

import mammoth from 'mammoth';
import { v4 as uuidv4 } from 'uuid';
import type {
  ParsedDocument,
  DocumentContent,
  DocumentMetadata,
  DocumentStructure,
  ParagraphInfo,
  TableInfo,
  HeaderInfo,
  ListInfo,
  FormattingInfo,
  TextPosition,
  ContractComparisonError,
  ContractComparisonErrorType
} from '../../src/types/contractComparison';

/**
 * 合同文档解析器类
 */
export class ContractDocumentParser {
  
  /**
   * 解析文档（主入口方法）
   * @param file 上传的文件
   * @returns 解析后的文档对象
   */
  async parseDocument(file: Express.Multer.File): Promise<ParsedDocument> {
    console.log(`🔍 [文档解析] 开始解析文档: ${file.originalname}`);
    
    try {
      // 检测文件类型
      const fileType = this.detectFileType(file);
      console.log(`📄 [文档解析] 检测到文件类型: ${fileType}`);
      
      // 根据文件类型选择解析方法
      let content: DocumentContent;
      switch (fileType) {
        case 'docx':
        case 'doc':
          content = await this.parseWordDocument(file.buffer);
          break;
        case 'pdf':
          content = await this.parsePdfDocument(file.buffer);
          break;
        default:
          throw this.createError(
            ContractComparisonErrorType.INVALID_FILE_FORMAT,
            `不支持的文件格式: ${fileType}`,
            file.originalname
          );
      }
      
      // 创建文档元数据
      const metadata = this.createDocumentMetadata(file, content);
      
      // 构建解析结果
      const parsedDocument: ParsedDocument = {
        id: uuidv4(),
        originalName: file.originalname,
        content,
        metadata,
        parseTime: new Date()
      };
      
      console.log(`✅ [文档解析] 文档解析完成: ${file.originalname}`, {
        paragraphs: content.structure.paragraphs.length,
        tables: content.structure.tables.length,
        headers: content.structure.headers.length,
        characterCount: content.plainText.length
      });
      
      return parsedDocument;
      
    } catch (error) {
      console.error(`❌ [文档解析] 解析失败: ${file.originalname}`, error);
      
      if (error instanceof Error && 'type' in error) {
        throw error; // 重新抛出已知的错误
      }
      
      throw this.createError(
        ContractComparisonErrorType.PARSE_ERROR,
        `文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
        file.originalname
      );
    }
  }
  
  /**
   * 解析Word文档
   * @param buffer 文件缓冲区
   * @returns 文档内容
   */
  private async parseWordDocument(buffer: Buffer): Promise<DocumentContent> {
    console.log('📝 [Word解析] 开始解析Word文档...');
    
    try {
      // 使用mammoth解析Word文档
      const result = await mammoth.convertToHtml(buffer, {
        convertImage: mammoth.images.imgElement((image) => {
          return image.read("base64").then((imageBuffer) => {
            return {
              src: `data:${image.contentType};base64,${imageBuffer}`
            };
          });
        })
      });
      
      const html = result.value;
      const plainText = this.htmlToPlainText(html);
      
      // 提取文档结构
      const structure = this.extractDocumentStructure(html, plainText);
      
      // 提取格式信息
      const formatting = this.extractFormattingInfo(html);
      
      console.log('✅ [Word解析] Word文档解析完成');
      
      return {
        html,
        plainText,
        structure,
        formatting
      };
      
    } catch (error) {
      console.error('❌ [Word解析] Word文档解析失败:', error);
      throw this.createError(
        ContractComparisonErrorType.PARSE_ERROR,
        `Word文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`
      );
    }
  }
  
  /**
   * 解析PDF文档
   * @param buffer 文件缓冲区
   * @returns 文档内容
   */
  private async parsePdfDocument(buffer: Buffer): Promise<DocumentContent> {
    console.log('📄 [PDF解析] 开始解析PDF文档...');

    try {
      // 动态导入pdf-parse以避免初始化错误
      const pdfParse = (await import('pdf-parse')).default;

      // 使用pdf-parse解析PDF文档
      const data = await pdfParse(buffer);
      
      const plainText = data.text;
      const html = this.plainTextToHtml(plainText);
      
      // 提取文档结构
      const structure = this.extractDocumentStructure(html, plainText);
      
      // PDF格式信息相对简单
      const formatting: FormattingInfo[] = [];
      
      console.log('✅ [PDF解析] PDF文档解析完成', {
        pages: data.numpages,
        textLength: plainText.length
      });
      
      return {
        html,
        plainText,
        structure,
        formatting
      };
      
    } catch (error) {
      console.error('❌ [PDF解析] PDF文档解析失败:', error);
      throw this.createError(
        ContractComparisonErrorType.PARSE_ERROR,
        `PDF文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`
      );
    }
  }
  
  /**
   * 检测文件类型
   * @param file 文件对象
   * @returns 文件类型
   */
  private detectFileType(file: Express.Multer.File): string {
    const fileName = file.originalname.toLowerCase();
    const mimeType = file.mimetype.toLowerCase();
    
    // 根据文件扩展名判断
    if (fileName.endsWith('.docx')) return 'docx';
    if (fileName.endsWith('.doc')) return 'doc';
    if (fileName.endsWith('.pdf')) return 'pdf';
    
    // 根据MIME类型判断
    if (mimeType.includes('wordprocessingml')) return 'docx';
    if (mimeType.includes('msword')) return 'doc';
    if (mimeType.includes('pdf')) return 'pdf';
    
    // 默认返回未知类型
    return 'unknown';
  }
  
  /**
   * 提取文档结构
   * @param html HTML内容
   * @param plainText 纯文本内容
   * @returns 文档结构
   */
  private extractDocumentStructure(html: string, plainText: string): DocumentStructure {
    const paragraphs = this.extractParagraphs(html, plainText);
    const tables = this.extractTables(html);
    const headers = this.extractHeaders(html);
    const lists = this.extractLists(html);
    
    return {
      paragraphs,
      tables,
      headers,
      lists
    };
  }
  
  /**
   * 提取段落信息
   * @param html HTML内容
   * @param plainText 纯文本内容
   * @returns 段落信息列表
   */
  private extractParagraphs(html: string, plainText: string): ParagraphInfo[] {
    const paragraphs: ParagraphInfo[] = [];
    
    // 使用正则表达式匹配段落
    const paragraphRegex = /<p[^>]*>(.*?)<\/p>/gi;
    let match;
    let paragraphIndex = 0;
    let currentIndex = 0;
    
    while ((match = paragraphRegex.exec(html)) !== null) {
      const content = this.htmlToPlainText(match[1]);
      if (content.trim()) {
        const startIndex = plainText.indexOf(content, currentIndex);
        const endIndex = startIndex + content.length;
        
        paragraphs.push({
          id: uuidv4(),
          content,
          position: {
            startIndex,
            endIndex,
            lineNumber: paragraphIndex + 1,
            columnNumber: 0,
            paragraphIndex
          },
          type: 'normal'
        });
        
        currentIndex = endIndex;
        paragraphIndex++;
      }
    }
    
    return paragraphs;
  }
  
  /**
   * 提取表格信息
   * @param html HTML内容
   * @returns 表格信息列表
   */
  private extractTables(html: string): TableInfo[] {
    const tables: TableInfo[] = [];
    
    // 使用正则表达式匹配表格
    const tableRegex = /<table[^>]*>(.*?)<\/table>/gi;
    let match;
    let tableIndex = 0;
    
    while ((match = tableRegex.exec(html)) !== null) {
      const tableHtml = match[1];
      const rows = this.extractTableRows(tableHtml);
      
      if (rows.length > 0) {
        tables.push({
          id: uuidv4(),
          rows: rows.length,
          columns: rows[0]?.length || 0,
          data: rows,
          position: {
            startIndex: 0,
            endIndex: 0,
            lineNumber: tableIndex + 1,
            columnNumber: 0,
            paragraphIndex: tableIndex
          }
        });
        
        tableIndex++;
      }
    }
    
    return tables;
  }
  
  /**
   * 提取表格行数据
   * @param tableHtml 表格HTML
   * @returns 表格数据
   */
  private extractTableRows(tableHtml: string): string[][] {
    const rows: string[][] = [];
    
    const rowRegex = /<tr[^>]*>(.*?)<\/tr>/gi;
    let rowMatch;
    
    while ((rowMatch = rowRegex.exec(tableHtml)) !== null) {
      const rowHtml = rowMatch[1];
      const cells: string[] = [];
      
      const cellRegex = /<t[dh][^>]*>(.*?)<\/t[dh]>/gi;
      let cellMatch;
      
      while ((cellMatch = cellRegex.exec(rowHtml)) !== null) {
        const cellContent = this.htmlToPlainText(cellMatch[1]);
        cells.push(cellContent);
      }
      
      if (cells.length > 0) {
        rows.push(cells);
      }
    }
    
    return rows;
  }
  
  /**
   * 提取标题信息
   * @param html HTML内容
   * @returns 标题信息列表
   */
  private extractHeaders(html: string): HeaderInfo[] {
    const headers: HeaderInfo[] = [];
    
    // 匹配h1-h6标签
    const headerRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi;
    let match;
    let headerIndex = 0;
    
    while ((match = headerRegex.exec(html)) !== null) {
      const level = parseInt(match[1]);
      const text = this.htmlToPlainText(match[2]);
      
      if (text.trim()) {
        headers.push({
          id: uuidv4(),
          level,
          text,
          position: {
            startIndex: 0,
            endIndex: text.length,
            lineNumber: headerIndex + 1,
            columnNumber: 0,
            paragraphIndex: headerIndex
          }
        });
        
        headerIndex++;
      }
    }
    
    return headers;
  }
  
  /**
   * 提取列表信息
   * @param html HTML内容
   * @returns 列表信息列表
   */
  private extractLists(html: string): ListInfo[] {
    const lists: ListInfo[] = [];
    
    // 匹配有序和无序列表
    const listRegex = /<(ul|ol)[^>]*>(.*?)<\/\1>/gi;
    let match;
    let listIndex = 0;
    
    while ((match = listRegex.exec(html)) !== null) {
      const listType = match[1] === 'ol' ? 'ordered' : 'unordered';
      const listHtml = match[2];
      const items = this.extractListItems(listHtml);
      
      if (items.length > 0) {
        lists.push({
          id: uuidv4(),
          type: listType,
          items,
          position: {
            startIndex: 0,
            endIndex: 0,
            lineNumber: listIndex + 1,
            columnNumber: 0,
            paragraphIndex: listIndex
          }
        });
        
        listIndex++;
      }
    }
    
    return lists;
  }
  
  /**
   * 提取列表项
   * @param listHtml 列表HTML
   * @returns 列表项数组
   */
  private extractListItems(listHtml: string): string[] {
    const items: string[] = [];
    
    const itemRegex = /<li[^>]*>(.*?)<\/li>/gi;
    let match;
    
    while ((match = itemRegex.exec(listHtml)) !== null) {
      const itemContent = this.htmlToPlainText(match[1]);
      if (itemContent.trim()) {
        items.push(itemContent);
      }
    }
    
    return items;
  }
  
  /**
   * 提取格式信息
   * @param html HTML内容
   * @returns 格式信息列表
   */
  private extractFormattingInfo(html: string): FormattingInfo[] {
    // 简化的格式信息提取
    // 在实际应用中可以更详细地解析CSS样式
    return [];
  }
  
  /**
   * HTML转纯文本
   * @param html HTML内容
   * @returns 纯文本
   */
  private htmlToPlainText(html: string): string {
    return html
      .replace(/<[^>]*>/g, '') // 移除HTML标签
      .replace(/&nbsp;/g, ' ') // 替换非断行空格
      .replace(/&amp;/g, '&') // 替换HTML实体
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ') // 合并多个空白字符
      .trim();
  }
  
  /**
   * 纯文本转HTML（用于PDF）
   * @param plainText 纯文本
   * @returns HTML内容
   */
  private plainTextToHtml(plainText: string): string {
    return plainText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .map(line => `<p>${line}</p>`)
      .join('\n');
  }
  
  /**
   * 创建文档元数据
   * @param file 文件对象
   * @param content 文档内容
   * @returns 文档元数据
   */
  private createDocumentMetadata(file: Express.Multer.File, content: DocumentContent): DocumentMetadata {
    return {
      size: file.size,
      mimeType: file.mimetype,
      extension: file.originalname.split('.').pop()?.toLowerCase() || '',
      lastModified: new Date(),
      characterCount: content.plainText.length
    };
  }
  
  /**
   * 创建错误对象
   * @param type 错误类型
   * @param message 错误消息
   * @param fileName 文件名
   * @returns 错误对象
   */
  private createError(
    type: ContractComparisonErrorType,
    message: string,
    fileName?: string
  ): ContractComparisonError {
    const error = new Error(message) as any;
    error.type = type;
    error.message = message;
    error.timestamp = new Date();
    error.fileName = fileName;
    return error;
  }
}
