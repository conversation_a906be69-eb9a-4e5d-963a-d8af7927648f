/**
 * 合同对比主服务
 * 
 * 整合文档解析和差异分析服务，实现完整的对比工作流程
 * 负责结果格式化和统计生成
 */

import { v4 as uuidv4 } from 'uuid';
import { ContractDocumentParser } from './contractDocumentParser';
import { ContractDifferenceAnalyzer } from './contractDifferenceAnalyzer';
import type {
  ParsedDocument,
  ComparisonResult,
  DifferenceItem,
  DifferenceStatistics,
  ContractComparisonError,
  ContractComparisonErrorType
} from '../../src/types/contractComparison';

/**
 * 对比选项接口
 */
interface ComparisonOptions {
  /** 是否忽略空白字符 */
  ignoreWhitespace?: boolean;
  /** 是否忽略大小写 */
  ignoreCase?: boolean;
  /** 最小差异长度 */
  minDifferenceLength?: number;
  /** 相似度阈值 */
  similarityThreshold?: number;
  /** 是否生成详细统计 */
  generateDetailedStats?: boolean;
}

/**
 * 合同对比服务类
 */
export class ContractComparisonService {
  
  private readonly documentParser: ContractDocumentParser;
  private readonly differenceAnalyzer: ContractDifferenceAnalyzer;
  
  constructor() {
    this.documentParser = new ContractDocumentParser();
    this.differenceAnalyzer = new ContractDifferenceAnalyzer();
  }
  
  /**
   * 执行完整的合同对比流程
   * @param primaryFile 主文件
   * @param secondaryFile 副文件
   * @param options 对比选项
   * @returns 对比结果
   */
  async compareContracts(
    primaryFile: Express.Multer.File,
    secondaryFile: Express.Multer.File,
    options: ComparisonOptions = {}
  ): Promise<ComparisonResult> {
    const sessionId = uuidv4();
    const startTime = Date.now();
    
    console.log(`🚀 [合同对比] 开始对比流程 [${sessionId}]`, {
      primaryFile: primaryFile.originalname,
      secondaryFile: secondaryFile.originalname,
      options
    });
    
    try {
      // 1. 解析文档
      console.log(`📄 [合同对比] [${sessionId}] 步骤1: 解析文档`);
      const [primaryDoc, secondaryDoc] = await Promise.all([
        this.documentParser.parseDocument(primaryFile),
        this.documentParser.parseDocument(secondaryFile)
      ]);
      
      console.log(`✅ [合同对比] [${sessionId}] 文档解析完成`, {
        primaryParagraphs: primaryDoc.content.structure.paragraphs.length,
        secondaryParagraphs: secondaryDoc.content.structure.paragraphs.length
      });
      
      // 2. 分析差异
      console.log(`🔍 [合同对比] [${sessionId}] 步骤2: 分析差异`);
      const differences = await this.differenceAnalyzer.analyzeDifferences(
        primaryDoc,
        secondaryDoc,
        {
          ignoreWhitespace: options.ignoreWhitespace ?? true,
          ignoreCase: options.ignoreCase ?? false,
          minDifferenceLength: options.minDifferenceLength ?? 3,
          similarityThreshold: options.similarityThreshold ?? 0.8
        }
      );
      
      console.log(`✅ [合同对比] [${sessionId}] 差异分析完成，发现 ${differences.length} 个差异`);
      
      // 3. 计算相似度
      console.log(`📊 [合同对比] [${sessionId}] 步骤3: 计算相似度`);
      const similarity = this.differenceAnalyzer.calculateSimilarity(primaryDoc, secondaryDoc);
      
      // 4. 生成统计信息
      console.log(`📈 [合同对比] [${sessionId}] 步骤4: 生成统计信息`);
      const statistics = this.generateStatistics(
        differences,
        primaryDoc,
        secondaryDoc,
        options.generateDetailedStats ?? true
      );
      
      // 5. 构建对比结果
      const processTime = Date.now() - startTime;
      const comparisonResult: ComparisonResult = {
        sessionId,
        primaryDocument: primaryDoc,
        secondaryDocument: secondaryDoc,
        differences,
        statistics,
        similarity: Math.round(similarity * 100), // 转换为百分比
        processTime,
        createdAt: new Date(),
        status: 'completed'
      };
      
      console.log(`🎉 [合同对比] [${sessionId}] 对比完成`, {
        similarity: `${comparisonResult.similarity}%`,
        differences: differences.length,
        processTime: `${processTime}ms`
      });
      
      return comparisonResult;
      
    } catch (error) {
      console.error(`❌ [合同对比] [${sessionId}] 对比失败:`, error);
      
      // 构建错误结果
      const processTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      const comparisonResult: ComparisonResult = {
        sessionId,
        primaryDocument: {} as ParsedDocument, // 空文档
        secondaryDocument: {} as ParsedDocument, // 空文档
        differences: [],
        statistics: this.getEmptyStatistics(),
        similarity: 0,
        processTime,
        createdAt: new Date(),
        status: 'failed',
        error: errorMessage
      };
      
      return comparisonResult;
    }
  }
  
  /**
   * 生成差异统计信息
   * @param differences 差异列表
   * @param primaryDoc 主文档
   * @param secondaryDoc 副文档
   * @param detailed 是否生成详细统计
   * @returns 统计信息
   */
  private generateStatistics(
    differences: DifferenceItem[],
    primaryDoc: ParsedDocument,
    secondaryDoc: ParsedDocument,
    detailed: boolean = true
  ): DifferenceStatistics {
    console.log('📊 [统计生成] 生成差异统计信息...');
    
    // 基础统计
    const addedCount = differences.filter(d => d.type === 'added').length;
    const deletedCount = differences.filter(d => d.type === 'deleted').length;
    const modifiedCount = differences.filter(d => d.type === 'modified').length;
    const movedCount = differences.filter(d => d.type === 'moved').length;
    
    // 受影响的段落数
    const affectedParagraphs = this.calculateAffectedParagraphs(differences);
    
    // 相似度分数
    const similarityScore = this.differenceAnalyzer.calculateSimilarity(primaryDoc, secondaryDoc);
    
    // 文档长度对比
    const primaryLength = primaryDoc.content.plainText.length;
    const secondaryLength = secondaryDoc.content.plainText.length;
    const lengthDifference = secondaryLength - primaryLength;
    const percentageChange = primaryLength > 0 ? (lengthDifference / primaryLength) * 100 : 0;
    
    const statistics: DifferenceStatistics = {
      totalDifferences: differences.length,
      addedCount,
      deletedCount,
      modifiedCount,
      movedCount,
      affectedParagraphs,
      similarityScore: Math.round(similarityScore * 100),
      lengthComparison: {
        primary: primaryLength,
        secondary: secondaryLength,
        difference: lengthDifference,
        percentageChange: Math.round(percentageChange * 100) / 100
      }
    };
    
    console.log('✅ [统计生成] 统计信息生成完成', statistics);
    
    return statistics;
  }
  
  /**
   * 计算受影响的段落数
   * @param differences 差异列表
   * @returns 受影响的段落数
   */
  private calculateAffectedParagraphs(differences: DifferenceItem[]): number {
    const affectedParagraphIndices = new Set<number>();
    
    for (const diff of differences) {
      if (diff.primaryPosition?.paragraphIndex !== undefined) {
        affectedParagraphIndices.add(diff.primaryPosition.paragraphIndex);
      }
      if (diff.secondaryPosition?.paragraphIndex !== undefined) {
        affectedParagraphIndices.add(diff.secondaryPosition.paragraphIndex);
      }
    }
    
    return affectedParagraphIndices.size;
  }
  
  /**
   * 获取空的统计信息
   * @returns 空统计信息
   */
  private getEmptyStatistics(): DifferenceStatistics {
    return {
      totalDifferences: 0,
      addedCount: 0,
      deletedCount: 0,
      modifiedCount: 0,
      movedCount: 0,
      affectedParagraphs: 0,
      similarityScore: 0,
      lengthComparison: {
        primary: 0,
        secondary: 0,
        difference: 0,
        percentageChange: 0
      }
    };
  }
  
  /**
   * 验证文件格式
   * @param file 文件对象
   * @returns 是否有效
   */
  validateFileFormat(file: Express.Multer.File): boolean {
    const supportedExtensions = ['.doc', '.docx', '.pdf'];
    const supportedMimeTypes = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf'
    ];
    
    const fileName = file.originalname.toLowerCase();
    const mimeType = file.mimetype.toLowerCase();
    
    const hasValidExtension = supportedExtensions.some(ext => fileName.endsWith(ext));
    const hasValidMimeType = supportedMimeTypes.some(type => mimeType.includes(type));
    
    return hasValidExtension && hasValidMimeType;
  }
  
  /**
   * 验证文件大小
   * @param file 文件对象
   * @param maxSizeInMB 最大大小（MB）
   * @returns 是否有效
   */
  validateFileSize(file: Express.Multer.File, maxSizeInMB: number = 50): boolean {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  }
  
  /**
   * 创建错误对象
   * @param type 错误类型
   * @param message 错误消息
   * @param fileName 文件名
   * @returns 错误对象
   */
  private createError(
    type: ContractComparisonErrorType,
    message: string,
    fileName?: string
  ): ContractComparisonError {
    const error = new Error(message) as any;
    error.type = type;
    error.message = message;
    error.timestamp = new Date();
    error.fileName = fileName;
    return error;
  }
}
