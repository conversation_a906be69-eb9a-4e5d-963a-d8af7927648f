/**
 * File Compare Service
 * 文件对比服务 - 提供文件对比分析功能
 * 独立于现有Compare.tsx模块，专门处理文件级别的对比
 */

import { DocumentParserService } from './documentParserService';
import {
  FileComparisonResult,
  DifferenceItem,
  SimilarityItem,
  ClauseComparisonResult,
  ClauseItem,
  ClauseMatch,
  ParsedDocument,
  DocumentContent,
  ComparisonOptions,
  ComparisonStatistics,
  FileMetadata,
  TextPosition,
  DifferenceType,
  DifferenceSeverity,
  ClauseType,
  ImportanceLevel,
  ImpactLevel,
  FileCompareError,
  FileCompareErrorType,
  ParagraphType
} from '../../src/types/fileCompare';

/**
 * 条款提取器类
 * 专门负责从文档中提取和分析条款
 */
class ClauseExtractor {
  /**
   * 从文档内容中提取条款
   */
  extractClauses(content: DocumentContent): ClauseItem[] {
    const clauses: ClauseItem[] = [];
    
    // 基于规则的条款识别
    content.paragraphs.forEach((paragraph, index) => {
      const clauseType = this.identifyClauseType(paragraph.content);
      if (clauseType !== ClauseType.OTHER) {
        clauses.push({
          id: `clause_${index}`,
          type: clauseType,
          title: this.extractClauseTitle(paragraph.content),
          content: paragraph.content,
          position: paragraph.position,
          importance: this.assessClauseImportance(clauseType),
          keywords: this.extractKeywords(paragraph.content)
        });
      }
    });

    return this.prioritizeClauses(clauses);
  }

  /**
   * 基于规则的条款类型识别
   */
  private identifyClauseType(content: string): ClauseType {
    const lowerContent = content.toLowerCase();
    
    // 付款条款识别
    if (this.matchesPaymentPatterns(lowerContent)) return ClauseType.PAYMENT;
    
    // 交付条款识别
    if (this.matchesDeliveryPatterns(lowerContent)) return ClauseType.DELIVERY;
    
    // 保修条款识别
    if (this.matchesWarrantyPatterns(lowerContent)) return ClauseType.WARRANTY;
    
    // 责任条款识别
    if (this.matchesLiabilityPatterns(lowerContent)) return ClauseType.LIABILITY;
    
    // 终止条款识别
    if (this.matchesTerminationPatterns(lowerContent)) return ClauseType.TERMINATION;
    
    // 保密条款识别
    if (this.matchesConfidentialityPatterns(lowerContent)) return ClauseType.CONFIDENTIALITY;
    
    // 争议解决条款识别
    if (this.matchesDisputePatterns(lowerContent)) return ClauseType.DISPUTE;
    
    // 不可抗力条款识别
    if (this.matchesForceMajeurePatterns(lowerContent)) return ClauseType.FORCE_MAJEURE;
    
    // 知识产权条款识别
    if (this.matchesIntellectualPropertyPatterns(lowerContent)) return ClauseType.INTELLECTUAL_PROPERTY;
    
    return ClauseType.OTHER;
  }

  /**
   * 付款条款模式匹配
   */
  private matchesPaymentPatterns(content: string): boolean {
    const paymentKeywords = [
      '付款', '支付', '款项', '费用', '价款', '货款', '服务费',
      '预付款', '尾款', '定金', '保证金', '违约金', '滞纳金',
      '银行转账', '现金支付', '支票', '汇款', '结算'
    ];
    return paymentKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 交付条款模式匹配
   */
  private matchesDeliveryPatterns(content: string): boolean {
    const deliveryKeywords = [
      '交付', '交货', '发货', '运输', '配送', '送达',
      '交付时间', '交付地点', '验收', '包装', '运费',
      '物流', '快递', '邮寄', '自提'
    ];
    return deliveryKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 保修条款模式匹配
   */
  private matchesWarrantyPatterns(content: string): boolean {
    const warrantyKeywords = [
      '保修', '保证', '质保', '维修', '维护', '保养',
      '质量保证', '性能保证', '免费维修', '保修期',
      '质量问题', '缺陷', '故障'
    ];
    return warrantyKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 责任条款模式匹配
   */
  private matchesLiabilityPatterns(content: string): boolean {
    const liabilityKeywords = [
      '责任', '赔偿', '损失', '损害', '承担', '免责',
      '违约责任', '赔偿责任', '连带责任', '限制责任',
      '损害赔偿', '经济损失', '直接损失', '间接损失'
    ];
    return liabilityKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 终止条款模式匹配
   */
  private matchesTerminationPatterns(content: string): boolean {
    const terminationKeywords = [
      '终止', '解除', '解约', '中止', '期满', '到期',
      '合同终止', '协议解除', '提前终止', '违约解除',
      '合同期限', '有效期', '续约', '延期'
    ];
    return terminationKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 保密条款模式匹配
   */
  private matchesConfidentialityPatterns(content: string): boolean {
    const confidentialityKeywords = [
      '保密', '机密', '秘密', '泄露', '披露', '公开',
      '商业秘密', '技术秘密', '保密义务', '保密协议',
      '机密信息', '敏感信息', '内部信息'
    ];
    return confidentialityKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 争议解决条款模式匹配
   */
  private matchesDisputePatterns(content: string): boolean {
    const disputeKeywords = [
      '争议', '纠纷', '仲裁', '诉讼', '法院', '管辖',
      '争议解决', '仲裁委员会', '仲裁庭', '调解',
      '司法管辖', '法律适用', '管辖法院'
    ];
    return disputeKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 不可抗力条款模式匹配
   */
  private matchesForceMajeurePatterns(content: string): boolean {
    const forceMajeureKeywords = [
      '不可抗力', '天灾', '自然灾害', '战争', '政府行为',
      '地震', '洪水', '台风', '火灾', '疫情', '罢工',
      '政策变化', '法律变更', '不可预见'
    ];
    return forceMajeureKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 知识产权条款模式匹配
   */
  private matchesIntellectualPropertyPatterns(content: string): boolean {
    const ipKeywords = [
      '知识产权', '专利', '商标', '著作权', '版权',
      '技术秘密', '专有技术', '发明', '实用新型',
      '外观设计', '软件著作权', '商业标识', '域名'
    ];
    return ipKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * 提取条款标题
   */
  private extractClauseTitle(content: string): string {
    const lines = content.split('\n');
    const firstLine = lines[0].trim();
    
    // 如果第一行看起来像标题，使用它
    if (firstLine.length < 100 && (
      /^第[一二三四五六七八九十\d]+[章节条款项]/.test(firstLine) ||
      /^\d+(\.\d+)*\s/.test(firstLine) ||
      /^[一二三四五六七八九十]+[、.]/.test(firstLine)
    )) {
      return firstLine;
    }
    
    // 否则生成一个简短的标题
    return content.substring(0, 50) + (content.length > 50 ? '...' : '');
  }

  /**
   * 评估条款重要性
   */
  private assessClauseImportance(type: ClauseType): ImportanceLevel {
    switch (type) {
      case ClauseType.PAYMENT:
      case ClauseType.LIABILITY:
      case ClauseType.TERMINATION:
        return ImportanceLevel.CRITICAL;
      case ClauseType.DELIVERY:
      case ClauseType.WARRANTY:
      case ClauseType.CONFIDENTIALITY:
        return ImportanceLevel.HIGH;
      case ClauseType.DISPUTE:
      case ClauseType.FORCE_MAJEURE:
      case ClauseType.INTELLECTUAL_PROPERTY:
        return ImportanceLevel.MEDIUM;
      default:
        return ImportanceLevel.LOW;
    }
  }

  /**
   * 提取关键词
   */
  private extractKeywords(content: string): string[] {
    const keywords: string[] = [];
    const allKeywords = [
      // 付款相关
      '付款', '支付', '款项', '费用', '价款', '货款', '服务费',
      '预付款', '尾款', '定金', '保证金', '违约金',
      
      // 交付相关
      '交付', '交货', '发货', '运输', '配送', '验收',
      
      // 保修相关
      '保修', '保证', '质保', '维修', '维护',
      
      // 责任相关
      '责任', '赔偿', '损失', '损害', '承担', '免责',
      
      // 终止相关
      '终止', '解除', '解约', '中止', '期满', '到期',
      
      // 保密相关
      '保密', '机密', '秘密', '泄露', '披露',
      
      // 争议相关
      '争议', '纠纷', '仲裁', '诉讼', '法院', '管辖',
      
      // 不可抗力相关
      '不可抗力', '天灾', '自然灾害', '战争', '政府行为',
      
      // 知识产权相关
      '知识产权', '专利', '商标', '著作权', '版权',
      
      // 通用合同术语
      '合同', '协议', '甲方', '乙方', '双方', '当事人'
    ];
    
    allKeywords.forEach(keyword => {
      if (content.includes(keyword)) {
        keywords.push(keyword);
      }
    });
    
    return [...new Set(keywords)]; // 去重
  }

  /**
   * 按重要性排序条款
   */
  private prioritizeClauses(clauses: ClauseItem[]): ClauseItem[] {
    const importanceOrder = {
      [ImportanceLevel.CRITICAL]: 0,
      [ImportanceLevel.HIGH]: 1,
      [ImportanceLevel.MEDIUM]: 2,
      [ImportanceLevel.LOW]: 3
    };

    return clauses.sort((a, b) => {
      const aOrder = importanceOrder[a.importance];
      const bOrder = importanceOrder[b.importance];
      return aOrder - bOrder;
    });
  }
}

/**
 * 差异分析器类 - 双轨制架构
 * 专门负责分析两个文档之间的差异，使用纯文本进行对比计算
 */
class DifferenceAnalyzer {
  /**
   * 分析两个文档内容的差异 - 双轨制架构
   */
  analyzeDifferences(
    primaryContent: DocumentContent,
    secondaryContent: DocumentContent,
    options: ComparisonOptions
  ): DifferenceItem[] {
    console.log('🔧 [差异分析调试] 开始差异分析（双轨制）:', {
      primaryHasPlainText: !!primaryContent.plainText,
      secondaryHasPlainText: !!secondaryContent.plainText,
      primaryTextLength: primaryContent.text.length,
      secondaryTextLength: secondaryContent.text.length,
      primaryPlainTextLength: primaryContent.plainText?.length || 0,
      secondaryPlainTextLength: secondaryContent.plainText?.length || 0
    });

    const differences: DifferenceItem[] = [];

    // 使用纯文本进行差异分析
    const primaryText = primaryContent.plainText || primaryContent.text;
    const secondaryText = secondaryContent.plainText || secondaryContent.text;

    console.log('🔧 [差异分析调试] 使用文本进行对比:', {
      primaryTextPreview: primaryText.substring(0, 100),
      secondaryTextPreview: secondaryText.substring(0, 100)
    });

    // 基于纯文本的差异分析
    const textDiffs = this.analyzeTextDifferences(primaryText, secondaryText, options);
    differences.push(...textDiffs);

    // 段落级别的差异分析（基于纯文本）
    const paragraphDiffs = this.analyzeParagraphDifferences(
      primaryContent.paragraphs,
      secondaryContent.paragraphs,
      options
    );
    differences.push(...paragraphDiffs);

    // 结构级别的差异分析
    const structureDiffs = this.analyzeStructuralDifferences(
      primaryContent,
      secondaryContent
    );
    differences.push(...structureDiffs);

    console.log('🔧 [差异分析调试] 差异分析完成:', {
      totalDifferences: differences.length,
      textDiffs: textDiffs.length,
      paragraphDiffs: paragraphDiffs.length,
      structureDiffs: structureDiffs.length
    });

    // 对差异进行排序和优化
    return this.optimizeDifferences(differences);
  }

  /**
   * 基于纯文本的差异分析
   */
  private analyzeTextDifferences(
    primaryText: string,
    secondaryText: string,
    options: ComparisonOptions
  ): DifferenceItem[] {
    const differences: DifferenceItem[] = [];

    // 简单的字符级差异检测
    const primaryLines = primaryText.split('\n');
    const secondaryLines = secondaryText.split('\n');

    // 使用简单的行级对比
    const maxLines = Math.max(primaryLines.length, secondaryLines.length);

    for (let i = 0; i < maxLines; i++) {
      const primaryLine = primaryLines[i] || '';
      const secondaryLine = secondaryLines[i] || '';

      if (primaryLine !== secondaryLine) {
        // 计算行在文本中的位置
        const primaryStart = primaryLines.slice(0, i).join('\n').length + (i > 0 ? 1 : 0);
        const secondaryStart = secondaryLines.slice(0, i).join('\n').length + (i > 0 ? 1 : 0);

        differences.push({
          id: `text-diff-${i}`,
          type: 'text-change',
          severity: 'medium',
          description: `第${i + 1}行内容不同`,
          position: {
            start: primaryStart,
            end: primaryStart + primaryLine.length
          },
          primaryText: primaryLine,
          secondaryText: secondaryLine,
          context: {
            before: primaryLines[i - 1] || '',
            after: primaryLines[i + 1] || ''
          }
        });
      }
    }

    return differences;
  }

  /**
   * 分析段落级别的差异
   */
  private analyzeParagraphDifferences(
    primaryParagraphs: any[],
    secondaryParagraphs: any[],
    options: ComparisonOptions
  ): DifferenceItem[] {
    const differences: DifferenceItem[] = [];
    const primaryTexts = primaryParagraphs.map(p => this.normalizeText(p.content, options));
    const secondaryTexts = secondaryParagraphs.map(p => this.normalizeText(p.content, options));
    
    // 使用改进的LCS算法进行差异检测
    const lcsResult = this.computeLCS(primaryTexts, secondaryTexts);
    const diffOperations = this.generateDiffOperations(lcsResult, primaryParagraphs, secondaryParagraphs);
    
    // 转换为DifferenceItem格式
    diffOperations.forEach((operation, index) => {
      const diffItem = this.createDifferenceItem(operation, index);
      if (diffItem) {
        differences.push(diffItem);
      }
    });
    
    return differences;
  }

  /**
   * 分析结构级别的差异
   */
  private analyzeStructuralDifferences(
    primaryContent: DocumentContent,
    secondaryContent: DocumentContent
  ): DifferenceItem[] {
    const differences: DifferenceItem[] = [];
    
    // 标题结构差异
    const headingDiffs = this.analyzeHeadingDifferences(
      primaryContent.structure?.headings || [],
      secondaryContent.structure?.headings || []
    );
    differences.push(...headingDiffs);
    
    // 列表结构差异
    const listDiffs = this.analyzeListDifferences(
      primaryContent.structure?.lists || [],
      secondaryContent.structure?.lists || []
    );
    differences.push(...listDiffs);
    
    return differences;
  }

  /**
   * 标题结构差异分析
   */
  private analyzeHeadingDifferences(primaryHeadings: any[], secondaryHeadings: any[]): DifferenceItem[] {
    const differences: DifferenceItem[] = [];
    
    // 简单的标题匹配算法
    const primaryTitles = primaryHeadings.map(h => h.text);
    const secondaryTitles = secondaryHeadings.map(h => h.text);
    
    // 查找缺失的标题
    primaryTitles.forEach((title, index) => {
      if (!secondaryTitles.includes(title)) {
        differences.push({
          id: `heading_diff_${index}`,
          type: DifferenceType.DELETED,
          severity: DifferenceSeverity.MEDIUM,
          primaryText: title,
          secondaryText: '',
          primaryPosition: primaryHeadings[index].position,
          secondaryPosition: undefined,
          description: `标题"${title}"在副文件中缺失`,
          suggestion: '检查是否需要在副文件中添加此标题',
          impact: ImpactLevel.MEDIUM
        });
      }
    });
    
    // 查找新增的标题
    secondaryTitles.forEach((title, index) => {
      if (!primaryTitles.includes(title)) {
        differences.push({
          id: `heading_diff_new_${index}`,
          type: DifferenceType.ADDED,
          severity: DifferenceSeverity.MEDIUM,
          primaryText: '',
          secondaryText: title,
          primaryPosition: undefined,
          secondaryPosition: secondaryHeadings[index].position,
          description: `副文件中新增标题"${title}"`,
          suggestion: '检查新增标题是否必要',
          impact: ImpactLevel.MEDIUM
        });
      }
    });
    
    return differences;
  }

  /**
   * 列表结构差异分析
   */
  private analyzeListDifferences(primaryLists: any[], secondaryLists: any[]): DifferenceItem[] {
    const differences: DifferenceItem[] = [];
    
    // 比较列表项数量
    if (primaryLists.length !== secondaryLists.length) {
      differences.push({
        id: `list_count_diff`,
        type: primaryLists.length > secondaryLists.length ? DifferenceType.DELETED : DifferenceType.ADDED,
        severity: DifferenceSeverity.LOW,
        primaryText: `列表数量: ${primaryLists.length}`,
        secondaryText: `列表数量: ${secondaryLists.length}`,
        primaryPosition: { startLine: 0, endLine: 0, startColumn: 0, endColumn: 0, startIndex: 0, endIndex: 0 },
        secondaryPosition: { startLine: 0, endLine: 0, startColumn: 0, endColumn: 0, startIndex: 0, endIndex: 0 },
        description: '列表结构发生变化',
        suggestion: '检查列表变化是否符合预期',
        impact: ImpactLevel.LOW
      });
    }
    
    return differences;
  }

  /**
   * 计算最长公共子序列（LCS）
   */
  private computeLCS(seq1: string[], seq2: string[]): number[][] {
    const m = seq1.length;
    const n = seq2.length;
    const dp: number[][] = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0));
    
    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (seq1[i - 1] === seq2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
        }
      }
    }
    
    return dp;
  }

  /**
   * 生成差异操作序列
   */
  private generateDiffOperations(
    lcsMatrix: number[][],
    primaryParagraphs: any[],
    secondaryParagraphs: any[]
  ): DiffOperation[] {
    const operations: DiffOperation[] = [];
    let i = primaryParagraphs.length;
    let j = secondaryParagraphs.length;
    
    while (i > 0 || j > 0) {
      if (i > 0 && j > 0 && primaryParagraphs[i - 1].content === secondaryParagraphs[j - 1].content) {
        // 相同内容，跳过
        i--;
        j--;
      } else if (j > 0 && (i === 0 || lcsMatrix[i][j - 1] >= lcsMatrix[i - 1][j])) {
        // 插入操作
        operations.unshift({
          type: 'insert',
          primaryIndex: i,
          secondaryIndex: j - 1,
          content: secondaryParagraphs[j - 1].content
        });
        j--;
      } else if (i > 0) {
        // 删除操作
        operations.unshift({
          type: 'delete',
          primaryIndex: i - 1,
          secondaryIndex: j,
          content: primaryParagraphs[i - 1].content
        });
        i--;
      }
    }
    
    return operations;
  }

  /**
   * 创建差异项
   */
  private createDifferenceItem(operation: DiffOperation, index: number): DifferenceItem | null {
    const id = `diff_${index}_${operation.type}`;
    
    switch (operation.type) {
      case 'insert':
        return {
          id,
          type: DifferenceType.ADDED,
          severity: this.calculateSeverity(operation.content),
          primaryText: '',
          secondaryText: operation.content,
          primaryPosition: undefined,
          secondaryPosition: {
            startLine: operation.secondaryIndex,
            endLine: operation.secondaryIndex + 1,
            startColumn: 0,
            endColumn: operation.content.length,
            startIndex: operation.secondaryIndex,
            endIndex: operation.secondaryIndex + 1
          },
          description: '新增内容',
          suggestion: this.generateSuggestions('added', operation.content).join('; '),
          impact: this.calculateImpact(operation.content)
        };
      
      case 'delete':
        return {
          id,
          type: DifferenceType.DELETED,
          severity: this.calculateSeverity(operation.content),
          primaryText: operation.content,
          secondaryText: '',
          primaryPosition: {
            startLine: operation.primaryIndex,
            endLine: operation.primaryIndex + 1,
            startColumn: 0,
            endColumn: operation.content.length,
            startIndex: operation.primaryIndex,
            endIndex: operation.primaryIndex + 1
          },
          secondaryPosition: undefined,
          description: '删除内容',
          suggestion: this.generateSuggestions('deleted', operation.content).join('; '),
          impact: this.calculateImpact(operation.content)
        };
      
      default:
        return null;
    }
  }

  /**
   * 计算差异严重程度
   */
  private calculateSeverity(content: string): DifferenceSeverity {
    const criticalKeywords = ['付款', '违约', '终止', '赔偿', '责任'];
    const highKeywords = ['交付', '保修', '保证', '期限'];
    const mediumKeywords = ['通知', '联系', '地址', '方式'];
    
    const lowerContent = content.toLowerCase();
    
    if (criticalKeywords.some(keyword => lowerContent.includes(keyword))) {
      return DifferenceSeverity.CRITICAL;
    }
    if (highKeywords.some(keyword => lowerContent.includes(keyword))) {
      return DifferenceSeverity.HIGH;
    }
    if (mediumKeywords.some(keyword => lowerContent.includes(keyword))) {
      return DifferenceSeverity.MEDIUM;
    }
    
    return DifferenceSeverity.LOW;
  }

  /**
   * 计算影响级别
   */
  private calculateImpact(content: string): ImpactLevel {
    const severity = this.calculateSeverity(content);
    
    switch (severity) {
      case DifferenceSeverity.CRITICAL:
        return ImpactLevel.HIGH;
      case DifferenceSeverity.HIGH:
        return ImpactLevel.HIGH;
      case DifferenceSeverity.MEDIUM:
        return ImpactLevel.MEDIUM;
      default:
        return ImpactLevel.LOW;
    }
  }

  /**
   * 生成建议
   */
  private generateSuggestions(type: 'added' | 'deleted' | 'modified', content: string): string[] {
    const suggestions: string[] = [];
    
    switch (type) {
      case 'added':
        suggestions.push('检查新增内容是否必要');
        if (content.length > 100) {
          suggestions.push('新增内容较长，建议仔细审查');
        }
        break;
      
      case 'deleted':
        suggestions.push('确认删除内容是否合适');
        if (this.calculateSeverity(content) === DifferenceSeverity.CRITICAL) {
          suggestions.push('删除的内容包含关键条款，需要特别注意');
        }
        break;
      
      case 'modified':
        suggestions.push('比较修改前后的差异');
        suggestions.push('确认修改是否符合预期');
        break;
    }
    
    return suggestions;
  }

  /**
   * 文本标准化
   */
  private normalizeText(text: string, options: ComparisonOptions): string {
    let normalized = text;
    
    if (options.ignoreWhitespace) {
      normalized = normalized.replace(/\s+/g, ' ').trim();
    }
    
    if (options.ignoreFormatting) {
      // 移除格式化字符
      normalized = normalized.replace(/[\*\*\_\_\~\~]/g, '');
    }
    
    return normalized;
  }

  /**
   * 优化差异结果
   */
  private optimizeDifferences(differences: DifferenceItem[]): DifferenceItem[] {
    // 按位置排序
    differences.sort((a, b) => {
      const posA = a.primaryPosition || a.secondaryPosition;
      const posB = b.primaryPosition || b.secondaryPosition;
      if (!posA || !posB) return 0;
      return posA.startIndex - posB.startIndex;
    });
    
    // 合并相邻的相似差异
    const optimized: DifferenceItem[] = [];
    let current: DifferenceItem | null = null;
    
    for (const diff of differences) {
      if (!current) {
        current = diff;
        continue;
      }
      
      // 检查是否可以合并
      if (this.canMergeDifferences(current, diff)) {
        current = this.mergeDifferences(current, diff);
      } else {
        optimized.push(current);
        current = diff;
      }
    }
    
    if (current) {
      optimized.push(current);
    }
    
    return optimized;
  }

  /**
   * 检查两个差异是否可以合并
   */
  private canMergeDifferences(diff1: DifferenceItem, diff2: DifferenceItem): boolean {
    if (diff1.type !== diff2.type || diff1.severity !== diff2.severity) {
      return false;
    }
    
    // 检查位置是否相邻
    const pos1 = diff1.primaryPosition || diff1.secondaryPosition;
    const pos2 = diff2.primaryPosition || diff2.secondaryPosition;
    
    if (!pos1 || !pos2) {
      return false;
    }
    
    return Math.abs(pos1.endLine - pos2.startLine) <= 1;
  }

  /**
   * 合并两个差异
   */
  private mergeDifferences(diff1: DifferenceItem, diff2: DifferenceItem): DifferenceItem {
    return {
      ...diff1,
      id: `${diff1.id}_merged_${diff2.id}`,
      primaryText: diff1.primaryText + '\n' + diff2.primaryText,
      secondaryText: diff1.secondaryText + '\n' + diff2.secondaryText,
      primaryPosition: diff1.primaryPosition && diff2.primaryPosition ? {
        startLine: Math.min(diff1.primaryPosition.startLine, diff2.primaryPosition.startLine),
        endLine: Math.max(diff1.primaryPosition.endLine, diff2.primaryPosition.endLine),
        startColumn: Math.min(diff1.primaryPosition.startColumn, diff2.primaryPosition.startColumn),
        endColumn: Math.max(diff1.primaryPosition.endColumn, diff2.primaryPosition.endColumn),
        startIndex: Math.min(diff1.primaryPosition.startIndex, diff2.primaryPosition.startIndex),
        endIndex: Math.max(diff1.primaryPosition.endIndex, diff2.primaryPosition.endIndex)
      } : diff1.primaryPosition || diff2.primaryPosition,
      secondaryPosition: diff1.secondaryPosition && diff2.secondaryPosition ? {
        startLine: Math.min(diff1.secondaryPosition.startLine, diff2.secondaryPosition.startLine),
        endLine: Math.max(diff1.secondaryPosition.endLine, diff2.secondaryPosition.endLine),
        startColumn: Math.min(diff1.secondaryPosition.startColumn, diff2.secondaryPosition.startColumn),
        endColumn: Math.max(diff1.secondaryPosition.endColumn, diff2.secondaryPosition.endColumn),
        startIndex: Math.min(diff1.secondaryPosition.startIndex, diff2.secondaryPosition.startIndex),
        endIndex: Math.max(diff1.secondaryPosition.endIndex, diff2.secondaryPosition.endIndex)
      } : diff1.secondaryPosition || diff2.secondaryPosition,
      suggestion: [diff1.suggestion, diff2.suggestion].filter(Boolean).join('; ')
    };
  }
}

/**
 * 差异操作接口
 */
interface DiffOperation {
  type: 'insert' | 'delete' | 'modify';
  primaryIndex: number;
  secondaryIndex: number;
  content: string;
}

/**
 * TF-IDF向量接口
 */
interface TfIdfVector {
  [term: string]: number;
}

/**
 * 文档向量化结果接口
 */
interface DocumentVector {
  vector: TfIdfVector;
  magnitude: number;
  termCount: number;
}

/**
 * 相似度计算器类
 * 专门负责计算文档之间的各种相似度
 */
class SimilarityCalculator {
  private readonly textWeight = 0.5;      // 文本相似度权重
  private readonly structureWeight = 0.3; // 结构相似度权重
  private readonly clauseWeight = 0.2;    // 条款相似度权重

  /**
   * 计算综合相似度
   */
  calculateOverallSimilarity(
    primaryContent: DocumentContent,
    secondaryContent: DocumentContent,
    primaryClauses: ClauseItem[],
    secondaryClauses: ClauseItem[]
  ): number {
    // 计算各种相似度
    const textSimilarity = this.calculateTextSimilarity(primaryContent.text, secondaryContent.text);
    const structuralSimilarity = this.calculateStructuralSimilarity(primaryContent, secondaryContent);
    const clauseSimilarity = this.calculateClauseSimilarity(primaryClauses, secondaryClauses);

    // 加权计算综合相似度
    const overallSimilarity = 
      textSimilarity * this.textWeight +
      structuralSimilarity * this.structureWeight +
      clauseSimilarity * this.clauseWeight;

    return Math.round(overallSimilarity * 100);
  }

  /**
   * 计算文本相似度（使用TF-IDF和余弦相似度）
   */
  calculateTextSimilarity(text1: string, text2: string): number {
    if (!text1 || !text2) return 0;
    if (text1 === text2) return 1;

    // 文本预处理
    const processedText1 = this.preprocessText(text1);
    const processedText2 = this.preprocessText(text2);

    // 构建词汇表
    const vocabulary = this.buildVocabulary([processedText1, processedText2]);

    // 计算TF-IDF向量
    const vector1 = this.calculateTfIdfVector(processedText1, vocabulary, [processedText1, processedText2]);
    const vector2 = this.calculateTfIdfVector(processedText2, vocabulary, [processedText1, processedText2]);

    // 计算余弦相似度
    return this.cosineSimilarity(vector1, vector2);
  }

  /**
   * 计算结构相似度
   */
  calculateStructuralSimilarity(primaryContent: DocumentContent, secondaryContent: DocumentContent): number {
    let structuralScore = 0;
    let totalChecks = 0;

    // 段落数量相似度
    const paragraphSimilarity = this.calculateCountSimilarity(
      primaryContent.paragraphs.length,
      secondaryContent.paragraphs.length
    );
    structuralScore += paragraphSimilarity * 0.4;
    totalChecks += 0.4;

    // 标题结构相似度
    if (primaryContent.structure?.headings && secondaryContent.structure?.headings) {
      const headingSimilarity = this.calculateHeadingStructureSimilarity(
        primaryContent.structure.headings,
        secondaryContent.structure.headings
      );
      structuralScore += headingSimilarity * 0.3;
      totalChecks += 0.3;
    }

    // 列表结构相似度
    if (primaryContent.structure?.lists && secondaryContent.structure?.lists) {
      const listSimilarity = this.calculateCountSimilarity(
        primaryContent.structure.lists.length,
        secondaryContent.structure.lists.length
      );
      structuralScore += listSimilarity * 0.3;
      totalChecks += 0.3;
    }

    return totalChecks > 0 ? structuralScore / totalChecks : 0;
  }

  /**
   * 计算条款相似度
   */
  calculateClauseSimilarity(primaryClauses: ClauseItem[], secondaryClauses: ClauseItem[]): number {
    if (primaryClauses.length === 0 && secondaryClauses.length === 0) return 1;
    if (primaryClauses.length === 0 || secondaryClauses.length === 0) return 0;

    // 按类型分组条款
    const primaryByType = this.groupClausesByType(primaryClauses);
    const secondaryByType = this.groupClausesByType(secondaryClauses);

    let totalSimilarity = 0;
    let comparedTypes = 0;

    // 获取所有条款类型
    const allTypes = new Set([...Object.keys(primaryByType), ...Object.keys(secondaryByType)]);

    for (const type of allTypes) {
      const primaryOfType = primaryByType[type] || [];
      const secondaryOfType = secondaryByType[type] || [];

      if (primaryOfType.length === 0 && secondaryOfType.length === 0) continue;

      // 计算该类型条款的相似度
      const typeSimilarity = this.calculateClauseTypeSimilarity(primaryOfType, secondaryOfType);
      totalSimilarity += typeSimilarity;
      comparedTypes++;
    }

    return comparedTypes > 0 ? totalSimilarity / comparedTypes : 0;
  }

  /**
   * 文本预处理
   */
  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, '') // 保留中文、英文、数字和空格
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 构建词汇表
   */
  private buildVocabulary(texts: string[]): string[] {
    const vocabulary = new Set<string>();
    
    texts.forEach(text => {
      const words = this.tokenize(text);
      words.forEach(word => vocabulary.add(word));
    });

    return Array.from(vocabulary);
  }

  /**
   * 分词
   */
  private tokenize(text: string): string[] {
    // 简单的分词实现，可以根据需要改进
    return text.split(/\s+/).filter(word => word.length > 1);
  }

  /**
   * 计算TF-IDF向量
   */
  private calculateTfIdfVector(text: string, vocabulary: string[], corpus: string[]): DocumentVector {
    const words = this.tokenize(text);
    const wordCount = words.length;
    const vector: TfIdfVector = {};
    let magnitude = 0;

    // 计算词频（TF）
    const termFreq: { [term: string]: number } = {};
    words.forEach(word => {
      termFreq[word] = (termFreq[word] || 0) + 1;
    });

    // 计算TF-IDF
    vocabulary.forEach(term => {
      const tf = (termFreq[term] || 0) / wordCount;
      const idf = this.calculateIdf(term, corpus);
      const tfidf = tf * idf;
      
      vector[term] = tfidf;
      magnitude += tfidf * tfidf;
    });

    return {
      vector,
      magnitude: Math.sqrt(magnitude),
      termCount: Object.keys(termFreq).length
    };
  }

  /**
   * 计算逆文档频率（IDF）
   */
  private calculateIdf(term: string, corpus: string[]): number {
    const documentsWithTerm = corpus.filter(doc => this.tokenize(doc).includes(term)).length;
    return documentsWithTerm > 0 ? Math.log(corpus.length / documentsWithTerm) : 0;
  }

  /**
   * 计算余弦相似度
   */
  private cosineSimilarity(vector1: DocumentVector, vector2: DocumentVector): number {
    if (vector1.magnitude === 0 || vector2.magnitude === 0) return 0;

    let dotProduct = 0;
    const allTerms = new Set([...Object.keys(vector1.vector), ...Object.keys(vector2.vector)]);

    allTerms.forEach(term => {
      const val1 = vector1.vector[term] || 0;
      const val2 = vector2.vector[term] || 0;
      dotProduct += val1 * val2;
    });

    return dotProduct / (vector1.magnitude * vector2.magnitude);
  }

  /**
   * 计算数量相似度
   */
  private calculateCountSimilarity(count1: number, count2: number): number {
    if (count1 === 0 && count2 === 0) return 1;
    const maxCount = Math.max(count1, count2);
    const minCount = Math.min(count1, count2);
    return maxCount > 0 ? minCount / maxCount : 0;
  }

  /**
   * 计算标题结构相似度
   */
  private calculateHeadingStructureSimilarity(headings1: any[], headings2: any[]): number {
    if (headings1.length === 0 && headings2.length === 0) return 1;
    if (headings1.length === 0 || headings2.length === 0) return 0;

    // 比较标题层次结构
    const levels1 = headings1.map(h => h.level);
    const levels2 = headings2.map(h => h.level);

    // 计算层次分布相似度
    const levelDistribution1 = this.calculateLevelDistribution(levels1);
    const levelDistribution2 = this.calculateLevelDistribution(levels2);

    return this.calculateDistributionSimilarity(levelDistribution1, levelDistribution2);
  }

  /**
   * 计算层次分布
   */
  private calculateLevelDistribution(levels: number[]): { [level: number]: number } {
    const distribution: { [level: number]: number } = {};
    levels.forEach(level => {
      distribution[level] = (distribution[level] || 0) + 1;
    });
    return distribution;
  }

  /**
   * 计算分布相似度
   */
  private calculateDistributionSimilarity(dist1: { [key: number]: number }, dist2: { [key: number]: number }): number {
    const allLevels = new Set([...Object.keys(dist1).map(Number), ...Object.keys(dist2).map(Number)]);
    let similarity = 0;
    let totalComparisons = 0;

    allLevels.forEach(level => {
      const count1 = dist1[level] || 0;
      const count2 = dist2[level] || 0;
      similarity += this.calculateCountSimilarity(count1, count2);
      totalComparisons++;
    });

    return totalComparisons > 0 ? similarity / totalComparisons : 0;
  }

  /**
   * 按类型分组条款
   */
  private groupClausesByType(clauses: ClauseItem[]): { [type: string]: ClauseItem[] } {
    const grouped: { [type: string]: ClauseItem[] } = {};
    
    clauses.forEach(clause => {
      const type = clause.type.toString();
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(clause);
    });

    return grouped;
  }

  /**
   * 计算特定类型条款的相似度
   */
  private calculateClauseTypeSimilarity(clauses1: ClauseItem[], clauses2: ClauseItem[]): number {
    if (clauses1.length === 0 && clauses2.length === 0) return 1;
    if (clauses1.length === 0 || clauses2.length === 0) return 0;

    // 计算内容相似度
    let totalSimilarity = 0;
    let comparisons = 0;

    clauses1.forEach(clause1 => {
      let maxSimilarity = 0;
      clauses2.forEach(clause2 => {
        const similarity = this.calculateTextSimilarity(clause1.content, clause2.content);
        maxSimilarity = Math.max(maxSimilarity, similarity);
      });
      totalSimilarity += maxSimilarity;
      comparisons++;
    });

    return comparisons > 0 ? totalSimilarity / comparisons : 0;
  }
}

/**
 * 文件对比服务类
 */
export class FileCompareService {
  private documentParser: DocumentParserService;
  private clauseExtractor: ClauseExtractor;
  private differenceAnalyzer: DifferenceAnalyzer;
  private similarityCalculator: SimilarityCalculator;

  constructor() {
    this.documentParser = new DocumentParserService();
    this.clauseExtractor = new ClauseExtractor();
    this.differenceAnalyzer = new DifferenceAnalyzer();
    this.similarityCalculator = new SimilarityCalculator();
  }

  /**
   * 对比两个文件
   * @param primaryFile 主文件
   * @param secondaryFile 副文件
   * @param options 对比选项
   * @returns 对比结果
   */
  async compareFiles(
    primaryFile: Express.Multer.File,
    secondaryFile: Express.Multer.File,
    options: ComparisonOptions = this.getDefaultOptions()
  ): Promise<FileComparisonResult> {
    console.log('🔍 [文件对比] === compareFiles 开始 ===');
    console.log('🔍 [文件对比] 主文件:', primaryFile.originalname);
    console.log('🔍 [文件对比] 副文件:', secondaryFile.originalname);

    try {
      const startTime = Date.now();

      // 解析两个文件
      const [primaryDoc, secondaryDoc] = await Promise.all([
        this.parseDocumentForComparison(primaryFile),
        this.parseDocumentForComparison(secondaryFile)
      ]);

      // 执行文本级别对比
      const textDifferences = await this.compareTexts(
        primaryDoc.content,
        secondaryDoc.content,
        options
      );

      // 执行相似度分析
      const similarities = await this.findSimilarities(
        primaryDoc.content,
        secondaryDoc.content,
        options
      );

      // 执行条款级别对比（如果启用）
      let clauseComparison: ClauseComparisonResult = {
        primaryClauses: [],
        secondaryClauses: [],
        matchedClauses: [],
        primaryOnlyClauses: [],
        secondaryOnlyClauses: []
      };

      if (options.enableClauseComparison) {
        clauseComparison = await this.compareClauses(
          primaryDoc.content,
          secondaryDoc.content,
          options
        );
      }

      // 生成统计信息
      const statistics = this.generateStatistics(
        textDifferences,
        similarities,
        clauseComparison,
        Date.now() - startTime
      );

      // 计算整体相似度
      const overallSimilarity = this.calculateOverallSimilarity(similarities, textDifferences);

      // 构建包含纯文本的文档内容
      const primaryContentWithPlainText = {
        ...primaryDoc.content,
        plainText: primaryDoc.plainText
      };

      const secondaryContentWithPlainText = {
        ...secondaryDoc.content,
        plainText: secondaryDoc.plainText
      };

      console.log('🔧 [文件对比调试] 构建对比结果:', {
        primaryContentLength: primaryDoc.content.text.length,
        secondaryContentLength: secondaryDoc.content.text.length,
        primaryHasPlainText: !!primaryDoc.plainText,
        secondaryHasPlainText: !!secondaryDoc.plainText,
        primaryPlainTextLength: primaryDoc.plainText?.length || 0,
        secondaryPlainTextLength: secondaryDoc.plainText?.length || 0,
        differencesCount: textDifferences.length,
        similaritiesCount: similarities.length
      });

      const result: FileComparisonResult = {
        sessionId: this.generateComparisonId(),
        timestamp: new Date(),
        primaryFile: primaryDoc.metadata,
        secondaryFile: secondaryDoc.metadata,
        // 添加完整的文档内容，确保前端能正确显示
        primaryContent: primaryContentWithPlainText,
        secondaryContent: secondaryContentWithPlainText,
        overallSimilarity,
        differences: textDifferences,
        similarities,
        clauseComparison,
        statistics,
        status: 'completed'
      };

      console.log('✅ [文件对比] 对比完成:', {
        sessionId: result.sessionId,
        differenceCount: textDifferences.length,
        similarityCount: similarities.length,
        processingTime: statistics.processingTime
      });

      // 详细检查最终返回的数据结构
      console.log('🔧 [文件对比调试] 最终返回数据结构检查:', {
        hasPrimaryContent: !!result.primaryContent,
        hasSecondaryContent: !!result.secondaryContent,
        primaryContentKeys: result.primaryContent ? Object.keys(result.primaryContent) : [],
        secondaryContentKeys: result.secondaryContent ? Object.keys(result.secondaryContent) : [],
        primaryContentText: result.primaryContent?.text?.substring(0, 100) || 'N/A',
        primaryContentPlainText: result.primaryContent?.plainText?.substring(0, 100) || 'N/A',
        secondaryContentText: result.secondaryContent?.text?.substring(0, 100) || 'N/A',
        secondaryContentPlainText: result.secondaryContent?.plainText?.substring(0, 100) || 'N/A',
        primaryContentTextLength: result.primaryContent?.text?.length || 0,
        primaryContentPlainTextLength: result.primaryContent?.plainText?.length || 0,
        secondaryContentTextLength: result.secondaryContent?.text?.length || 0,
        secondaryContentPlainTextLength: result.secondaryContent?.plainText?.length || 0
      });

      return result;

    } catch (error) {
      console.error('❌ [文件对比] 对比失败:', error);
      throw this.createFileCompareError(
        FileCompareErrorType.COMPARISON_FAILED,
        `文件对比失败: ${error instanceof Error ? error.message : '未知错误'}`,
        { primaryFile: primaryFile.originalname, secondaryFile: secondaryFile.originalname }
      );
    }
  }

  /**
   * 解析文档用于对比
   */
  private async parseDocumentForComparison(file: Express.Multer.File): Promise<ParsedDocument> {
    try {
      // 使用现有的DocumentParserService解析文档
      const parseResult = await this.documentParser.parseDocument(file.buffer, file.mimetype, file.originalname);
      
      if (!parseResult.success) {
        throw new Error(parseResult.error || '文档解析失败');
      }

      // 创建文件元数据 - 双轨制架构
      const metadata: FileMetadata = {
        name: file.originalname,
        size: file.size,
        mimeType: file.mimetype,
        extension: this.getFileExtension(file.originalname),
        uploadTime: new Date(),
        hash: this.generateFileHash(file),
        characterCount: parseResult.displayContent.length
      };

      // 创建文档内容 - 双轨制架构
      const content: DocumentContent = {
        text: parseResult.displayContent, // 显示内容（HTML格式）
        paragraphs: this.extractParagraphs(parseResult.displayContent),
        structure: this.extractDocumentStructure(parseResult.displayContent),
        plainText: parseResult.compareContent // 对比内容（纯文本）
      };

      console.log('🔧 [文件对比调试] 文档解析完成（双轨制）:', {
        fileName: file.originalname,
        displayContentLength: parseResult.displayContent.length,
        compareContentLength: parseResult.compareContent.length,
        hasHtmlContent: parseResult.metadata?.hasHtmlContent,
        positionMappings: parseResult.positionMap?.length || 0,
        structureElements: (parseResult.structure?.paragraphs?.length || 0) +
                          (parseResult.structure?.tables?.length || 0) +
                          (parseResult.structure?.headings?.length || 0),
        displayPreview: parseResult.displayContent.substring(0, 200),
        comparePreview: parseResult.compareContent.substring(0, 200)
      });

      const parsedDocument: ParsedDocument = {
        content,
        metadata,
        parseTime: new Date(),
        status: 'success',
        // 保存纯文本用于前端高亮计算
        plainText: parseResult.compareContent
      };

      return parsedDocument;

    } catch (error) {
      throw this.createFileCompareError(
        FileCompareErrorType.PARSE_ERROR,
        `文档解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
        { fileName: file.originalname }
      );
    }
  }

  /**
   * 对比文本内容
   */
  private async compareTexts(
    primaryContent: DocumentContent,
    secondaryContent: DocumentContent,
    options: ComparisonOptions
  ): Promise<DifferenceItem[]> {
    console.log('📝 [文本对比] 开始文本级别对比...');

    // 使用专门的DifferenceAnalyzer进行差异分析
    const differences = this.analyzeDifferences(primaryContent, secondaryContent, options);

    console.log('📝 [文本对比] 文本对比完成:', { differenceCount: differences.length });
    return differences;
  }

  /**
   * 分析文档差异
   */
  private analyzeDifferences(
    primaryContent: DocumentContent,
    secondaryContent: DocumentContent,
    options: ComparisonOptions
  ): DifferenceItem[] {
    // 使用专门的DifferenceAnalyzer进行差异分析
    return this.differenceAnalyzer.analyzeDifferences(primaryContent, secondaryContent, options);
  }

  /**
   * 查找相似内容
   */
  private async findSimilarities(
    primaryContent: DocumentContent,
    secondaryContent: DocumentContent,
    options: ComparisonOptions
  ): Promise<SimilarityItem[]> {
    console.log('🔍 [相似度分析] 开始相似度分析...');

    const similarities: SimilarityItem[] = [];
    const threshold = options.similarityThreshold / 100;

    // 对比所有段落组合
    for (let i = 0; i < primaryContent.paragraphs.length; i++) {
      for (let j = 0; j < secondaryContent.paragraphs.length; j++) {
        const primaryPara = primaryContent.paragraphs[i];
        const secondaryPara = secondaryContent.paragraphs[j];

        // 使用增强的相似度计算（TF-IDF + 余弦相似度）
        const enhancedSimilarity = this.similarityCalculator.calculateTextSimilarity(
          primaryPara.content, 
          secondaryPara.content
        );

        // 保留原有的简单相似度计算作为备选
        const simpleSimilarity = this.calculateTextSimilarity(primaryPara.content, secondaryPara.content);

        // 使用增强算法的结果，但保留简单算法作为验证
        const similarity = enhancedSimilarity;

        if (similarity >= threshold && similarity < 1.0) { // 不包括完全相同的内容
          similarities.push({
            id: `sim_${similarities.length}`,
            similarity: Math.round(similarity * 100),
            primaryPosition: primaryPara.position,
            secondaryPosition: secondaryPara.position,
            text: primaryPara.content.length > secondaryPara.content.length 
              ? primaryPara.content 
              : secondaryPara.content,
            // 增加详细信息，保持功能完整性
            details: {
              enhancedSimilarity: Math.round(enhancedSimilarity * 100),
              simpleSimilarity: Math.round(simpleSimilarity * 100),
              algorithm: 'TF-IDF + Cosine Similarity'
            }
          });
        }
      }
    }

    // 按相似度排序
    similarities.sort((a, b) => b.similarity - a.similarity);

    console.log('🔍 [相似度分析] 相似度分析完成:', { 
      similarityCount: similarities.length,
      algorithm: 'Enhanced TF-IDF + Cosine Similarity'
    });
    return similarities;
  }

  /**
   * 对比条款
   */
  private async compareClauses(
    primaryContent: DocumentContent,
    secondaryContent: DocumentContent,
    options: ComparisonOptions
  ): Promise<ClauseComparisonResult> {
    console.log('📋 [条款对比] 开始条款级别对比...');

    // 提取条款
    const primaryClauses = this.extractClauses(primaryContent);
    const secondaryClauses = this.extractClauses(secondaryContent);

    // 匹配条款
    const matchedClauses: ClauseMatch[] = [];
    const primaryOnlyClauses: ClauseItem[] = [];
    const secondaryOnlyClauses: ClauseItem[] = [];

    // 简单的条款匹配算法
    const usedSecondaryIndices = new Set<number>();

    for (const primaryClause of primaryClauses) {
      let bestMatch: { clause: ClauseItem; score: number; index: number } | null = null;

      for (let j = 0; j < secondaryClauses.length; j++) {
        if (usedSecondaryIndices.has(j)) continue;

        const secondaryClause = secondaryClauses[j];
        const score = this.calculateClauseSimilarity(primaryClause, secondaryClause);

        if (score > 0.5 && (!bestMatch || score > bestMatch.score)) {
          bestMatch = { clause: secondaryClause, score, index: j };
        }
      }

      if (bestMatch) {
        usedSecondaryIndices.add(bestMatch.index);
        matchedClauses.push({
          primaryClause,
          secondaryClause: bestMatch.clause,
          matchScore: Math.round(bestMatch.score * 100),
          differences: this.findClauseDifferences(primaryClause, bestMatch.clause)
        });
      } else {
        primaryOnlyClauses.push(primaryClause);
      }
    }

    // 添加副文件独有的条款
    for (let j = 0; j < secondaryClauses.length; j++) {
      if (!usedSecondaryIndices.has(j)) {
        secondaryOnlyClauses.push(secondaryClauses[j]);
      }
    }

    const result: ClauseComparisonResult = {
      primaryClauses,
      secondaryClauses,
      matchedClauses,
      primaryOnlyClauses,
      secondaryOnlyClauses
    };

    console.log('📋 [条款对比] 条款对比完成:', {
      matchCount: matchedClauses.length,
      primaryOnlyCount: primaryOnlyClauses.length,
      secondaryOnlyCount: secondaryOnlyClauses.length
    });

    return result;
  }

  /**
   * 提取段落
   */
  private extractParagraphs(text: string): any[] {
    return text
      .split(/\n\s*\n/)
      .map(p => p.trim())
      .filter(p => p.length > 0)
      .map((content, index) => ({
        id: `p_${index}`,
        content,
        position: {
          startLine: index + 1,
          endLine: index + 1,
          startColumn: 1,
          endColumn: content.length,
          startIndex: 0,
          endIndex: content.length
        },
        type: ParagraphType.BODY,
        formatting: {
          fontSize: 12,
          bold: false,
          italic: false,
          underline: false,
          alignment: 'left' as const
        }
      }));
  }

  /**
   * 提取文档结构
   */
  private extractDocumentStructure(text: string): any {
    const lines = text.split('\n');
    const headings: any[] = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      
      // 检测标题
      if (this.isHeading(trimmedLine)) {
        headings.push({
          id: `h_${headings.length}`,
          level: this.getHeadingLevel(trimmedLine),
          text: trimmedLine,
          position: {
            startLine: index + 1,
            endLine: index + 1,
            startColumn: 1,
            endColumn: trimmedLine.length,
            startIndex: 0,
            endIndex: trimmedLine.length
          }
        });
      }
    });

    return {
      headings,
      sections: [],
      lists: [],
      tables: []
    };
  }

  /**
   * 提取条款
   */
  private extractClauses(content: DocumentContent): ClauseItem[] {
    // 使用专门的ClauseExtractor类进行条款提取
    return this.clauseExtractor.extractClauses(content);
  }

  /**
   * 计算文本相似度
   */
  private calculateTextSimilarity(text1: string, text2: string): number {
    if (text1 === text2) return 1.0;
    if (!text1 || !text2) return 0.0;

    // 简单的Jaccard相似度计算
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  /**
   * 计算条款相似度
   */
  private calculateClauseSimilarity(clause1: ClauseItem, clause2: ClauseItem): number {
    // 类型匹配加权
    const typeWeight = clause1.type === clause2.type ? 0.3 : 0;
    
    // 内容相似度
    const contentSimilarity = this.calculateTextSimilarity(clause1.content, clause2.content);
    
    // 关键词匹配
    const keywordSimilarity = this.calculateKeywordSimilarity(clause1.keywords, clause2.keywords);
    
    return typeWeight + contentSimilarity * 0.5 + keywordSimilarity * 0.2;
  }

  /**
   * 计算关键词相似度
   */
  private calculateKeywordSimilarity(keywords1: string[], keywords2: string[]): number {
    if (keywords1.length === 0 && keywords2.length === 0) return 1.0;
    if (keywords1.length === 0 || keywords2.length === 0) return 0.0;

    const set1 = new Set(keywords1.map(k => k.toLowerCase()));
    const set2 = new Set(keywords2.map(k => k.toLowerCase()));
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }

  /**
   * 计算整体相似度
   */
  private calculateOverallSimilarity(similarities: SimilarityItem[], differences: DifferenceItem[]): number {
    if (similarities.length === 0 && differences.length === 0) return 100;
    if (similarities.length === 0) return 0;
    
    // 使用增强的相似度计算器进行综合计算
    try {
      // 提取所有相似文本进行综合分析
      const primaryTexts = similarities.map(s => s.text);
      const secondaryTexts = similarities.map(s => s.text);
      
      if (primaryTexts.length > 0 && secondaryTexts.length > 0) {
        // 构建简化的DocumentContent对象用于相似度计算
        const primaryContent: DocumentContent = {
          text: primaryTexts.join('\n'),
          paragraphs: [],
          structure: { headings: [], sections: [], lists: [], tables: [] }
        };
        
        const secondaryContent: DocumentContent = {
          text: secondaryTexts.join('\n'),
          paragraphs: [],
          structure: { headings: [], sections: [], lists: [], tables: [] }
        };
        
        // 使用SimilarityCalculator计算综合相似度
        const enhancedSimilarity = this.similarityCalculator.calculateOverallSimilarity(
          primaryContent,
          secondaryContent,
          [], // 空的条款数组，因为这里主要计算文本相似度
          []
        );
        
        // 应用差异惩罚
        const diffPenalty = Math.min(differences.length * 3, 30); // 减少惩罚力度，每个差异扣3分
        
        return Math.max(0, Math.round(enhancedSimilarity - diffPenalty));
      }
    } catch (error) {
      console.warn('Enhanced similarity calculation failed, falling back to simple method:', error);
    }
    
    // 保留原有的简单计算方法作为后备
    const avgSimilarity = similarities.reduce((sum, item) => sum + item.similarity, 0) / similarities.length;
    const diffPenalty = Math.min(differences.length * 5, 50); // 每个差异扣5分，最多扣50分
    
    return Math.max(0, Math.round(avgSimilarity - diffPenalty));
  }

  /**
   * 生成统计信息
   */
  private generateStatistics(
    differences: DifferenceItem[],
    similarities: SimilarityItem[],
    clauseComparison: ClauseComparisonResult,
    processingTime: number
  ): ComparisonStatistics {
    const addedCount = differences.filter(d => d.type === DifferenceType.ADDED).length;
    const deletedCount = differences.filter(d => d.type === DifferenceType.DELETED).length;
    const modifiedCount = differences.filter(d => d.type === DifferenceType.MODIFIED).length;

    return {
      totalDifferences: differences.length,
      addedCount,
      deletedCount,
      modifiedCount,
      similarCount: similarities.length,
      processingTime,
      lengthComparison: {
        primary: 0, // 需要从实际文档获取
        secondary: 0,
        difference: 0,
        percentageChange: 0
      },
      clauseStatistics: {
        primaryCount: clauseComparison.primaryClauses.length,
        secondaryCount: clauseComparison.secondaryClauses.length,
        matchedCount: clauseComparison.matchedClauses.length,
        primaryOnlyCount: clauseComparison.primaryOnlyClauses.length,
        secondaryOnlyCount: clauseComparison.secondaryOnlyClauses.length
      }
    };
  }

  // ==================== 辅助方法 ====================

  private getDefaultOptions(): ComparisonOptions {
    return {
      ignoreFormatting: true,
      ignoreWhitespace: true,
      enableClauseComparison: true,
      similarityThreshold: 70,
      generateSuggestions: true,
      language: 'zh'
    };
  }

  private generateComparisonId(): string {
    return `comp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || '';
  }

  private generateFileHash(file: Express.Multer.File): string {
    // 简单的哈希生成，实际应该基于文件内容
    return `hash_${file.originalname}_${file.size}_${Date.now()}`;
  }

  private calculateDifferenceSeverity(similarity: number): DifferenceSeverity {
    if (similarity < 0.2) return DifferenceSeverity.CRITICAL;
    if (similarity < 0.5) return DifferenceSeverity.HIGH;
    if (similarity < 0.8) return DifferenceSeverity.MEDIUM;
    return DifferenceSeverity.LOW;
  }

  private calculateImpactLevel(similarity: number): ImpactLevel {
    if (similarity < 0.2) return ImpactLevel.HIGH;
    if (similarity < 0.5) return ImpactLevel.HIGH;
    if (similarity < 0.8) return ImpactLevel.MEDIUM;
    return ImpactLevel.LOW;
  }

  private assessImpactLevel(difference: DifferenceItem): ImpactLevel {
    // 根据差异类型和严重程度评估影响级别
    if (difference.severity === DifferenceSeverity.CRITICAL) {
      return ImpactLevel.HIGH;
    }
    if (difference.severity === DifferenceSeverity.HIGH) {
      return ImpactLevel.HIGH;
    }
    if (difference.severity === DifferenceSeverity.MEDIUM) {
      return ImpactLevel.MEDIUM;
    }
    return ImpactLevel.LOW;
  }

  private isHeading(text: string): boolean {
    // 简单的标题检测
    return /^第[一二三四五六七八九十\d]+[章节条款项]/.test(text) ||
           /^\d+(\.\d+)*\s/.test(text) ||
           (text.length < 50 && text.length > 3);
  }

  private getHeadingLevel(text: string): number {
    if (/^第[一二三四五六七八九十\d]+章/.test(text)) return 1;
    if (/^第[一二三四五六七八九十\d]+节/.test(text)) return 2;
    if (/^\d+\.\s/.test(text)) return 2;
    if (/^\d+\.\d+\s/.test(text)) return 3;
    return 2;
  }

  private identifyClauseType(content: string): ClauseType {
    const lowerContent = content.toLowerCase();
    
    if (lowerContent.includes('付款') || lowerContent.includes('支付')) return ClauseType.PAYMENT;
    if (lowerContent.includes('交付') || lowerContent.includes('交货')) return ClauseType.DELIVERY;
    if (lowerContent.includes('保修') || lowerContent.includes('保证')) return ClauseType.WARRANTY;
    if (lowerContent.includes('责任') || lowerContent.includes('赔偿')) return ClauseType.LIABILITY;
    if (lowerContent.includes('终止') || lowerContent.includes('解除')) return ClauseType.TERMINATION;
    if (lowerContent.includes('保密') || lowerContent.includes('机密')) return ClauseType.CONFIDENTIALITY;
    if (lowerContent.includes('争议') || lowerContent.includes('仲裁')) return ClauseType.DISPUTE;
    if (lowerContent.includes('不可抗力') || lowerContent.includes('天灾')) return ClauseType.FORCE_MAJEURE;
    if (lowerContent.includes('知识产权') || lowerContent.includes('专利')) return ClauseType.INTELLECTUAL_PROPERTY;
    
    return ClauseType.OTHER;
  }

  private extractClauseTitle(content: string): string {
    const lines = content.split('\n');
    const firstLine = lines[0].trim();
    
    // 如果第一行看起来像标题，使用它
    if (firstLine.length < 100 && (
      /^第[一二三四五六七八九十\d]+[章节条款项]/.test(firstLine) ||
      /^\d+(\.\d+)*\s/.test(firstLine)
    )) {
      return firstLine;
    }
    
    // 否则生成一个简短的标题
    return content.substring(0, 50) + (content.length > 50 ? '...' : '');
  }

  private assessClauseImportance(type: ClauseType): ImportanceLevel {
    switch (type) {
      case ClauseType.PAYMENT:
      case ClauseType.LIABILITY:
      case ClauseType.TERMINATION:
        return ImportanceLevel.CRITICAL;
      case ClauseType.DELIVERY:
      case ClauseType.WARRANTY:
      case ClauseType.CONFIDENTIALITY:
        return ImportanceLevel.HIGH;
      case ClauseType.DISPUTE:
      case ClauseType.FORCE_MAJEURE:
        return ImportanceLevel.MEDIUM;
      default:
        return ImportanceLevel.LOW;
    }
  }

  private extractKeywords(content: string): string[] {
    // 简单的关键词提取
    const keywords: string[] = [];
    const commonKeywords = [
      '合同', '协议', '甲方', '乙方', '付款', '交付', '保修', '责任', '终止', 
      '保密', '争议', '仲裁', '违约', '赔偿', '知识产权', '专利', '商标'
    ];
    
    commonKeywords.forEach(keyword => {
      if (content.includes(keyword)) {
        keywords.push(keyword);
      }
    });
    
    return keywords;
  }

  private findClauseDifferences(clause1: ClauseItem, clause2: ClauseItem): DifferenceItem[] {
    const differences: DifferenceItem[] = [];
    
    if (clause1.content !== clause2.content) {
      differences.push({
        id: `clause_diff_${Date.now()}`,
        type: DifferenceType.MODIFIED,
        severity: DifferenceSeverity.MEDIUM,
        primaryText: clause1.content,
        secondaryText: clause2.content,
        primaryPosition: clause1.position,
        secondaryPosition: clause2.position,
        description: '条款内容差异',
        suggestion: '建议仔细比较条款的具体内容',
        impact: ImpactLevel.MEDIUM
      });
    }
    
    return differences;
  }

  private createFileCompareError(
    type: FileCompareErrorType,
    message: string,
    context?: any
  ): FileCompareError {
    return {
      type,
      message,
      details: context ? JSON.stringify(context) : undefined,
      code: `FC_${type.toUpperCase()}`,
      timestamp: new Date(),
      fileName: context?.fileName
    };
  }
}