/**
 * Mock Database Service
 * 模拟数据库服务，使用内存存储和JSON文件持久化
 */
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import { getContractTypeLabel } from '../constants/contractTypes';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const DATA_DIR = path.join(__dirname, '../data');

// 数据模型接口定义
export interface User {
  id: string;
  email: string;
  password_hash: string;
  name: string;
  role: 'user' | 'legal_staff' | 'legal_manager' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface Contract {
  id: string;
  user_id: string;
  title: string;
  category: string;
  file_path: string;
  content?: string;
  ocr_content?: string;
  status: 'uploaded' | 'processing' | 'reviewed' | 'archived';
  created_at: string;
  updated_at: string;
}

export interface ReviewTask {
  id: string;
  contract_id: string;
  user_id: string;
  reviewer_id?: string;
  review_type: string;
  task_type: 'element_extraction' | 'risk_analysis' | 'compliance_check';
  analysis_result?: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  error_message?: string;
  risk_level?: string;
  risk_score?: number;
  result?: any;
  created_at: string;
  completed_at?: string;
}

export interface ContractElement {
  id: string;
  contract_id: string;
  element_type: string;
  element_name: string;
  element_value: string;
  content: string;
  is_present: boolean;
  description?: string;
  confidence_score: number;
  location?: string;
  extracted_at: string;
}

export interface RiskItem {
  id: string;
  contract_id: string;
  review_task_id: string;
  risk_type: string;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location?: string;
  suggestion?: string;
  auto_fixable: boolean;
  created_at: string;
  identified_at: string;
}

export interface Template {
  id: string;
  name: string;
  category: string;
  content: string;
  parameters?: any;
  is_active: boolean;
  created_at: string;
}

export interface KnowledgeBase {
  id: string;
  type: 'regulation' | 'case_law' | 'template' | 'guideline';
  title: string;
  content: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface ReviewRule {
  id: string;
  knowledge_base_id: string;
  rule_name: string;
  rule_content: string;
  rule_type: string;
  description: string;
  suggestion?: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  is_active: boolean;
}

// 内存数据存储
class MockDatabase {
  private users: User[] = [];
  private contracts: Contract[] = [];
  private reviewTasks: ReviewTask[] = [];
  private contractElements: ContractElement[] = [];
  private riskItems: RiskItem[] = [];
  private templates: Template[] = [];
  private knowledgeBase: KnowledgeBase[] = [];
  private reviewRules: ReviewRule[] = [];

  constructor() {
    this.initializeData();
  }

  // 初始化数据
  private async initializeData() {
    try {
      await this.ensureDataDirectory();
      await this.loadData();
      if (this.users.length === 0) {
        await this.seedInitialData();
      }
    } catch (error) {
      console.error('Failed to initialize mock database:', error);
    }
  }

  // 确保数据目录存在
  private async ensureDataDirectory() {
    try {
      await fs.access(DATA_DIR);
    } catch {
      await fs.mkdir(DATA_DIR, { recursive: true });
    }
  }

  // 从JSON文件加载数据
  private async loadData() {
    const tables = [
      'users', 'contracts', 'reviewTasks', 'contractElements',
      'riskItems', 'templates', 'knowledgeBase', 'reviewRules'
    ];

    for (const table of tables) {
      try {
        const filePath = path.join(DATA_DIR, `${table}.json`);
        const data = await fs.readFile(filePath, 'utf-8');
        (this as any)[table] = JSON.parse(data);
      } catch {
        // 文件不存在或格式错误，使用空数组
        (this as any)[table] = [];
      }
    }
  }

  // 保存数据到JSON文件
  private async saveData() {
    const tables = {
      users: this.users,
      contracts: this.contracts,
      reviewTasks: this.reviewTasks,
      contractElements: this.contractElements,
      riskItems: this.riskItems,
      templates: this.templates,
      knowledgeBase: this.knowledgeBase,
      reviewRules: this.reviewRules
    };

    for (const [table, data] of Object.entries(tables)) {
      try {
        const filePath = path.join(DATA_DIR, `${table}.json`);
        await fs.writeFile(filePath, JSON.stringify(data, null, 2));
      } catch (error) {
        console.error(`Failed to save ${table}:`, error);
      }
    }
  }

  // 生成UUID
  private generateId(): string {
    return uuidv4();
  }

  // 初始化种子数据
  private async seedInitialData() {
    const now = new Date().toISOString();

    // 创建默认用户 (密码都是: password123)
    const adminUser: User = {
      id: this.generateId(),
      email: '<EMAIL>',
      password_hash: '$2b$10$8dO9QGLEPoaasRRzMhw94.miTf1N.iTULxOjehI9/jT8DOFfqsz6.',
      name: '系统管理员',
      role: 'admin',
      created_at: now,
      updated_at: now
    };

    const legalManager: User = {
      id: this.generateId(),
      email: '<EMAIL>',
      password_hash: '$2b$10$8dO9QGLEPoaasRRzMhw94.miTf1N.iTULxOjehI9/jT8DOFfqsz6.',
      name: '法务主管',
      role: 'legal_manager',
      created_at: now,
      updated_at: now
    };

    const legalStaff: User = {
      id: this.generateId(),
      email: '<EMAIL>',
      password_hash: '$2b$10$8dO9QGLEPoaasRRzMhw94.miTf1N.iTULxOjehI9/jT8DOFfqsz6.',
      name: '法务专员',
      role: 'legal_staff',
      created_at: now,
      updated_at: now
    };

    this.users = [adminUser, legalManager, legalStaff];

    // 创建默认模板
    const serviceTemplate: Template = {
      id: this.generateId(),
      name: '标准服务合同模板',
      category: 'service',
      content: '甲方：[甲方名称]\n乙方：[乙方名称]\n\n根据《中华人民共和国合同法》等相关法律法规，甲乙双方在平等、自愿、公平、诚实信用的基础上，就服务事宜达成如下协议：\n\n第一条 服务内容\n乙方向甲方提供[服务内容]服务。\n\n第二条 服务期限\n服务期限为[服务期限]，自[开始日期]起至[结束日期]止。\n\n第三条 服务费用\n服务费用总计人民币[金额]元（大写：[大写金额]）。\n\n第四条 付款方式\n[付款方式说明]\n\n第五条 违约责任\n[违约责任条款]\n\n第六条 争议解决\n因履行本合同发生的争议，双方应友好协商解决；协商不成的，提交[仲裁机构]仲裁。\n\n第七条 其他\n本合同一式两份，甲乙双方各执一份，具有同等法律效力。\n\n甲方（盖章）：\n法定代表人：\n日期：\n\n乙方（盖章）：\n法定代表人：\n日期：',
      parameters: {
        fields: ['甲方名称', '乙方名称', '服务内容', '服务期限', '开始日期', '结束日期', '金额', '大写金额', '付款方式', '违约责任条款', '仲裁机构']
      },
      is_active: true,
      created_at: now
    };

    const purchaseTemplate: Template = {
      id: this.generateId(),
      name: '采购合同模板',
      category: 'purchase',
      content: '采购方：[采购方名称]\n供应方：[供应方名称]\n\n根据《中华人民共和国合同法》等相关法律法规，采购方与供应方就货物采购事宜达成如下协议：\n\n第一条 货物信息\n货物名称：[货物名称]\n规格型号：[规格型号]\n数量：[数量]\n单价：[单价]\n总价：[总价]\n\n第二条 交货\n交货地点：[交货地点]\n交货时间：[交货时间]\n\n第三条 质量标准\n[质量标准说明]\n\n第四条 验收\n[验收条款]\n\n第五条 付款\n[付款条款]\n\n第六条 违约责任\n[违约责任条款]\n\n采购方（盖章）：\n日期：\n\n供应方（盖章）：\n日期：',
      parameters: {
        fields: ['采购方名称', '供应方名称', '货物名称', '规格型号', '数量', '单价', '总价', '交货地点', '交货时间', '质量标准说明', '验收条款', '付款条款', '违约责任条款']
      },
      is_active: true,
      created_at: now
    };

    this.templates = [serviceTemplate, purchaseTemplate];

    // 创建知识库数据
    const contractLawKnowledge: KnowledgeBase = {
      id: this.generateId(),
      type: 'regulation',
      title: '合同法基本原则',
      content: '《中华人民共和国民法典》合同编规定了合同的基本原则：\n1. 平等原则：当事人在合同中的地位平等\n2. 自愿原则：当事人依法享有自愿订立合同的权利\n3. 公平原则：当事人应当遵循公平原则确定各方的权利和义务\n4. 诚实信用原则：当事人行使权利、履行义务应当遵循诚实信用原则\n5. 守法原则：当事人订立、履行合同，应当遵守法律、行政法规',
      metadata: {
        source: '中华人民共和国民法典',
        effective_date: '2021-01-01'
      },
      created_at: now,
      updated_at: now
    };

    const reviewGuideline: KnowledgeBase = {
      id: this.generateId(),
      type: 'guideline',
      title: '合同审查指导原则',
      content: '合同审查的基本指导原则：\n1. 合法性审查：确保合同内容符合法律法规\n2. 完整性审查：检查合同条款是否完整\n3. 明确性审查：确保合同条款表述清晰明确\n4. 可执行性审查：评估合同条款的可执行性\n5. 风险识别：识别潜在的法律风险和商业风险\n6. 利益平衡：确保各方权利义务平衡',
      metadata: {
        version: '1.0',
        department: '法务部'
      },
      created_at: now,
      updated_at: now
    };

    this.knowledgeBase = [contractLawKnowledge, reviewGuideline];

    // 创建审查规则
    const riskRule: ReviewRule = {
      id: this.generateId(),
      knowledge_base_id: contractLawKnowledge.id,
      rule_name: '违约责任条款检查',
      rule_content: '合同应当明确约定违约责任，包括违约情形、责任承担方式、损失赔偿等',
      rule_type: 'risk_analysis',
      description: '检查合同中违约责任条款的完整性和明确性',
      suggestion: '建议明确约定违约情形、责任承担方式和损失赔偿标准',
      severity: 'warning',
      is_active: true
    };

    const termRule: ReviewRule = {
      id: this.generateId(),
      knowledge_base_id: reviewGuideline.id,
      rule_name: '合同期限明确性检查',
      rule_content: '合同应当明确约定履行期限，避免使用模糊的时间表述',
      rule_type: 'compliance_check',
      description: '检查合同期限条款的明确性和可执行性',
      suggestion: '建议使用具体的日期或明确的时间节点',
      severity: 'error',
      is_active: true
    };

    this.reviewRules = [riskRule, termRule];

    await this.saveData();
  }

  // 用户相关操作
  async getUsers(): Promise<User[]> {
    return this.users;
  }

  async getUserById(id: string): Promise<User | null> {
    return this.users.find(user => user.id === id) || null;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return this.users.find(user => user.email === email) || null;
  }

  async createUser(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>): Promise<User> {
    const now = new Date().toISOString();
    const user: User = {
      id: this.generateId(),
      ...userData,
      created_at: now,
      updated_at: now
    };
    this.users.push(user);
    await this.saveData();
    return user;
  }

  // 合同相关操作
  async getContracts(userId?: string): Promise<Contract[]> {
    if (userId) {
      return this.contracts.filter(contract => contract.user_id === userId);
    }
    return this.contracts;
  }

  async getAllContracts(): Promise<Contract[]> {
    return this.contracts;
  }

  async getContractById(id: string): Promise<Contract | null> {
    return this.contracts.find(contract => contract.id === id) || null;
  }

  async createContract(contractData: Omit<Contract, 'id' | 'created_at' | 'updated_at'>): Promise<Contract> {
    const now = new Date().toISOString();
    const contract: Contract = {
      id: this.generateId(),
      ...contractData,
      created_at: now,
      updated_at: now
    };
    this.contracts.push(contract);
    await this.saveData();
    return contract;
  }

  async updateContract(id: string, updates: Partial<Contract>): Promise<Contract | null> {
    const index = this.contracts.findIndex(contract => contract.id === id);
    if (index === -1) return null;
    
    this.contracts[index] = {
      ...this.contracts[index],
      ...updates,
      updated_at: new Date().toISOString()
    };
    await this.saveData();
    return this.contracts[index];
  }

  // 审查任务相关操作
  async getReviewTasks(userId?: string): Promise<ReviewTask[]> {
    if (userId) {
      return this.reviewTasks.filter(task => task.user_id === userId);
    }
    return this.reviewTasks;
  }

  async createReviewTask(taskData: Omit<ReviewTask, 'id' | 'created_at'>): Promise<ReviewTask> {
    const task: ReviewTask = {
      id: this.generateId(),
      ...taskData,
      created_at: new Date().toISOString()
    };
    this.reviewTasks.push(task);
    await this.saveData();
    return task;
  }

  async updateReviewTask(id: string, updates: Partial<ReviewTask>): Promise<ReviewTask | null> {
    const index = this.reviewTasks.findIndex(task => task.id === id);
    if (index === -1) return null;
    
    this.reviewTasks[index] = {
      ...this.reviewTasks[index],
      ...updates
    };
    await this.saveData();
    return this.reviewTasks[index];
  }

  // 模板相关操作
  async getTemplates(): Promise<Template[]> {
    return this.templates.filter(template => template.is_active);
  }

  async getTemplateById(id: string): Promise<Template | null> {
    return this.templates.find(template => template.id === id) || null;
  }

  // 审查规则相关操作
  async getAllReviewRules(): Promise<ReviewRule[]> {
    return this.reviewRules.filter(rule => rule.is_active);
  }

  // 审查任务相关操作
  async getReviewTaskById(id: string): Promise<ReviewTask | null> {
    return this.reviewTasks.find(task => task.id === id) || null;
  }

  // 合同删除操作
  async deleteContract(id: string): Promise<void> {
    this.contracts = this.contracts.filter(contract => contract.id !== id);
    await this.saveData();
  }

  // 合同要素和风险项（模拟方法）
  async getContractElements(contractId: string): Promise<any[]> {
    // 模拟返回合同要素
    return [
      { type: 'party', content: '甲方：示例公司', confidence: 0.95 },
      { type: 'amount', content: '合同金额：100万元', confidence: 0.90 },
      { type: 'term', content: '合同期限：1年', confidence: 0.85 }
    ];
  }

  async getRiskItems(contractId: string): Promise<any[]> {
    // 模拟返回风险项
    return [
      { type: 'legal', description: '违约责任条款不够明确', severity: 'medium', suggestion: '建议明确违约责任的具体承担方式' },
      { type: 'commercial', description: '付款条件对己方不利', severity: 'high', suggestion: '建议调整付款周期' }
    ];
  }

  // 知识库相关操作
  async getKnowledgeBase(): Promise<KnowledgeBase[]> {
    return this.knowledgeBase;
  }

  async getReviewRules(): Promise<ReviewRule[]> {
    return this.reviewRules.filter(rule => rule.is_active);
  }

  // 统计数据
  async getStatistics(userId?: string): Promise<any> {
    const contracts = userId ? await this.getContracts(userId) : this.contracts;
    const reviewTasks = userId ? await this.getReviewTasks(userId) : this.reviewTasks;
    
    return {
      totalContracts: contracts.length,
      pendingReviews: reviewTasks.filter(task => task.status === 'pending').length,
      completedReviews: reviewTasks.filter(task => task.status === 'completed').length,
      contractsByCategory: this.groupBy(contracts, 'category'),
      contractsByStatus: this.groupBy(contracts, 'status'),
      riskDistribution: this.getRiskDistribution()
    };
  }

  private groupBy(array: any[], key: string): Record<string, number> {
    return array.reduce((acc, item) => {
      const value = item[key] || 'unknown';
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {});
  }

  private getRiskDistribution(): Record<string, number> {
    return this.groupBy(this.riskItems, 'risk_level');
  }
}

// 导出单例实例
export const mockDB = new MockDatabase();
export default mockDB;