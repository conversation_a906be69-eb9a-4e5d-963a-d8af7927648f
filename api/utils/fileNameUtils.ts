/**
 * 文件名处理工具
 * 处理文件名编码、验证和清理
 */

/**
 * 修复文件名编码问题
 * 处理中文文件名在HTTP传输过程中的编码异常
 */
export function fixFileNameEncoding(originalName: string): string {
  if (!originalName) return '';
  
  let fixedName = originalName;
  
  try {
    // 只在检测到编码问题时才输出日志
    const needsFixing = /[éèêëàáâãäåæçìíîïòóôõöøùúûüýÿñ]/.test(fixedName) || 
                       /\\x/.test(fixedName) || 
                       fixedName.includes('%');
    
    if (needsFixing) {
      console.log('🔧 [文件名修复] 开始修复文件名:', originalName);
    }
    
    // 方法1: 检测UTF-8被错误解释为Latin-1的情况
    // 这是最常见的中文文件名乱码问题
    if (/[éèêëàáâãäåæçìíîïòóôõöøùúûüýÿñ]/.test(fixedName)) {
      console.log('🔧 [文件名修复] 检测到可能的UTF-8编码问题，尝试修复...');
      
      try {
        // 将字符串重新编码为字节数组（假设它是Latin-1）
        const buffer = Buffer.from(fixedName, 'latin1');
        // 然后将这些字节重新解码为UTF-8
        const decodedName = buffer.toString('utf8');
        
        // 检查解码后的结果是否包含有效的中文字符
        if (decodedName && decodedName.length > 0 && !decodedName.includes('\uFFFD')) {
          // 进一步验证：检查是否包含中文字符范围
          const hasChinese = /[\u4e00-\u9fff]/.test(decodedName);
          if (hasChinese) {
            fixedName = decodedName;
            console.log('✅ [文件名修复] UTF-8编码修复成功:', fixedName);
          }
        }
      } catch (e) {
        // 静默处理失败，避免日志噪音
      }
    }
    
    // 方法2: 检查是否包含转义字符
    if (/\\x/.test(fixedName)) {
      // 尝试从Latin-1解码为UTF-8
      const buffer = Buffer.from(fixedName, 'latin1');
      const decodedName = buffer.toString('utf8');
      
      if (decodedName && decodedName.length > 0 && !decodedName.includes('\uFFFD')) {
        fixedName = decodedName;
      }
    }
    
    // 方法3: 尝试URL解码
    if (fixedName.includes('%')) {
      try {
        const urlDecoded = decodeURIComponent(fixedName);
        if (urlDecoded !== fixedName) {
          fixedName = urlDecoded;
        }
      } catch (e) {
        // 静默处理失败
      }
    }
    
    // 只在文件名确实被修复时才输出日志
    if (originalName !== fixedName && needsFixing) {
      console.log('🔧 [文件名修复] 修复完成:', {
        original: originalName,
        fixed: fixedName,
        changed: true
      });
    }
    
  } catch (error) {
    console.warn('⚠️ [文件名修复] 编码修复过程中出现异常:', error);
  }
  
  return fixedName;
}

/**
 * 清理文件名，移除非法字符
 */
export function sanitizeFileName(fileName: string): string {
  if (!fileName) return '';
  
  // 移除路径分隔符和其他危险字符
  let cleaned = fileName
    .replace(/[\/\\:*?"<>|]/g, '') // 移除文件系统非法字符
    .replace(/[\x00-\x1f\x7f]/g, '') // 移除控制字符
    .replace(/\s+/g, ' ') // 合并多个空格
    .trim();
  
  // 确保文件名不为空且有扩展名
  if (!cleaned || !cleaned.includes('.')) {
    return '';
  }
  
  // 限制文件名长度
  if (cleaned.length > 255) {
    const parts = cleaned.split('.');
    const extension = parts.pop() || '';
    const baseName = parts.join('.');
    const maxBaseLength = 255 - extension.length - 1;
    cleaned = baseName.substring(0, maxBaseLength) + '.' + extension;
  }
  
  return cleaned;
}

/**
 * 验证文件名是否有效
 */
export function validateFileName(fileName: string): { isValid: boolean; error?: string } {
  if (!fileName) {
    return { isValid: false, error: '文件名不能为空' };
  }
  
  // 检查是否包含扩展名
  if (!fileName.includes('.')) {
    return { isValid: false, error: '文件名必须包含扩展名' };
  }
  
  // 检查文件名长度
  if (fileName.length > 255) {
    return { isValid: false, error: '文件名过长（最大255字符）' };
  }
  
  // 检查是否包含非法字符
  const illegalChars = /[\/\\:*?"<>|]/;
  if (illegalChars.test(fileName)) {
    return { isValid: false, error: '文件名包含非法字符' };
  }
  
  // 检查是否为保留名称（Windows）
  const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
  const baseName = fileName.split('.')[0].toUpperCase();
  if (reservedNames.includes(baseName)) {
    return { isValid: false, error: '文件名为系统保留名称' };
  }
  
  return { isValid: true };
}

/**
 * 生成安全的文件名
 * 如果原文件名有问题，生成一个安全的替代名称
 */
export function generateSafeFileName(originalName: string, fallbackPrefix: string = 'file'): string {
  // 首先尝试修复编码
  let fixedName = fixFileNameEncoding(originalName);
  
  // 然后清理文件名
  let cleanedName = sanitizeFileName(fixedName);
  
  // 验证清理后的文件名
  const validation = validateFileName(cleanedName);
  
  if (validation.isValid) {
    return cleanedName;
  }
  
  // 如果仍然无效，生成一个安全的文件名
  const extension = originalName.includes('.') ? originalName.split('.').pop() : 'txt';
  const timestamp = Date.now();
  const safeFileName = `${fallbackPrefix}_${timestamp}.${extension}`;
  
  console.log('🔧 [文件名修复] 生成安全文件名:', {
    original: originalName,
    fixed: fixedName,
    cleaned: cleanedName,
    safe: safeFileName,
    reason: validation.error
  });
  
  return safeFileName;
}

/**
 * 完整的文件名处理流程
 */
export function processFileName(originalName: string): {
  processedName: string;
  wasFixed: boolean;
  originalName: string;
} {
  const fixedName = fixFileNameEncoding(originalName);
  const cleanedName = sanitizeFileName(fixedName);
  const validation = validateFileName(cleanedName);
  
  let finalName: string;
  let wasFixed = false;
  
  if (validation.isValid) {
    finalName = cleanedName;
    wasFixed = (originalName !== finalName);
  } else {
    finalName = generateSafeFileName(originalName);
    wasFixed = true;
  }
  
  // 只在文件名确实需要修复且修复成功时才输出日志
  if (wasFixed && originalName !== finalName) {
    console.log('🔧 [文件名处理] 文件名已修复:', {
      original: originalName,
      processed: finalName
    });
  }
  
  return {
    processedName: finalName,
    wasFixed,
    originalName
  };
}