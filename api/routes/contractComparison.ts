/**
 * 合同对比API路由
 * 
 * 提供合同对比功能的RESTful API接口
 * 支持文件上传、对比执行、结果获取等功能
 */

import express from 'express';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import { ContractComparisonService } from '../services/contractComparisonService';
import { authenticateToken } from '../middleware/auth';
import type {
  ComparisonResult,
  ContractComparisonError,
  ContractComparisonErrorType,
  ApiResponse
} from '../../src/types/contractComparison';

const router = express.Router();

// 创建合同对比服务实例
const contractComparisonService = new ContractComparisonService();

// 配置multer用于文件上传
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB per file
    files: 2 // 最多2个文件
  },
  fileFilter: (req, file, cb) => {
    // 检查文件类型
    const allowedMimeTypes = [
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/pdf'
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`不支持的文件类型: ${file.mimetype}`));
    }
  }
});

// 存储对比会话的临时缓存
const comparisonSessions = new Map<string, {
  primaryFile?: Express.Multer.File;
  secondaryFile?: Express.Multer.File;
  result?: ComparisonResult;
  createdAt: Date;
}>();

// 清理过期会话的定时器（每小时清理一次）
setInterval(() => {
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  
  for (const [sessionId, session] of comparisonSessions.entries()) {
    if (session.createdAt < oneHourAgo) {
      comparisonSessions.delete(sessionId);
      console.log(`🧹 [会话清理] 清理过期会话: ${sessionId}`);
    }
  }
}, 60 * 60 * 1000); // 每小时执行一次

/**
 * 文件上传端点
 * POST /api/contract-comparison/upload
 */
router.post('/upload', authenticateToken, upload.fields([
  { name: 'primaryFile', maxCount: 1 },
  { name: 'secondaryFile', maxCount: 1 }
]), async (req, res) => {
  const requestId = uuidv4();
  console.log(`📤 [文件上传] [${requestId}] 开始处理文件上传`);
  
  try {
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    
    // 验证文件是否存在
    if (!files || !files.primaryFile || !files.secondaryFile) {
      return res.status(400).json({
        success: false,
        message: '请上传两个文件进行对比（primaryFile 和 secondaryFile）',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    const primaryFile = files.primaryFile[0];
    const secondaryFile = files.secondaryFile[0];
    
    console.log(`📄 [文件上传] [${requestId}] 接收到文件:`, {
      primary: primaryFile.originalname,
      secondary: secondaryFile.originalname,
      primarySize: `${(primaryFile.size / 1024 / 1024).toFixed(2)}MB`,
      secondarySize: `${(secondaryFile.size / 1024 / 1024).toFixed(2)}MB`
    });
    
    // 验证文件格式
    if (!contractComparisonService.validateFileFormat(primaryFile)) {
      return res.status(400).json({
        success: false,
        message: `主文件格式不支持: ${primaryFile.originalname}`,
        timestamp: new Date()
      } as ApiResponse);
    }
    
    if (!contractComparisonService.validateFileFormat(secondaryFile)) {
      return res.status(400).json({
        success: false,
        message: `副文件格式不支持: ${secondaryFile.originalname}`,
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 验证文件大小
    if (!contractComparisonService.validateFileSize(primaryFile)) {
      return res.status(400).json({
        success: false,
        message: `主文件过大: ${primaryFile.originalname} (最大50MB)`,
        timestamp: new Date()
      } as ApiResponse);
    }
    
    if (!contractComparisonService.validateFileSize(secondaryFile)) {
      return res.status(400).json({
        success: false,
        message: `副文件过大: ${secondaryFile.originalname} (最大50MB)`,
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 创建会话
    const sessionId = uuidv4();
    comparisonSessions.set(sessionId, {
      primaryFile,
      secondaryFile,
      createdAt: new Date()
    });
    
    console.log(`✅ [文件上传] [${requestId}] 文件上传成功，会话ID: ${sessionId}`);
    
    res.json({
      success: true,
      data: {
        sessionId,
        files: {
          primary: {
            name: primaryFile.originalname,
            size: primaryFile.size,
            type: primaryFile.mimetype
          },
          secondary: {
            name: secondaryFile.originalname,
            size: secondaryFile.size,
            type: secondaryFile.mimetype
          }
        }
      },
      message: '文件上传成功',
      timestamp: new Date()
    } as ApiResponse);
    
  } catch (error) {
    console.error(`❌ [文件上传] [${requestId}] 上传失败:`, error);
    
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '文件上传失败',
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * 对比执行端点
 * POST /api/contract-comparison/compare
 */
router.post('/compare', authenticateToken, async (req, res) => {
  const requestId = uuidv4();
  console.log(`🔍 [对比执行] [${requestId}] 开始执行对比`);
  
  try {
    const { sessionId, options = {} } = req.body;
    
    // 验证会话ID
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: '缺少会话ID',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    // 获取会话数据
    const session = comparisonSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: '会话不存在或已过期',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    if (!session.primaryFile || !session.secondaryFile) {
      return res.status(400).json({
        success: false,
        message: '会话中缺少文件数据',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    console.log(`📊 [对比执行] [${requestId}] 开始对比文档:`, {
      sessionId,
      primary: session.primaryFile.originalname,
      secondary: session.secondaryFile.originalname,
      options
    });
    
    // 执行对比
    const comparisonResult = await contractComparisonService.compareContracts(
      session.primaryFile,
      session.secondaryFile,
      options
    );
    
    // 保存结果到会话
    session.result = comparisonResult;
    
    console.log(`✅ [对比执行] [${requestId}] 对比完成:`, {
      sessionId: comparisonResult.sessionId,
      similarity: `${comparisonResult.similarity}%`,
      differences: comparisonResult.differences.length,
      status: comparisonResult.status
    });
    
    res.json({
      success: true,
      data: comparisonResult,
      message: '对比完成',
      timestamp: new Date()
    } as ApiResponse<ComparisonResult>);
    
  } catch (error) {
    console.error(`❌ [对比执行] [${requestId}] 对比失败:`, error);
    
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '对比执行失败',
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * 结果获取端点
 * GET /api/contract-comparison/result/:sessionId
 */
router.get('/result/:sessionId', authenticateToken, async (req, res) => {
  const requestId = uuidv4();
  const { sessionId } = req.params;
  
  console.log(`📋 [结果获取] [${requestId}] 获取对比结果: ${sessionId}`);
  
  try {
    // 获取会话数据
    const session = comparisonSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        message: '会话不存在或已过期',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    if (!session.result) {
      return res.status(404).json({
        success: false,
        message: '对比结果不存在，请先执行对比',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    console.log(`✅ [结果获取] [${requestId}] 返回对比结果: ${sessionId}`);
    
    res.json({
      success: true,
      data: session.result,
      message: '获取结果成功',
      timestamp: new Date()
    } as ApiResponse<ComparisonResult>);
    
  } catch (error) {
    console.error(`❌ [结果获取] [${requestId}] 获取失败:`, error);
    
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取结果失败',
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * 会话清理端点
 * DELETE /api/contract-comparison/cleanup/:sessionId
 */
router.delete('/cleanup/:sessionId', authenticateToken, async (req, res) => {
  const requestId = uuidv4();
  const { sessionId } = req.params;
  
  console.log(`🧹 [会话清理] [${requestId}] 清理会话: ${sessionId}`);
  
  try {
    const deleted = comparisonSessions.delete(sessionId);
    
    if (deleted) {
      console.log(`✅ [会话清理] [${requestId}] 会话清理成功: ${sessionId}`);
      res.json({
        success: true,
        message: '会话清理成功',
        timestamp: new Date()
      } as ApiResponse);
    } else {
      res.status(404).json({
        success: false,
        message: '会话不存在',
        timestamp: new Date()
      } as ApiResponse);
    }
    
  } catch (error) {
    console.error(`❌ [会话清理] [${requestId}] 清理失败:`, error);
    
    res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '会话清理失败',
      timestamp: new Date()
    } as ApiResponse);
  }
});

/**
 * 健康检查端点
 * GET /api/contract-comparison/health
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      activeSessions: comparisonSessions.size,
      uptime: process.uptime(),
      memory: process.memoryUsage()
    },
    message: '服务正常',
    timestamp: new Date()
  } as ApiResponse);
});

// 错误处理中间件
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('❌ [API错误]', error);
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大50MB）',
        timestamp: new Date()
      } as ApiResponse);
    }
    
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: '文件数量超过限制（最多2个文件）',
        timestamp: new Date()
      } as ApiResponse);
    }
  }
  
  res.status(500).json({
    success: false,
    message: error.message || '服务器内部错误',
    timestamp: new Date()
  } as ApiResponse);
});

export default router;
