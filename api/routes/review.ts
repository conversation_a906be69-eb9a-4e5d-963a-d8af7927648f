/**
 * Review Routes
 * 智能审查相关路由
 */
import express from 'express';
import multer from 'multer';
import fs from 'fs';
import crypto from 'crypto';
import { supabaseService } from '../services/supabaseService.js';
import { reviewService } from '../services/reviewService.js';
import { authenticateToken, requireLegalStaff } from '../middleware/auth.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import { FileCompareService } from '../services/fileCompareService.js';
import { documentParserService } from '../services/documentParserService.js';
import { 
  fileCompareErrorHandler as fileCompareErrorMiddleware,
  validateFileCompareRequest,
  asyncFileCompareHandler,
  fileCompareErrorFactory,
  FileCompareErrorFormatter
} from '../middleware/fileCompareErrorHandler.js';
import { processFileName } from '../utils/fileNameUtils.js';

// 创建FileCompareService实例
const fileCompareService = new FileCompareService();

// 创建ReviewTaskService别名以兼容现有代码
const ReviewTaskService = {
  startReview: reviewService.startReview.bind(reviewService),
  getReviewResult: reviewService.getReviewResult.bind(reviewService)
};

const router = express.Router();

// 配置multer用于文件对比（支持双文件上传）
const storage = multer.memoryStorage();
const fileCompareUpload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB per file
    files: 2 // 最多2个文件
  },
  fileFilter: (req, file, cb) => {
    console.log('🔍 [FileCompare] 文件过滤器被调用:', {
      fieldname: file.fieldname,
      originalname: file.originalname,
      mimetype: file.mimetype,
      timestamp: new Date().toISOString()
    });

    // 允许的文件类型
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/tiff'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      console.log('✅ [FileCompare] 文件类型验证通过:', file.mimetype);
      cb(null, true);
    } else {
      console.error('❌ [FileCompare] 不支持的文件类型:', file.mimetype);
      cb(new Error(`不支持的文件类型: ${file.mimetype}`));
    }
  }
});

// multer错误处理中间件（重命名以避免冲突）
const multerErrorHandler = (error: any, req: any, res: any, next: any) => {
  console.error('💥 [FileCompare] 文件上传错误:', {
    error: error.message,
    code: error.code,
    field: error.field,
    timestamp: new Date().toISOString()
  });
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大10MB）'
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: '文件数量超过限制（最多2个文件）'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: '意外的文件字段'
      });
    }
  }
  
  return res.status(400).json({
    success: false,
    message: error.message || '文件上传失败'
  });
};

// 文件验证中间件
const validateFileCompareUpload = (req: any, res: any, next: any) => {
  console.log('🔍 [FileCompare] 验证上传的文件:', {
    files: req.files,
    fileCount: req.files ? Object.keys(req.files).length : 0,
    timestamp: new Date().toISOString()
  });

  if (!req.files || !req.files.primaryFile || !req.files.secondaryFile) {
    return res.status(400).json({
      success: false,
      message: '请上传两个文件进行对比（primaryFile 和 secondaryFile）'
    });
  }

  const primaryFile = req.files.primaryFile[0];
  const secondaryFile = req.files.secondaryFile[0];

  if (!primaryFile || !secondaryFile) {
    return res.status(400).json({
      success: false,
      message: '请确保上传了主文件和对比文件'
    });
  }

  // 验证文件大小
  if (primaryFile.size === 0 || secondaryFile.size === 0) {
    return res.status(400).json({
      success: false,
      message: '文件不能为空'
    });
  }

  console.log('✅ [FileCompare] 文件验证通过:', {
    primaryFile: {
      name: primaryFile.originalname,
      size: primaryFile.size,
      type: primaryFile.mimetype
    },
    secondaryFile: {
      name: secondaryFile.originalname,
      size: secondaryFile.size,
      type: secondaryFile.mimetype
    }
  });

  next();
};

// ==================== 文件对比辅助函数 ====================

/**
 * 验证文件内容的完整性和安全性
 */
async function validateFileContent(
  primaryFile: Express.Multer.File, 
  secondaryFile: Express.Multer.File, 
  requestId: string
): Promise<{ isValid: boolean; error?: string }> {
  try {
    console.log(`🔍 [FileCompare] [${requestId}] 开始文件内容验证...`);

    // 1. 基础验证
    if (!primaryFile.buffer || !secondaryFile.buffer) {
      return { isValid: false, error: '文件缓冲区为空' };
    }

    if (primaryFile.buffer.length === 0 || secondaryFile.buffer.length === 0) {
      return { isValid: false, error: '文件内容为空' };
    }

    // 2. 文件大小合理性检查
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (primaryFile.size > maxSize || secondaryFile.size > maxSize) {
      return { isValid: false, error: `文件大小超过限制 (${maxSize / 1024 / 1024}MB)` };
    }

    // 3. 文件类型一致性检查（可选，允许不同类型对比）
    const supportedTypes = [
      'application/pdf',
      'application/msword', 
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/tiff'
    ];

    if (!supportedTypes.includes(primaryFile.mimetype) || !supportedTypes.includes(secondaryFile.mimetype)) {
      return { isValid: false, error: '包含不支持的文件类型' };
    }

    // 4. 文件头验证（简单的魔数检查）
    const primaryHeader = primaryFile.buffer.slice(0, 8);
    const secondaryHeader = secondaryFile.buffer.slice(0, 8);
    
    // PDF文件头检查
    if (primaryFile.mimetype === 'application/pdf' && !primaryHeader.toString().startsWith('%PDF')) {
      return { isValid: false, error: '主文件PDF格式无效' };
    }
    if (secondaryFile.mimetype === 'application/pdf' && !secondaryHeader.toString().startsWith('%PDF')) {
      return { isValid: false, error: '对比文件PDF格式无效' };
    }

    console.log(`✅ [FileCompare] [${requestId}] 文件内容验证通过`);
    return { isValid: true };

  } catch (error) {
    console.error(`❌ [FileCompare] [${requestId}] 文件内容验证失败:`, error);
    return { 
      isValid: false, 
      error: `文件验证异常: ${error instanceof Error ? error.message : '未知错误'}` 
    };
  }
}

/**
 * 解析和验证对比选项
 */
function parseComparisonOptions(body: any, requestId: string): any {
  console.log(`⚙️ [FileCompare] [${requestId}] 解析对比选项:`, body);

  const options = {
    ignoreFormatting: body.ignoreFormatting === 'true' || body.ignoreFormatting === true,
    ignoreWhitespace: body.ignoreWhitespace === 'true' || body.ignoreWhitespace === true,
    enableClauseComparison: body.enableClauseComparison === 'true' || body.enableClauseComparison === true,
    similarityThreshold: Math.max(0, Math.min(100, parseInt(body.similarityThreshold) || 70)),
    generateSuggestions: body.generateSuggestions === 'true' || body.generateSuggestions === true,
    language: body.language || 'zh'
  };

  // 验证选项合理性
  if (options.similarityThreshold < 0 || options.similarityThreshold > 100) {
    options.similarityThreshold = 70;
  }

  if (!['zh', 'en', 'auto'].includes(options.language)) {
    options.language = 'zh';
  }

  console.log(`✅ [FileCompare] [${requestId}] 对比选项解析完成:`, options);
  return options;
}

/**
 * 验证文件是否可以被解析
 */
async function validateFilesParseable(
  primaryFile: Express.Multer.File, 
  secondaryFile: Express.Multer.File, 
  requestId: string
): Promise<{ isValid: boolean; error?: string }> {
  try {
    console.log(`🔍 [FileCompare] [${requestId}] 开始文件解析能力验证...`);

    // 尝试解析主文件
    const primaryParseResult = await documentParserService.parseDocument(
      primaryFile.buffer,
      primaryFile.mimetype,
      primaryFile.originalname
    );

    if (!primaryParseResult.success) {
      return { 
        isValid: false, 
        error: `主文件解析失败: ${primaryParseResult.error}` 
      };
    }

    // 尝试解析对比文件
    const secondaryParseResult = await documentParserService.parseDocument(
      secondaryFile.buffer,
      secondaryFile.mimetype,
      secondaryFile.originalname
    );

    if (!secondaryParseResult.success) {
      return { 
        isValid: false, 
        error: `对比文件解析失败: ${secondaryParseResult.error}` 
      };
    }

    // 检查解析结果是否有足够的内容 - 双轨制架构
    if (!primaryParseResult.displayContent || primaryParseResult.displayContent.trim().length < 10) {
      return {
        isValid: false,
        error: '主文件内容过少或无法提取有效文本'
      };
    }

    if (!secondaryParseResult.displayContent || secondaryParseResult.displayContent.trim().length < 10) {
      return {
        isValid: false,
        error: '对比文件内容过少或无法提取有效文本'
      };
    }

    console.log(`✅ [FileCompare] [${requestId}] 文件解析能力验证通过（双轨制）:`, {
      primaryDisplayContentLength: primaryParseResult.displayContent.length,
      primaryCompareContentLength: primaryParseResult.compareContent.length,
      secondaryDisplayContentLength: secondaryParseResult.displayContent.length,
      secondaryCompareContentLength: secondaryParseResult.compareContent.length
    });

    return { isValid: true };

  } catch (error) {
    console.error(`❌ [FileCompare] [${requestId}] 文件解析能力验证失败:`, error);
    return { 
      isValid: false, 
      error: `解析验证异常: ${error instanceof Error ? error.message : '未知错误'}` 
    };
  }
}

/**
 * 格式化对比响应数据
 */
function formatComparisonResponse(
  comparisonResult: any,
  primaryFile: Express.Multer.File,
  secondaryFile: Express.Multer.File,
  options: any,
  metadata: any
): any {
  return {
    // 核心对比结果
    sessionId: comparisonResult.sessionId,
    overallSimilarity: comparisonResult.overallSimilarity,
    differences: comparisonResult.differences,
    similarities: comparisonResult.similarities,
    clauseComparison: comparisonResult.clauseComparison,
    statistics: comparisonResult.statistics,
    status: comparisonResult.status,

    // 文件内容 - 添加缺失的内容数据
    primaryContent: comparisonResult.primaryContent,
    secondaryContent: comparisonResult.secondaryContent,

    // 文件元数据（使用已处理的文件名，避免重复调用）
    files: {
      primary: {
        name: metadata.processedPrimaryName?.processedName || primaryFile.originalname,
        originalName: primaryFile.originalname,
        size: primaryFile.size,
        type: primaryFile.mimetype,
        encoding: primaryFile.encoding
      },
      secondary: {
        name: metadata.processedSecondaryName?.processedName || secondaryFile.originalname,
        originalName: secondaryFile.originalname,
        size: secondaryFile.size,
        type: secondaryFile.mimetype,
        encoding: secondaryFile.encoding
      }
    },

    // 对比选项
    options,

    // 处理元数据
    metadata: {
      requestId: metadata.requestId,
      userId: metadata.userId,
      timestamp: metadata.timestamp,
      performance: {
        comparisonTime: metadata.comparisonTime,
        totalProcessingTime: metadata.totalProcessingTime
      }
    }
  };
}

/**
 * 格式化错误响应
 */
function formatErrorResponse(
  error: any, 
  requestId: string, 
  userId?: string
): { statusCode: number; body: any } {
  const errorMessage = error instanceof Error ? error.message : '未知错误';
  
  // 根据错误类型确定状态码
  let statusCode = 500;
  if (errorMessage.includes('文件验证失败') || 
      errorMessage.includes('文件上传不完整') ||
      errorMessage.includes('缺少必需的文件')) {
    statusCode = 400;
  } else if (errorMessage.includes('解析失败') || 
             errorMessage.includes('不支持的文件类型')) {
    statusCode = 422;
  }

  return {
    statusCode,
    body: {
      success: false,
      message: '文件对比处理失败',
      error: errorMessage,
      requestId,
      details: {
        timestamp: new Date().toISOString(),
        userId,
        errorType: error.constructor?.name || 'UnknownError'
      }
    }
  };
}

// ==================== 文件对比API路由 ====================

/**
 * 文件对比API - 上传两个文件进行对比分析
 * POST /api/review/file-compare
 */
router.post('/file-compare', 
  authenticateToken, 
  fileCompareUpload.fields([
    { name: 'primaryFile', maxCount: 1 },
    { name: 'secondaryFile', maxCount: 1 }
  ]), 
  multerErrorHandler, 
  validateFileCompareUpload, 
  fileCompareErrorMiddleware,
  asyncFileCompareHandler(async (req, res) => {
    const requestId = crypto.randomUUID();
    const startTime = Date.now();
    
    console.log('🚀 [FileCompare] === 文件对比API被调用 ===');
    console.log(`📥 [FileCompare] [${requestId}] 请求信息:`, {
      userId: req.user?.userId,
      userRole: req.user?.role,
      timestamp: new Date().toISOString(),
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    // 临时文件清理函数
    const cleanupTempFiles = () => {
      try {
        const files = req.files as { [fieldname: string]: Express.Multer.File[] };
        if (files) {
          [files.primaryFile?.[0], files.secondaryFile?.[0]].forEach(file => {
            if (file?.path && fs.existsSync(file.path)) {
              fs.unlinkSync(file.path);
              console.log(`🗑️ [FileCompare] [${requestId}] 临时文件已清理: ${file.path}`);
            }
          });
        }
      } catch (cleanupError) {
        console.error(`⚠️ [FileCompare] [${requestId}] 临时文件清理失败:`, cleanupError);
      }
    };

    try {
      // 1. 文件验证和提取
      const files = req.files as { [fieldname: string]: Express.Multer.File[] };
      
      if (!files || !files.primaryFile || !files.secondaryFile) {
        throw new Error('缺少必需的文件：需要上传主文件和对比文件');
      }

      const primaryFile = files.primaryFile[0];
      const secondaryFile = files.secondaryFile[0];

      if (!primaryFile || !secondaryFile) {
        throw new Error('文件上传不完整：主文件或对比文件缺失');
      }

      // 处理文件名编码问题（避免重复调用）
      const processedPrimaryName = processFileName(primaryFile.originalname);
      const processedSecondaryName = processFileName(secondaryFile.originalname);
      
      console.log(`📁 [FileCompare] [${requestId}] 处理文件:`, {
        primary: {
          name: processedPrimaryName.processedName,
          originalName: primaryFile.originalname,
          wasFixed: processedPrimaryName.wasFixed,
          size: primaryFile.size,
          type: primaryFile.mimetype,
          encoding: primaryFile.encoding
        },
        secondary: {
          name: processedSecondaryName.processedName,
          originalName: secondaryFile.originalname,
          wasFixed: processedSecondaryName.wasFixed,
          size: secondaryFile.size,
          type: secondaryFile.mimetype,
          encoding: secondaryFile.encoding
        }
      });

      // 2. 高级文件验证
      const validationResult = await validateFileContent(primaryFile, secondaryFile, requestId);
      if (!validationResult.isValid) {
        throw new Error(`文件验证失败: ${validationResult.error}`);
      }

      // 3. 解析和验证对比选项
      const options = parseComparisonOptions(req.body, requestId);
      console.log(`⚙️ [FileCompare] [${requestId}] 对比选项:`, options);

      // 4. 文件解析预检查
      console.log(`🔍 [FileCompare] [${requestId}] 开始文件解析预检查...`);
      const parseValidation = await validateFilesParseable(primaryFile, secondaryFile, requestId);
      if (!parseValidation.isValid) {
        throw new Error(`文件解析预检查失败: ${parseValidation.error}`);
      }

      // 5. 执行文件对比
      console.log(`🔄 [FileCompare] [${requestId}] 开始执行文件对比...`);
      const comparisonStartTime = Date.now();
      
      const comparisonResult = await fileCompareService.compareFiles(
        primaryFile,
        secondaryFile,
        options
      );
      
      const comparisonTime = Date.now() - comparisonStartTime;
      const totalProcessingTime = Date.now() - startTime;

      console.log(`✅ [FileCompare] [${requestId}] 对比完成:`, {
         sessionId: comparisonResult.sessionId,
         overallSimilarity: comparisonResult.overallSimilarity,
         differencesCount: comparisonResult.differences.length,
         similaritiesCount: comparisonResult.similarities.length,
         comparisonTime: `${comparisonTime}ms`,
         totalProcessingTime: `${totalProcessingTime}ms`
       });

      // 6. 格式化响应数据
      const responseData = formatComparisonResponse(
        comparisonResult,
        primaryFile,
        secondaryFile,
        options,
        {
          requestId,
          comparisonTime,
          totalProcessingTime,
          userId: req.user?.userId,
          timestamp: new Date().toISOString(),
          processedPrimaryName,
          processedSecondaryName
        }
      );

      // 7. 清理临时文件
      cleanupTempFiles();

      // 8. 返回标准化响应
      res.json({
        success: true,
        message: '文件对比完成',
        requestId,
        data: responseData,
        performance: {
          comparisonTime,
          totalProcessingTime,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      console.error(`💥 [FileCompare] [${requestId}] 对比处理错误:`, {
        error: error instanceof Error ? error.message : '未知错误',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });
      
      // 确保清理临时文件
      cleanupTempFiles();
      
      // 标准化错误响应
      const errorResponse = formatErrorResponse(error, requestId, req.user?.userId);
      res.status(errorResponse.statusCode).json(errorResponse.body);
    }
  })
);

/**
 * 获取支持的文件类型信息
 * GET /api/review/file-compare/supported-types
 */
router.get('/file-compare/supported-types', 
  authenticateToken,
  asyncHandler(async (req, res) => {
    const supportedTypes = {
      documents: [
        {
          type: 'application/pdf',
          extension: '.pdf',
          description: 'PDF文档'
        },
        {
          type: 'application/msword',
          extension: '.doc',
          description: 'Microsoft Word文档（旧版）'
        },
        {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          extension: '.docx',
          description: 'Microsoft Word文档'
        },
        {
          type: 'text/plain',
          extension: '.txt',
          description: '纯文本文件'
        }
      ],
      images: [
        {
          type: 'image/jpeg',
          extension: '.jpg, .jpeg',
          description: 'JPEG图片'
        },
        {
          type: 'image/png',
          extension: '.png',
          description: 'PNG图片'
        },
        {
          type: 'image/gif',
          extension: '.gif',
          description: 'GIF图片'
        },
        {
          type: 'image/bmp',
          extension: '.bmp',
          description: 'BMP图片'
        },
        {
          type: 'image/tiff',
          extension: '.tiff, .tif',
          description: 'TIFF图片'
        }
      ],
      limits: {
        maxFileSize: '10MB',
        maxFiles: 2,
        note: '每次对比最多上传2个文件，每个文件最大10MB'
      }
    };

    res.json({
      success: true,
      message: '获取支持的文件类型成功',
      data: supportedTypes
    });
  })
);

// 合同对比核心函数
async function performContractComparison(
  contract1: any,
  contract2: any,
  comparisonType: string,
  focusAreas?: string[]
) {
  // 模拟合同条款提取和对比逻辑
  const differences = [
    {
      clause_type: '付款条款',
      contract1_content: contract1.content ? '合同A规定30天内付款' : '付款条款不明确',
      contract2_content: contract2.content ? '合同B规定15天内付款' : '付款条款不明确',
      difference_type: 'different' as const,
      severity: 'medium' as const,
      description: '两个合同的付款期限不同',
      recommendation: '建议统一付款期限以避免混淆'
    },
    {
      clause_type: '违约责任',
      contract1_content: '违约金为合同总额的10%',
      contract2_content: '',
      difference_type: 'missing' as const,
      severity: 'high' as const,
      description: '合同B缺少违约责任条款',
      recommendation: '建议在合同B中添加明确的违约责任条款'
    },
    {
      clause_type: '保密条款',
      contract1_content: '',
      contract2_content: '双方应对商业机密保密',
      difference_type: 'additional' as const,
      severity: 'low' as const,
      description: '合同B包含额外的保密条款',
      recommendation: '考虑在合同A中也添加保密条款'
    }
  ];

  const similarities = [
    {
      clause_type: '合同期限',
      content: '合同期限均为一年',
      match_percentage: 95
    },
    {
      clause_type: '服务范围',
      content: '提供技术开发服务',
      match_percentage: 88
    },
    {
      clause_type: '知识产权',
      content: '开发成果归委托方所有',
      match_percentage: 92
    }
  ];

  // 根据对比类型过滤结果
  let filteredDifferences = differences;
  if (comparisonType === 'key_clauses') {
    filteredDifferences = differences.filter(d => 
      ['付款条款', '违约责任', '知识产权'].includes(d.clause_type)
    );
  } else if (comparisonType === 'risk_clauses') {
    filteredDifferences = differences.filter(d => 
      d.severity === 'high' || d.severity === 'medium'
    );
  }

  // 如果指定了关注领域，进一步过滤
  if (focusAreas && focusAreas.length > 0) {
    filteredDifferences = filteredDifferences.filter(d => 
      focusAreas.some(area => d.clause_type.includes(area))
    );
  }

  // 计算相似度分数
  const totalClauses = differences.length + similarities.length;
  const similarityScore = Math.round(
    (similarities.length / totalClauses) * 100
  );

  // 风险评估
  const highRiskCount = filteredDifferences.filter(d => d.severity === 'high').length;
  const mediumRiskCount = filteredDifferences.filter(d => d.severity === 'medium').length;
  
  let riskAssessment = 'low';
  if (highRiskCount > 0) {
    riskAssessment = 'high';
  } else if (mediumRiskCount > 1) {
    riskAssessment = 'medium';
  }

  return {
    id: `comp-${Date.now()}`,
    contract1_id: contract1.id,
    contract2_id: contract2.id,
    differences: filteredDifferences,
    similarities,
    summary: {
      total_clauses_compared: totalClauses,
      differences_found: filteredDifferences.length,
      similarity_score: similarityScore,
      risk_assessment: riskAssessment
    },
    created_at: new Date().toISOString()
  };
}

// 接口定义
interface ReviewRequest {
  contract_id: string;
  review_type: 'full' | 'quick' | 'custom';
  focus_areas?: string[];
  custom_rules?: any[];
}

interface ComparisonRequest {
  contract1_id: string;
  contract2_id: string;
  comparison_type: 'full' | 'key_clauses' | 'risk_clauses';
  focus_areas?: string[];
}

// 开始智能审查
router.post('/start', authenticateToken, async (req, res) => {
  try {
    const reviewRequest: ReviewRequest = {
      contract_id: req.body.contract_id,
      review_type: req.body.review_type || 'full',
      focus_areas: req.body.focus_areas,
      custom_rules: req.body.custom_rules
    };

    // 验证必填字段
    if (!reviewRequest.contract_id) {
      return res.status(400).json({
        success: false,
        message: '合同ID为必填项'
      });
    }

    // 验证审查类型
    const validTypes = ['full', 'quick', 'custom'];
    if (!validTypes.includes(reviewRequest.review_type)) {
      return res.status(400).json({
        success: false,
        message: '无效的审查类型'
      });
    }

    const reviewTask = await supabaseService.createReviewTask({
      contract_id: reviewRequest.contract_id,
      user_id: req.user!.userId,
      reviewer_id: req.user!.userId,
      review_type: reviewRequest.review_type,
      status: 'pending',
      focus_areas: reviewRequest.focus_areas,
      custom_rules: reviewRequest.custom_rules
    });
    
    res.status(201).json({
      success: true,
      task_id: reviewTask.id,
      message: '审查任务已创建'
    });
  } catch (error) {
    console.error('Start review route error:', error);
    res.status(500).json({
      success: false,
      message: '启动审查失败'
    });
  }
});

// 获取审查结果
router.get('/result/:taskId', authenticateToken, async (req, res) => {
  try {
    const taskId = req.params.taskId;
    
    if (!taskId) {
      return res.status(400).json({
        success: false,
        message: '任务ID为必填项'
      });
    }

    const reviewTask = await supabaseService.getReviewTaskById(taskId);
    
    if (!reviewTask) {
      return res.status(404).json({
        success: false,
        message: '审查任务不存在'
      });
    }
    
    res.json({
      success: true,
      result: reviewTask
    });
  } catch (error) {
    console.error('Get review result route error:', error);
    res.status(500).json({
      success: false,
      message: '获取审查结果失败'
    });
  }
});

// 获取审查任务状态
router.get('/status/:taskId', authenticateToken, async (req, res) => {
  try {
    const taskId = req.params.taskId;
    
    if (!taskId) {
      return res.status(400).json({
        success: false,
        message: '任务ID为必填项'
      });
    }

    const reviewTask = await supabaseService.getReviewTaskById(taskId);
    
    if (!reviewTask) {
      return res.status(404).json({
        success: false,
        message: '审查任务不存在'
      });
    }
    
    // 只返回状态信息，不返回完整结果
    res.json({
      success: true,
      status: {
        task_id: reviewTask.id,
        status: reviewTask.status,
        progress: reviewTask.progress || 0,
        created_at: reviewTask.created_at,
        completed_at: reviewTask.completed_at
      }
    });
  } catch (error) {
    console.error('Get review status route error:', error);
    res.status(500).json({
      success: false,
      message: '获取审查状态失败'
    });
  }
});

// 条款对比
router.post('/compare', authenticateToken, async (req, res) => {
  try {
    const comparisonRequest: ComparisonRequest = {
      contract1_id: req.body.contract1_id,
      contract2_id: req.body.contract2_id,
      comparison_type: req.body.comparison_type || 'full',
      focus_areas: req.body.focus_areas
    };

    // 验证必填字段
    if (!comparisonRequest.contract1_id || !comparisonRequest.contract2_id) {
      return res.status(400).json({
        success: false,
        message: '两个合同ID都为必填项',
        error_code: 'MISSING_CONTRACT_IDS'
      });
    }

    // 验证合同ID格式（简单的UUID格式检查）
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(comparisonRequest.contract1_id) || !uuidRegex.test(comparisonRequest.contract2_id)) {
      return res.status(400).json({
        success: false,
        message: '合同ID格式无效',
        error_code: 'INVALID_CONTRACT_ID_FORMAT'
      });
    }

    // 验证不能对比相同合同
    if (comparisonRequest.contract1_id === comparisonRequest.contract2_id) {
      return res.status(400).json({
        success: false,
        message: '不能对比相同的合同',
        error_code: 'SAME_CONTRACT_COMPARISON'
      });
    }

    // 验证对比类型
    const validTypes = ['full', 'key_clauses', 'risk_clauses'];
    if (!validTypes.includes(comparisonRequest.comparison_type)) {
      return res.status(400).json({
        success: false,
        message: `无效的对比类型。支持的类型: ${validTypes.join(', ')}`,
        error_code: 'INVALID_COMPARISON_TYPE'
      });
    }

    // 验证focus_areas格式
    if (comparisonRequest.focus_areas && !Array.isArray(comparisonRequest.focus_areas)) {
      return res.status(400).json({
        success: false,
        message: 'focus_areas必须是数组格式',
        error_code: 'INVALID_FOCUS_AREAS_FORMAT'
      });
    }

    // 验证focus_areas内容
    if (comparisonRequest.focus_areas && comparisonRequest.focus_areas.length > 0) {
      const validFocusAreas = ['付款条款', '违约责任', '知识产权', '保密条款', '合同期限', '服务范围'];
      const invalidAreas = comparisonRequest.focus_areas.filter(area => 
        typeof area !== 'string' || !validFocusAreas.includes(area)
      );
      
      if (invalidAreas.length > 0) {
        return res.status(400).json({
          success: false,
          message: `无效的关注领域: ${invalidAreas.join(', ')}。支持的领域: ${validFocusAreas.join(', ')}`,
          error_code: 'INVALID_FOCUS_AREAS'
        });
      }
    }

    // 获取两个合同的详细信息
    const contract1 = await supabaseService.getContractById(comparisonRequest.contract1_id);
    const contract2 = await supabaseService.getContractById(comparisonRequest.contract2_id);

    if (!contract1) {
      return res.status(404).json({
        success: false,
        message: `找不到合同A (ID: ${comparisonRequest.contract1_id})`,
        error_code: 'CONTRACT1_NOT_FOUND'
      });
    }

    if (!contract2) {
      return res.status(404).json({
        success: false,
        message: `找不到合同B (ID: ${comparisonRequest.contract2_id})`,
        error_code: 'CONTRACT2_NOT_FOUND'
      });
    }

    // 检查合同状态是否允许对比
    const allowedStatuses = ['uploaded', 'reviewed'];
    if (!allowedStatuses.includes(contract1.status)) {
      return res.status(400).json({
        success: false,
        message: `合同A状态不允许对比 (当前状态: ${contract1.status})`,
        error_code: 'CONTRACT1_STATUS_INVALID'
      });
    }

    if (!allowedStatuses.includes(contract2.status)) {
      return res.status(400).json({
        success: false,
        message: `合同B状态不允许对比 (当前状态: ${contract2.status})`,
        error_code: 'CONTRACT2_STATUS_INVALID'
      });
    }

    // 执行合同对比逻辑
    const comparisonResult = await performContractComparison(
      contract1,
      contract2,
      comparisonRequest.comparison_type,
      comparisonRequest.focus_areas
    );
    
    res.json({
      success: true,
      result: comparisonResult
    });
  } catch (error) {
    console.error('Compare contract route error:', error);
    
    // 根据错误类型返回不同的错误信息
    if (error instanceof TypeError) {
      return res.status(400).json({
        success: false,
        message: '请求参数格式错误',
        error_code: 'INVALID_REQUEST_FORMAT'
      });
    }
    
    if (error.message && error.message.includes('database')) {
      return res.status(503).json({
        success: false,
        message: '数据库连接错误，请稍后重试',
        error_code: 'DATABASE_ERROR'
      });
    }
    
    res.status(500).json({
      success: false,
      message: '合同对比失败，请稍后重试',
      error_code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

// 获取审查历史
router.get('/history/:contractId', authenticateToken, async (req, res) => {
  try {
    const contractId = req.params.contractId;
    
    if (!contractId) {
      return res.status(400).json({
        success: false,
        message: '合同ID为必填项'
      });
    }

    // 从数据库获取审查历史
    const reviewTasks = await supabaseService.getReviewTasksByContractId(contractId);
    
    const history = reviewTasks.map(task => ({
      task_id: task.id,
      review_type: task.review_type,
      status: task.status,
      risk_level: task.risk_level || 'unknown',
      risk_score: task.risk_score || 0,
      reviewer_name: task.reviewer_name || '系统审查',
      created_at: task.created_at,
      completed_at: task.completed_at
    }));

    res.json({
      success: true,
      history
    });
  } catch (error) {
    console.error('Get review history route error:', error);
    res.status(500).json({
      success: false,
      message: '获取审查历史失败'
    });
  }
});

// 获取审查规则列表
router.get('/rules', requireLegalStaff, async (req, res) => {
  try {
    // 这里应该从数据库获取审查规则
    // 暂时返回模拟数据
    const rules = [
      {
        id: 'rule-1',
        name: '违约责任条款检查',
        description: '检查合同是否包含明确的违约责任条款',
        rule_type: 'liability_clause',
        severity: 'high',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 'rule-2',
        name: '合同期限明确性检查',
        description: '检查合同期限是否明确定义',
        rule_type: 'term_clarity',
        severity: 'medium',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 'rule-3',
        name: '付款条款完整性检查',
        description: '检查付款方式和时间是否明确',
        rule_type: 'payment_terms',
        severity: 'high',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z'
      }
    ];

    res.json({
      success: true,
      rules
    });
  } catch (error) {
    console.error('Get review rules route error:', error);
    res.status(500).json({
      success: false,
      message: '获取审查规则失败'
    });
  }
});

// 创建自定义审查规则
router.post('/rules', requireLegalStaff, async (req, res) => {
  try {
    const { name, description, rule_type, severity, conditions } = req.body;
    
    // 验证必填字段
    if (!name || !description || !rule_type || !severity) {
      return res.status(400).json({
        success: false,
        message: '规则名称、描述、类型和严重程度为必填项'
      });
    }

    // 验证严重程度
    const validSeverities = ['low', 'medium', 'high', 'critical'];
    if (!validSeverities.includes(severity)) {
      return res.status(400).json({
        success: false,
        message: '无效的严重程度'
      });
    }

    // 创建规则（模拟）
    const newRule = {
      id: `rule-${Date.now()}`,
      name,
      description,
      rule_type,
      severity,
      conditions: conditions || {},
      is_active: true,
      created_by: req.user!.userId,
      created_at: new Date().toISOString()
    };

    res.status(201).json({
      success: true,
      rule: newRule,
      message: '审查规则创建成功'
    });
  } catch (error) {
    console.error('Create review rule route error:', error);
    res.status(500).json({
      success: false,
      message: '创建审查规则失败'
    });
  }
});

// 更新审查规则
router.put('/rules/:ruleId', requireLegalStaff, async (req, res) => {
  try {
    const ruleId = req.params.ruleId;
    const { name, description, severity, is_active, conditions } = req.body;
    
    if (!ruleId) {
      return res.status(400).json({
        success: false,
        message: '规则ID为必填项'
      });
    }

    // 更新规则（模拟）
    const updatedRule = {
      id: ruleId,
      name,
      description,
      severity,
      is_active,
      conditions,
      updated_by: req.user!.userId,
      updated_at: new Date().toISOString()
    };

    res.json({
      success: true,
      rule: updatedRule,
      message: '审查规则更新成功'
    });
  } catch (error) {
    console.error('Update review rule route error:', error);
    res.status(500).json({
      success: false,
      message: '更新审查规则失败'
    });
  }
});

// 删除审查规则
router.delete('/rules/:ruleId', requireLegalStaff, async (req, res) => {
  try {
    const ruleId = req.params.ruleId;
    
    if (!ruleId) {
      return res.status(400).json({
        success: false,
        message: '规则ID为必填项'
      });
    }

    // 删除规则（模拟）
    res.json({
      success: true,
      message: '审查规则删除成功'
    });
  } catch (error) {
    console.error('Delete review rule route error:', error);
    res.status(500).json({
      success: false,
      message: '删除审查规则失败'
    });
  }
});

// 批量审查
router.post('/batch', requireLegalStaff, async (req, res) => {
  try {
    const { contract_ids, review_type } = req.body;
    
    if (!contract_ids || !Array.isArray(contract_ids) || contract_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '合同ID列表不能为空'
      });
    }

    if (!review_type) {
      return res.status(400).json({
        success: false,
        message: '审查类型为必填项'
      });
    }

    // 批量启动审查任务
    const tasks = [];
    for (const contractId of contract_ids) {
      const result = await ReviewTaskService.startReview(req.user!.userId, {
        contract_id: contractId,
        review_type
      });
      
      if (result.success) {
        tasks.push(result.task);
      }
    }

    res.status(201).json({
      success: true,
      tasks,
      message: `成功启动${tasks.length}个审查任务`
    });
  } catch (error) {
    console.error('Batch review route error:', error);
    res.status(500).json({
      success: false,
      message: '批量审查失败'
    });
  }
});

// 导出审查报告
router.get('/export/:taskId', authenticateToken, async (req, res) => {
  try {
    const taskId = req.params.taskId;
    const format = req.query.format as string || 'json';
    
    if (!taskId) {
      return res.status(400).json({
        success: false,
        message: '任务ID为必填项'
      });
    }

    const result = await ReviewTaskService.getReviewResult(taskId);
    
    if (!result.success) {
      return res.status(404).json(result);
    }

    // 根据格式返回不同的响应
    switch (format.toLowerCase()) {
      case 'pdf':
        // 在实际应用中，这里会生成PDF文件
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="review-report-${taskId}.pdf"`);
        res.send('PDF content would be here');
        break;
        
      case 'excel':
        // 在实际应用中，这里会生成Excel文件
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        res.setHeader('Content-Disposition', `attachment; filename="review-report-${taskId}.xlsx"`);
        res.send('Excel content would be here');
        break;
        
      default:
        // JSON格式
        res.json({
          success: true,
          report: result.result,
          exported_at: new Date().toISOString()
        });
    }
  } catch (error) {
    console.error('Export review report route error:', error);
    res.status(500).json({
      success: false,
      message: '导出审查报告失败'
    });
  }
});



export default router;