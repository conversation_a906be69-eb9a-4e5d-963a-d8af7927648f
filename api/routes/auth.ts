/**
 * This is a user authentication API route demo.
 * Handle user registration, login, token management, etc.
 */
import { Router, type Request, type Response } from 'express';
import AuthService, { SignUpData, SignInData } from '../services/authService.js';
import { authenticateToken, rateLimit } from '../middleware/auth.js';

const router = Router();

// 应用限流中间件
router.use(rateLimit(50, 15 * 60 * 1000)); // 每15分钟最多50次请求

/**
 * User Registration
 * POST /api/auth/register
 */
router.post('/register', async (req: Request, res: Response): Promise<void> => {
  try {
    const registerData: SignUpData = req.body;
    
    // 验证必填字段
    if (!registerData.email || !registerData.password || !registerData.name) {
      res.status(400).json({
        success: false,
        message: '邮箱、密码和姓名为必填项'
      });
      return;
    }

    const result = await AuthService.signUp(registerData);
    
    if (result.user && !result.error) {
      res.status(201).json({
        success: true,
        message: '注册成功',
        user: result.user,
        session: result.session
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error?.message || '注册失败'
      });
    }
  } catch (error) {
    console.error('Register route error:', error);
    res.status(500).json({
      success: false,
      message: '注册失败，请稍后重试'
    });
  }
});

/**
 * User Login
 * POST /api/auth/login
 */
router.post('/login', async (req: Request, res: Response): Promise<void> => {
  try {
    const loginData: SignInData = req.body;
    
    // 验证必填字段
    if (!loginData.email || !loginData.password) {
      res.status(400).json({
        success: false,
        message: '邮箱和密码为必填项'
      });
      return;
    }

    const result = await AuthService.signIn(loginData);
    
    if (result.user && !result.error) {
      res.json({
        success: true,
        message: '登录成功',
        user: result.user,
        session: result.session
      });
    } else {
      res.status(401).json({
        success: false,
        message: result.error?.message || '登录失败'
      });
    }
  } catch (error) {
    console.error('Login route error:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试'
    });
  }
});

/**
 * User Logout
 * POST /api/auth/logout
 */
router.post('/logout', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await AuthService.signOut();
    
    if (!result.error) {
      res.json({
        success: true,
        message: '登出成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.error.message || '登出失败'
      });
    }
  } catch (error) {
    console.error('Logout route error:', error);
    res.status(500).json({
      success: false,
      message: '登出失败'
    });
  }
});

// 获取当前用户信息
router.get('/me', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const userProfile = await AuthService.getCurrentUserProfile();
    
    if (userProfile) {
      res.json({
        success: true,
        user: userProfile
      });
    } else {
      res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
  } catch (error) {
    console.error('Get current user route error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

// 刷新token
router.post('/refresh', async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await AuthService.refreshSession();
    
    if (result.session && !result.error) {
      res.json({
        success: true,
        message: 'Token刷新成功',
        session: result.session
      });
    } else {
      res.status(401).json({
        success: false,
        message: result.error?.message || 'Token刷新失败'
      });
    }
  } catch (error) {
    console.error('Refresh token route error:', error);
    res.status(500).json({
      success: false,
      message: 'Token刷新失败'
    });
  }
});

// 请求密码重置
router.post('/forgot-password', async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.body;
    
    if (!email) {
      res.status(400).json({
        success: false,
        message: '邮箱为必填项'
      });
      return;
    }

    const result = await AuthService.resetPassword(email);
    
    if (!result.error) {
      res.json({
        success: true,
        message: '密码重置邮件已发送'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error.message || '请求失败，请稍后重试'
      });
    }
  } catch (error) {
    console.error('Forgot password route error:', error);
    res.status(500).json({
      success: false,
      message: '请求失败，请稍后重试'
    });
  }
});

// 更新密码
router.post('/update-password', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { newPassword } = req.body;
    
    if (!newPassword) {
      res.status(400).json({
        success: false,
        message: '新密码为必填项'
      });
      return;
    }

    const result = await AuthService.updatePassword(newPassword);
    
    if (!result.error) {
      res.json({
        success: true,
        message: '密码更新成功'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error.message || '密码更新失败'
      });
    }
  } catch (error) {
    console.error('Update password route error:', error);
    res.status(500).json({
      success: false,
      message: '密码更新失败'
    });
  }
});

// 更新邮箱
router.post('/update-email', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { newEmail } = req.body;
    
    if (!newEmail) {
      res.status(400).json({
        success: false,
        message: '新邮箱为必填项'
      });
      return;
    }

    const result = await AuthService.updateEmail(newEmail);
    
    if (!result.error) {
      res.json({
        success: true,
        message: '邮箱更新成功'
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error.message || '邮箱更新失败'
      });
    }
  } catch (error) {
    console.error('Update email route error:', error);
    res.status(500).json({
      success: false,
      message: '邮箱更新失败'
    });
  }
});

// 检查用户角色权限
router.get('/check-role/:role', authenticateToken, async (req: Request, res: Response): Promise<void> => {
  try {
    const { role } = req.params;
    const hasRole = await AuthService.hasRole(role);
    
    res.json({
      success: true,
      hasRole
    });
  } catch (error) {
    console.error('Check role route error:', error);
    res.status(500).json({
      success: false,
      message: '权限检查失败'
    });
  }
});

export default router;