/**
 * Contract Routes
 * 合同管理相关路由
 */
import express from 'express';
import multer from 'multer';
import { supabaseService, Contract } from '../services/supabaseService.js';
import { fileStorageService, FileMetadata } from '../services/fileStorageService.js';
import { documentParserService } from '../services/documentParserService.js';
import { authenticateToken, requireLegalStaff, optionalAuth } from '../middleware/auth.js';
import { processFileName } from '../utils/fileNameUtils.js';
import {
  validateContractCreation,
  validateContractUpdate,
  validateContractQuery,
  validateContractId,
  validateBatchOperation,
  validateFileUpload,
  sanitizeInput,
  checkPermissions
} from '../middleware/validation.js';
import { asyncHandler } from '../middleware/errorHandler.js';
import {
  BusinessError,
  ValidationAppError,
  AuthorizationError,
  NotFoundError,
  FileError
} from '../middleware/errorHandler.js';

const router = express.Router();

// 类型定义
interface ContractListQuery {
  page?: number;
  limit?: number;
  status?: string;
  type?: string;
  search?: string;
  created_by?: string;
  start_date?: string;
  end_date?: string;
}

interface CreateContractRequest {
  title: string;
  category?: string;
  content?: string;
  description?: string;
  template_id?: string;
  file_name?: string;
  file_size?: number;
  file_path?: string;
  counterparty?: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  file_url?: string;
  ocr_content?: string;
}

interface UpdateContractRequest {
  title?: string;
  category?: string;
  content?: string;
  description?: string;
  status?: 'draft' | 'reviewing' | 'approved' | 'rejected' | 'signed' | 'expired' | 'uploaded' | 'processing' | 'reviewed' | 'archived';
  counterparty?: string;
  amount?: number;
  start_date?: string;
  end_date?: string;
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
  file_path?: string;
  file_url?: string;
  ocr_content?: string;
}

// 配置文件上传
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    console.log('🔍 [Multer调试] === 文件过滤器被调用 ===');
    console.log('🔍 [Multer调试] 请求信息:', {
      method: req.method,
      url: req.url,
      contentType: req.headers['content-type'],
      contentLength: req.headers['content-length']
    });
    console.log('🔍 [Multer调试] 原始文件信息:', {
      fieldname: file.fieldname,
      originalname: file.originalname,
      encoding: file.encoding,
      mimetype: file.mimetype,
      timestamp: new Date().toISOString()
    });

    // 使用专业的文件名处理工具
    try {
      const result = processFileName(file.originalname);

      if (result.wasFixed) {
        console.log('✅ [Multer调试] 文件名已修复:', {
          original: result.originalName,
          processed: result.processedName
        });
        // 更新文件名
        file.originalname = result.processedName;
      }
    } catch (error) {
      console.warn('⚠️ [Multer调试] 文件名处理失败:', error);
      // 如果处理失败，生成一个安全的文件名
      const timestamp = Date.now();
      const extension = file.originalname.includes('.') ? file.originalname.split('.').pop() : 'txt';
      file.originalname = `file_${timestamp}.${extension}`;
      console.log('🔧 [Multer调试] 使用安全文件名:', file.originalname);
    }
    
    // 允许的文件类型
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/tiff'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      console.log('✅ [Multer调试] 文件类型验证通过:', file.mimetype);
      cb(null, true);
    } else {
      console.error('❌ [Multer调试] 不支持的文件类型:', file.mimetype);
      cb(new Error('不支持的文件类型'));
    }
  }
});

// 添加multer错误处理中间件
const multerErrorHandler = (error: any, req: any, res: any, next: any) => {
  console.error('💥 [Multer错误] 文件上传错误:', {
    error: error.message,
    code: error.code,
    field: error.field,
    timestamp: new Date().toISOString()
  });
  
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大10MB）'
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: '意外的文件字段'
      });
    }
  }
  
  return res.status(400).json({
    success: false,
    message: error.message || '文件上传失败'
  });
};

// 获取合同列表
router.get('/', optionalAuth, validateContractQuery, asyncHandler(async (req, res) => {
  // 验证和解析查询参数
  const page = Math.max(1, parseInt(req.query.page as string) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string) || 10));
  // 搜索参数：仅支持合同标题模糊查询
  const cleanSearch = req.query.search ? sanitizeInput.cleanSearchTerm(req.query.search as string) : undefined;
  
  const options: any = {
     page,
     limit,
     search: cleanSearch,
     status: req.query.status as string,
     category: req.query.category as string, // 统一使用category
     start_date: req.query.start_date as string,
     end_date: req.query.end_date as string
   };

   // 如果用户已登录且不是管理员，只显示自己的合同
   if (req.user && !req.user.role.includes('admin')) {
     options.user_id = req.user.userId; // 统一使用user_id
   }

  const result = await supabaseService.getContracts(options);
  
  res.json({
    success: true,
    contracts: result.contracts,
    total: result.total,
    page,
    limit,
    totalPages: Math.ceil(result.total / limit)
  });
}));

// 获取合同统计信息
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    let contracts;
    
    // 如果不是管理员，只统计自己的合同
    if (req.user?.role.includes('admin') || req.user?.role === 'legal_staff') {
      contracts = await supabaseService.getAllContracts();
    } else {
      const result = await supabaseService.getContracts({ 
        userId: req.user?.userId,
        limit: 1000 // 获取足够多的数据用于统计
      });
      contracts = result.contracts;
    }
    
    const stats = {
      total: contracts.length,
      draft: contracts.filter(c => c.status === 'draft').length,
      reviewing: contracts.filter(c => c.status === 'reviewing').length,
      approved: contracts.filter(c => c.status === 'approved').length,
      rejected: contracts.filter(c => c.status === 'rejected').length,
      signed: contracts.filter(c => c.status === 'signed').length,
      expired: contracts.filter(c => c.status === 'expired').length,
      uploaded: contracts.filter(c => c.status === 'uploaded').length,
      processing: contracts.filter(c => c.status === 'processing').length,
      reviewed: contracts.filter(c => c.status === 'reviewed').length,
      archived: contracts.filter(c => c.status === 'archived').length
    };
    
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Get contract stats route error:', error);
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

// 创建合同
router.post('/', authenticateToken, upload.single('file'), multerErrorHandler, validateFileUpload, validateContractCreation, asyncHandler(async (req, res) => {
  console.log('🚀 [后端调试] === 合同创建接口被调用 ===');
  console.log('📥 [后端调试] 请求头信息:', {
    'content-type': req.headers['content-type'],
    'content-length': req.headers['content-length'],
    'authorization': req.headers.authorization ? 'Bearer [TOKEN]' : 'None',
    'user-agent': req.headers['user-agent']?.substring(0, 50) + '...',
    'origin': req.headers.origin,
    'referer': req.headers.referer
  });
  
  console.log('📥 [后端调试] 请求体信息:', {
    method: req.method,
    url: req.url,
    bodyKeys: Object.keys(req.body || {}),
    bodyValues: req.body,
    hasFile: !!req.file,
    fileInfo: req.file ? {
      fieldname: req.file.fieldname,
      originalname: req.file.originalname,
      size: req.file.size
    } : null,
    timestamp: new Date().toISOString()
  });
  
  // 详细检查请求体内容
  console.log('📋 [后端调试] 请求体详细分析:', {
    bodyType: typeof req.body,
    bodyConstructor: req.body?.constructor?.name,
    bodyEntries: req.body ? Object.entries(req.body) : [],
    bodyStringified: JSON.stringify(req.body, null, 2)
  });
  
  if (req.file) {
    console.log('📁 [后端调试] 文件详细信息:', {
      fieldname: req.file.fieldname,
      originalname: req.file.originalname,
      encoding: req.file.encoding,
      mimetype: req.file.mimetype,
      size: req.file.size,
      sizeInMB: (req.file.size / 1024 / 1024).toFixed(2),
      bufferLength: req.file.buffer?.length,
      hasBuffer: !!req.file.buffer
    });
  } else {
    console.log('📄 [后端调试] 未检测到文件上传');
    console.log('📄 [后端调试] 可能的原因分析:', {
      contentType: req.headers['content-type'],
      isMultipart: req.headers['content-type']?.includes('multipart/form-data'),
      hasBody: !!req.body,
      bodySize: JSON.stringify(req.body).length
    });
  }
  
  // 数据清理
  const cleanTitle = sanitizeInput.cleanText(req.body.title);
  const cleanContent = req.body.content ? sanitizeInput.cleanText(req.body.content) : undefined;
  const cleanDescription = req.body.description ? sanitizeInput.cleanText(req.body.description) : undefined;

  console.log('📋 [后端调试] 原始请求体数据:', {
    title: req.body.title,
    category: req.body.category,
    counterparty: req.body.counterparty,
    amount: req.body.amount,
    start_date: req.body.start_date,
    end_date: req.body.end_date,
    risk_level: req.body.risk_level,
    content: req.body.content?.substring(0, 100) + '...',
    description: req.body.description?.substring(0, 100) + '...'
  });

  // 现在前后端使用统一字段名，直接构建数据
  const contractData = {
    user_id: req.user!.userId,
    title: cleanTitle,
    category: req.body.category || 'general',
    file_path: '',
    content: cleanContent,
    description: cleanDescription,
    status: 'uploaded' as const,
    counterparty: req.body.counterparty,
    amount: req.body.amount ? parseFloat(req.body.amount) : undefined,
    start_date: req.body.start_date,
    end_date: req.body.end_date,
    risk_level: req.body.risk_level || 'medium'
  };

  console.log('📋 [后端调试] 处理后的合同数据:', {
    user_id: contractData.user_id,
    title: contractData.title,
    category: contractData.category,
    counterparty: contractData.counterparty,
    amount: contractData.amount,
    start_date: contractData.start_date,
    end_date: contractData.end_date,
    risk_level: contractData.risk_level,
    status: contractData.status
  });

  // 如果有文件上传，使用Supabase Storage
  if (req.file) {
    // 验证文件类型和大小
    if (!fileStorageService.validateFileType(req.file.mimetype)) {
      throw new FileError('不支持的文件类型。仅支持PDF、Word文档和文本文件。');
    }

    if (!fileStorageService.validateFileSize(req.file.size)) {
      throw new FileError('文件大小超过限制（最大10MB）');
    }

    // 解析文档内容
    let parsedContent = '';
    let parseError = null;
    let parseMetadata = null;
    
    console.log('🚀 [后端调试] === 文档解析开始 ===');
    console.log('📁 [后端调试] 接收到文件信息:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      sizeInMB: (req.file.size / 1024 / 1024).toFixed(2),
      bufferLength: req.file.buffer?.length,
      hasBuffer: !!req.file.buffer,
      timestamp: new Date().toISOString()
    });
    
    // 检查文档解析服务是否可用
    if (!documentParserService) {
      console.error('❌ [后端调试] documentParserService 未定义');
      parseError = '文档解析服务不可用';
    } else {
      console.log('✅ [后端调试] documentParserService 已加载');
      
      try {
        console.log('🔍 [后端调试] 开始调用文档解析服务...');
        console.log('🔍 [后端调试] 解析参数:', {
          bufferSize: req.file.buffer.length,
          mimeType: req.file.mimetype,
          fileName: req.file.originalname
        });
        
        const parseStartTime = Date.now();
        const parseResult = await documentParserService.parseDocument(
          req.file.buffer,
          req.file.mimetype,
          req.file.originalname
        );
        const parseEndTime = Date.now();
        
        console.log('📊 [后端调试] 文档解析完成，耗时:', parseEndTime - parseStartTime, 'ms');
        console.log('📋 [后端调试] 文档解析结果:', {
          success: parseResult.success,
          contentLength: parseResult.content?.length || 0,
          hasContent: !!parseResult.content,
          error: parseResult.error,
          metadata: parseResult.metadata,
          resultType: typeof parseResult
        });
        
        if (parseResult.success && parseResult.content) {
          parsedContent = parseResult.content;
          parseMetadata = parseResult.metadata;
          
          console.log('✅ [后端调试] 文档解析成功!');
          console.log('📝 [后端调试] 解析内容统计:', {
            totalLength: parsedContent.length,
            wordCount: parsedContent.split(/\s+/).filter(word => word.length > 0).length,
            lineCount: parsedContent.split('\n').length,
            hasChineseChars: /[\u4e00-\u9fa5]/.test(parsedContent)
          });
          console.log('📖 [后端调试] 解析内容预览 (前200字符):', parsedContent.substring(0, 200) + (parsedContent.length > 200 ? '...' : ''));
          
          if (parseResult.metadata) {
            console.log('📊 [后端调试] 解析元数据:', parseResult.metadata);
          }
        } else {
          parseError = parseResult.error || '解析失败，未返回内容';
          console.warn('⚠️ [后端调试] 文档解析失败:', parseError);
          console.warn('⚠️ [后端调试] 失败详情:', {
            success: parseResult.success,
            hasContent: !!parseResult.content,
            contentLength: parseResult.content?.length,
            error: parseResult.error
          });
        }
      } catch (error) {
        parseError = error instanceof Error ? error.message : '文档解析异常';
        console.error('💥 [后端调试] === 文档解析异常 ===');
        console.error('💥 [后端调试] 异常类型:', error.constructor.name);
        console.error('💥 [后端调试] 错误消息:', error.message);
        console.error('💥 [后端调试] 错误详情:', error);
        if (error.stack) {
          console.error('💥 [后端调试] 错误堆栈:', error.stack);
        }
      }
    }
    
    console.log('🏁 [后端调试] === 文档解析结束 ===');
    console.log('📊 [后端调试] 最终解析状态:', {
      hasContent: !!parsedContent,
      contentLength: parsedContent.length,
      hasError: !!parseError,
      error: parseError
    });

    // 如果没有手动输入的content，使用解析的内容
    if (!contractData.content && parsedContent) {
      contractData.content = parsedContent;
    }
    
    // 将解析的内容存储到ocr_content字段
    (contractData as any).ocr_content = parsedContent;

    // 先创建合同以获取ID
    const tempContract = await supabaseService.createContract(contractData);
    
    // 准备文件元数据
    const metadata: FileMetadata = {
      originalName: req.file.originalname,
      mimeType: req.file.mimetype,
      size: req.file.size,
      uploadedBy: req.user!.userId,
      contractId: tempContract.id
    };

    // 上传文件到Supabase Storage
    const uploadResult = await fileStorageService.uploadFile(req.file.buffer, metadata);
    
    if (uploadResult.success) {
      // 更新合同的文件路径
      const updatedContract = await supabaseService.updateContract(tempContract.id, {
        file_path: uploadResult.filePath!,
        file_url: uploadResult.publicUrl
      });
      
      console.log('📤 [后端调试] 准备发送响应给前端');
      console.log('📤 [后端调试] 合同创建结果:', {
        contractId: updatedContract.id,
        hasFile: !!uploadResult.filePath,
        fileUrl: uploadResult.publicUrl,
        parseSuccess: !parseError,
        contentLength: parsedContent.length
      });
      
      const responseData = {
        success: true,
        contract: updatedContract,
        file: {
          path: uploadResult.filePath,
          url: uploadResult.publicUrl,
          originalName: metadata.originalName
        },
        parsing: {
          success: !parseError,
          error: parseError,
          contentExtracted: !!parsedContent,
          content: parsedContent, // 添加解析的内容到响应中
          wordCount: parsedContent ? parsedContent.split(/\s+/).filter(word => word.length > 0).length : 0,
          metadata: parseMetadata
        }
      };
      
      console.log('📤 [后端调试] 响应数据结构:', {
        success: responseData.success,
        hasContract: !!responseData.contract,
        hasFile: !!responseData.file,
        parsingSuccess: responseData.parsing.success,
        parsingError: responseData.parsing.error,
        contentExtracted: responseData.parsing.contentExtracted,
        wordCount: responseData.parsing.wordCount
      });
      
      res.status(201).json(responseData);
    } else {
      // 如果文件上传失败，删除已创建的合同记录
      await supabaseService.deleteContract(tempContract.id);
      
      throw new FileError(uploadResult.error || '文件上传失败');
    }
  } else {
    // 没有文件上传的情况
    const contract = await supabaseService.createContract(contractData);
    
    res.status(201).json({
      success: true,
      contract
    });
  }
}));

// 获取单个合同详情
router.get('/:id', authenticateToken, validateContractId, asyncHandler(async (req, res) => {
  const contractId = req.params.id;
  const contract = await supabaseService.getContractById(contractId);
  
  if (!contract) {
    throw new NotFoundError('合同');
  }

  // 权限检查
  if (!checkPermissions.canViewContract(req.user, contract)) {
    throw new AuthorizationError('无权限查看此合同');
  }

  res.json({
    success: true,
    contract
  });
}));

// 更新合同
router.put('/:id', authenticateToken, upload.single('file'), multerErrorHandler, validateFileUpload, validateContractId, validateContractUpdate, asyncHandler(async (req, res) => {
  const contractId = req.params.id;
  const updateData: UpdateContractRequest = req.body;
  
  console.log('🔄 [后端调试] === 合同更新接口被调用 ===');
  console.log('📥 [后端调试] 合同ID:', contractId);
  console.log('📥 [后端调试] 请求头信息:', {
    'content-type': req.headers['content-type'],
    'content-length': req.headers['content-length'],
    'authorization': req.headers.authorization ? 'Bearer [TOKEN]' : 'None'
  });
  
  console.log('📥 [后端调试] 请求体信息:', {
    bodyKeys: Object.keys(req.body || {}),
    bodyValues: req.body,
    hasFile: !!req.file,
    fileInfo: req.file ? {
      fieldname: req.file.fieldname,
      originalname: req.file.originalname,
      size: req.file.size
    } : null
  });
  
  // 先检查合同是否存在和权限
  const existingContract = await supabaseService.getContractById(contractId);
  if (!existingContract) {
    throw new NotFoundError('合同');
  }
  
  // 权限检查
  if (!checkPermissions.canEditContract(req.user, existingContract)) {
    throw new AuthorizationError('无权限修改此合同');
  }

  // 数据清理
  const cleanedData: any = {
    // 确保使用当前登录用户的ID
    user_id: req.user.userId
  };
  if (updateData.title) {
    cleanedData.title = sanitizeInput.cleanText(updateData.title);
  }
  if (updateData.content) {
      cleanedData.content = sanitizeInput.cleanText(updateData.content);
    }
  if (updateData.category) {
    cleanedData.category = updateData.category;
  }
  if (updateData.status) {
    cleanedData.status = updateData.status;
  }
  if (updateData.counterparty) {
    cleanedData.counterparty = sanitizeInput.cleanText(updateData.counterparty);
  }
  if (updateData.amount !== undefined) {
    cleanedData.amount = updateData.amount;
  }
  if (updateData.start_date) {
    cleanedData.start_date = updateData.start_date;
  }
  if (updateData.end_date) {
    cleanedData.end_date = updateData.end_date;
  }
  if (updateData.risk_level) {
    cleanedData.risk_level = updateData.risk_level;
  }

  // 处理文件上传（如果有新文件）
  let parsedContent = '';
  let parseError = null;
  let parseMetadata = null;
  
  if (req.file) {
    console.log('📁 [后端调试] 检测到文件上传，开始处理...');
    
    // 验证文件类型和大小
    if (!fileStorageService.validateFileType(req.file.mimetype)) {
      throw new FileError('不支持的文件类型。仅支持PDF、Word文档和文本文件。');
    }

    if (!fileStorageService.validateFileSize(req.file.size)) {
      throw new FileError('文件大小超过限制（最大10MB）');
    }

    // 解析文档内容
    console.log('🚀 [后端调试] === 编辑合同文档解析开始 ===');
    console.log('📁 [后端调试] 接收到文件信息:', {
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      sizeInMB: (req.file.size / 1024 / 1024).toFixed(2)
    });
    
    // 检查文档解析服务是否可用
    if (!documentParserService) {
      console.error('❌ [后端调试] documentParserService 未定义');
      parseError = '文档解析服务不可用';
    } else {
      console.log('✅ [后端调试] documentParserService 已加载');
      
      try {
        console.log('🔍 [后端调试] 开始调用文档解析服务...');
        const parseStartTime = Date.now();
        const parseResult = await documentParserService.parseDocument(
          req.file.buffer,
          req.file.mimetype,
          req.file.originalname
        );
        const parseEndTime = Date.now();
        
        console.log('📊 [后端调试] 文档解析完成，耗时:', parseEndTime - parseStartTime, 'ms');
        console.log('📋 [后端调试] 文档解析结果:', {
          success: parseResult.success,
          contentLength: parseResult.content?.length || 0,
          hasContent: !!parseResult.content,
          error: parseResult.error
        });
        
        if (parseResult.success && parseResult.content) {
          parsedContent = parseResult.content;
          parseMetadata = parseResult.metadata;
          console.log('✅ [后端调试] 文档解析成功!');
          
          // 如果没有手动输入的content，使用解析的内容
          if (!cleanedData.content && parsedContent) {
            cleanedData.content = parsedContent;
            console.log('📝 [后端调试] 使用解析内容更新合同内容字段');
          }
          
          // 将解析的内容存储到ocr_content字段
          cleanedData.ocr_content = parsedContent;
          console.log('📝 [后端调试] 解析内容已存储到ocr_content字段');
        } else {
          parseError = parseResult.error || '解析失败，未返回内容';
          console.warn('⚠️ [后端调试] 文档解析失败:', parseError);
        }
      } catch (error) {
        parseError = error instanceof Error ? error.message : '文档解析异常';
        console.error('💥 [后端调试] === 文档解析异常 ===');
        console.error('💥 [后端调试] 错误消息:', error.message);
        console.error('💥 [后端调试] 错误详情:', error);
      }
    }
    
    console.log('🏁 [后端调试] === 编辑合同文档解析结束 ===');

    // 准备文件元数据
    const metadata: FileMetadata = {
      originalName: req.file.originalname,
      mimeType: req.file.mimetype,
      size: req.file.size,
      uploadedBy: req.user!.userId,
      contractId: contractId
    };

    // 上传文件到Supabase Storage
    const uploadResult = await fileStorageService.uploadFile(req.file.buffer, metadata);
    
    if (uploadResult.success) {
      // 更新合同的文件路径
      cleanedData.file_path = uploadResult.filePath!;
      cleanedData.file_url = uploadResult.publicUrl;
      console.log('✅ [后端调试] 文件上传成功:', {
        filePath: uploadResult.filePath,
        publicUrl: uploadResult.publicUrl
      });
    } else {
      console.error('❌ [后端调试] 文件上传失败:', uploadResult.error);
      throw new FileError(uploadResult.error || '文件上传失败');
    }
  }
  
  const contract = await supabaseService.updateContract(contractId, cleanedData);
  
  console.log('✅ [后端调试] 合同更新成功，准备返回响应');
  console.log('📤 [后端调试] 响应数据:', {
    contractId: contract.id,
    hasFile: !!req.file,
    parsedContentLength: parsedContent?.length || 0,
    parseError: parseError
  });

  // 构建响应数据，包含文档解析结果
  const responseData = {
    success: true,
    contract,
    // 添加文档解析结果
    parsing: req.file ? {
      success: !parseError && !!parsedContent,
      error: parseError,
      contentExtracted: !!parsedContent,
      content: parsedContent,
      wordCount: parsedContent ? parsedContent.split(/\s+/).filter(word => word.length > 0).length : 0,
      metadata: parseMetadata
    } : null,
    file: req.file && cleanedData.file_path ? {
      path: cleanedData.file_path,
      url: cleanedData.file_url,
      originalName: req.file.originalname
    } : null
  };

  console.log('📤 [后端调试] 最终响应结构:', {
    success: responseData.success,
    hasContract: !!responseData.contract,
    hasParsing: !!responseData.parsing,
    hasFile: !!responseData.file
  });

  res.json(responseData);
}));

// 删除合同
router.delete('/:id', authenticateToken, validateContractId, asyncHandler(async (req, res) => {
  const contractId = req.params.id;
  
  // 先检查合同是否存在和权限
  const existingContract = await supabaseService.getContractById(contractId);
  if (!existingContract) {
    throw new NotFoundError('合同');
  }
  
  // 权限检查
  if (!checkPermissions.canDeleteContract(req.user, existingContract)) {
    throw new AuthorizationError('无权限删除此合同');
  }
  
  await supabaseService.deleteContract(contractId);
  
  res.json({
    success: true,
    message: '合同删除成功'
  });
}));

// 复制合同
router.post('/:id/duplicate', authenticateToken, validateContractId, asyncHandler(async (req, res) => {
  const contractId = req.params.id;
  
  // 获取原合同
  const originalContract = await supabaseService.getContractById(contractId);
  if (!originalContract) {
    throw new NotFoundError('合同');
  }
  
  // 权限检查
  if (!checkPermissions.canViewContract(req.user, originalContract)) {
    throw new AuthorizationError('无权限复制此合同');
  }
  
  // 创建副本
  const duplicateData = {
      user_id: req.user.userId,
      title: `${originalContract.title} (副本)`,
      category: originalContract.category,
      type: originalContract.type || originalContract.category, // 确保type字段存在
      file_path: originalContract.file_path,
      content: originalContract.content,
      status: 'uploaded' as const
    };
  
  const contract = await supabaseService.createContract(duplicateData);
  
  res.status(201).json({
    success: true,
    contract
  });
}));

// 获取合同要素
router.get('/:id/elements', authenticateToken, validateContractId, asyncHandler(async (req, res) => {
  const contractId = req.params.id;
  
  // 检查合同是否存在和权限
  const contract = await supabaseService.getContractById(contractId);
  if (!contract) {
    throw new NotFoundError('合同');
  }
  
  // 权限检查
  if (!checkPermissions.canViewContract(req.user, contract)) {
    throw new AuthorizationError('无权限查看此合同');
  }
  
  const elements = await supabaseService.getContractElements(contractId);
  
  res.json({
    success: true,
    elements
  });
}));

// 获取风险项
router.get('/:id/risks', authenticateToken, validateContractId, asyncHandler(async (req, res) => {
  const contractId = req.params.id;
  
  // 检查合同是否存在和权限
  const contract = await supabaseService.getContractById(contractId);
  if (!contract) {
    throw new NotFoundError('合同');
  }
  
  // 权限检查
  if (!checkPermissions.canViewContract(req.user, contract)) {
    throw new AuthorizationError('无权限查看此合同');
  }
  
  const risks = await supabaseService.getRiskItems(contractId);
  
  res.json({
    success: true,
    risks
  });
}));

// 批量更新状态
router.patch('/batch/status', requireLegalStaff, validateBatchOperation, asyncHandler(async (req, res) => {
  const { contract_ids, status, operation } = req.body;
  
  // 验证操作类型
  if (operation && operation !== 'updateStatus') {
    throw new BusinessError('无效的批量操作类型');
  }
  
  if (!contract_ids || !Array.isArray(contract_ids) || contract_ids.length === 0) {
    throw new BusinessError('合同ID列表不能为空');
  }
  
  if (!status) {
    throw new BusinessError('状态不能为空');
  }

  // 批量更新合同状态
  const updatePromises = contract_ids.map(id => 
    supabaseService.updateContract(id, { status })
  );
  
  await Promise.all(updatePromises);
  
  res.json({
    success: true,
    message: `成功更新 ${contract_ids.length} 个合同的状态`,
    updated_count: contract_ids.length
  });
}));

// 文件上传错误处理
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大10MB）'
      });
    }
  }
  
  if (error.message === '不支持的文件类型') {
    return res.status(400).json({
      success: false,
      message: '不支持的文件类型，请上传PDF、Word或文本文件'
    });
  }
  
  next(error);
});

export default router;