/**
 * File Management Routes
 * 文件管理路由 - 处理文件上传、下载、删除等操作
 */
import express from 'express';
import multer from 'multer';
import { fileStorageService, FileMetadata } from '../services/fileStorageService.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    // 验证文件类型
    if (fileStorageService.validateFileType(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型。仅支持PDF、Word文档和文本文件。'));
    }
  },
});

/**
 * 上传文件
 * POST /api/files/upload
 */
router.post('/upload', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      });
    }

    // 验证文件大小
    if (!fileStorageService.validateFileSize(req.file.size)) {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大10MB）'
      });
    }

    const metadata: FileMetadata = {
      originalName: req.file.originalname,
      mimeType: req.file.mimetype,
      size: req.file.size,
      uploadedBy: req.user.userId,
      contractId: req.body.contractId || undefined
    };

    const result = await fileStorageService.uploadFile(req.file.buffer, metadata);

    if (result.success) {
      res.json({
        success: true,
        message: '文件上传成功',
        data: {
          filePath: result.filePath,
          publicUrl: result.publicUrl,
          originalName: metadata.originalName,
          size: metadata.size,
          mimeType: metadata.mimeType
        }
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.error || '文件上传失败'
      });
    }
  } catch (error) {
    console.error('File upload error:', error);
    res.status(500).json({
      success: false,
      message: '文件上传服务异常'
    });
  }
});

/**
 * 下载文件
 * GET /api/files/download/:filePath
 */
router.get('/download/:filePath(*)', authenticateToken, async (req, res) => {
  try {
    const filePath = req.params.filePath;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: '文件路径不能为空'
      });
    }

    const result = await fileStorageService.downloadFile(filePath);

    if (result.success && result.data) {
      // 设置响应头
      const fileName = filePath.split('/').pop() || 'download';
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Type', 'application/octet-stream');
      
      res.send(result.data);
    } else {
      res.status(404).json({
        success: false,
        message: result.error || '文件不存在'
      });
    }
  } catch (error) {
    console.error('File download error:', error);
    res.status(500).json({
      success: false,
      message: '文件下载服务异常'
    });
  }
});

/**
 * 删除文件
 * DELETE /api/files/:filePath
 */
router.delete('/:filePath(*)', authenticateToken, async (req, res) => {
  try {
    const filePath = req.params.filePath;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: '文件路径不能为空'
      });
    }

    const result = await fileStorageService.deleteFile(filePath);

    if (result.success) {
      res.json({
        success: true,
        message: '文件删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.error || '文件删除失败'
      });
    }
  } catch (error) {
    console.error('File delete error:', error);
    res.status(500).json({
      success: false,
      message: '文件删除服务异常'
    });
  }
});

/**
 * 获取文件签名URL（用于安全访问）
 * GET /api/files/signed-url/:filePath
 */
router.get('/signed-url/:filePath(*)', authenticateToken, async (req, res) => {
  console.log('🔍 [API] /signed-url 接口被调用');
  console.log('🔍 [API] 请求参数:', {
    filePath: req.params.filePath,
    query: req.query,
    headers: {
      authorization: req.headers.authorization ? '存在' : '不存在',
      'content-type': req.headers['content-type']
    },
    userId: req.user?.userId
  });
  
  try {
    const filePath = req.params.filePath;
    const expiresIn = parseInt(req.query.expiresIn as string) || 3600; // 默认1小时
    
    console.log('🔍 [API] 解析的参数:', {
      filePath,
      expiresIn,
      rawExpiresIn: req.query.expiresIn
    });
    
    if (!filePath) {
      console.warn('⚠️ [API] 文件路径为空');
      return res.status(400).json({
        success: false,
        message: '文件路径不能为空'
      });
    }

    console.log('🔍 [API] 调用 fileStorageService.getSignedUrl');
    const result = await fileStorageService.getSignedUrl(filePath, expiresIn);
    console.log('🔍 [API] fileStorageService 返回结果:', result);

    if (result.success) {
      const responseData = {
        success: true,
        data: {
          signedUrl: result.url,
          expiresIn
        }
      };
      console.log('✅ [API] 返回成功响应:', responseData);
      res.json(responseData);
    } else {
      console.error('❌ [API] fileStorageService 返回失败:', result.error);
      res.status(500).json({
        success: false,
        message: result.error || '获取文件访问链接失败'
      });
    }
  } catch (error) {
    console.error('❌ [API] 获取签名URL异常:', error);
    console.error('❌ [API] 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
    res.status(500).json({
      success: false,
      message: '获取文件访问链接服务异常'
    });
  }
});

/**
 * 获取文件列表
 * GET /api/files/list
 */
router.get('/list', authenticateToken, async (req, res) => {
  try {
    const folderPath = req.query.folder as string || '';
    
    const result = await fileStorageService.listFiles(folderPath);

    if (result.success) {
      res.json({
        success: true,
        data: result.files
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.error || '获取文件列表失败'
      });
    }
  } catch (error) {
    console.error('List files error:', error);
    res.status(500).json({
      success: false,
      message: '获取文件列表服务异常'
    });
  }
});

/**
 * 获取文件公共URL
 * GET /api/files/public-url/:filePath
 */
router.get('/public-url/:filePath(*)', authenticateToken, async (req, res) => {
  console.log('🔍 [API] /public-url 接口被调用');
  console.log('🔍 [API] 请求参数:', {
    filePath: req.params.filePath,
    query: req.query,
    headers: {
      authorization: req.headers.authorization ? '存在' : '不存在',
      'content-type': req.headers['content-type']
     },
     userId: req.user?.userId
   });
  
  try {
    const filePath = req.params.filePath;
    console.log('🔍 [API] 解析的文件路径:', filePath);
    
    if (!filePath) {
      console.warn('⚠️ [API] 文件路径为空');
      return res.status(400).json({
        success: false,
        message: '文件路径不能为空'
      });
    }

    console.log('🔍 [API] 调用 fileStorageService.getPublicUrl');
    const publicUrl = fileStorageService.getPublicUrl(filePath);
    console.log('✅ [API] 获取到公共URL:', publicUrl);

    const responseData = {
      success: true,
      data: {
        publicUrl
      }
    };
    
    console.log('✅ [API] 返回响应数据:', responseData);
    res.json(responseData);
  } catch (error) {
    console.error('❌ [API] 获取公共URL异常:', error);
    console.error('❌ [API] 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
    res.status(500).json({
      success: false,
      message: '获取文件公共链接服务异常'
    });
  }
});

// 错误处理中间件
router.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大10MB）'
      });
    }
  }
  
  if (error.message) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }

  res.status(500).json({
    success: false,
    message: '文件处理服务异常'
  });
});

export default router;