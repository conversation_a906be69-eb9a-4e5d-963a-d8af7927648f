/**
 * Vercel deploy entry handler, for serverless deployment, please don't modify this file
 */
import type { VercelRequest, VercelResponse } from '@vercel/node';
import app from './app.js';
import { setupGlobalErrorHandlers } from './middleware/errorHandler';

// 设置全局错误处理器
setupGlobalErrorHandlers();

export default function handler(req: VercelRequest, res: VercelResponse) {
  return app(req, res);
}