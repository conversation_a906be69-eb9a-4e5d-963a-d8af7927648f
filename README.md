# 智能合同审查系统 (ContractT2)

一个基于AI技术的专业法务工具平台，旨在提升合同管理效率、降低法律风险、优化合同审查流程。

## 📋 项目概述

智能合同审查系统是一个现代化的合同全生命周期管理解决方案，主要解决传统合同审查耗时长、风险识别不准确、条款对比困难等问题。为法务团队、律师事务所和企业提供智能化的合同管理服务。

### 🎯 核心目标
- 提升法务工作效率50%以上
- 降低合同风险识别遗漏率至5%以下
- 实现合同全生命周期智能化管理

## ✨ 功能特性

### 🏠 首页概览
- **数据概览面板**：显示合同总数、待审查数量、风险统计等关键指标
- **快速操作区**：提供合同上传、新建审查任务、查看最近文件等快捷入口
- **消息通知中心**：展示系统通知、审查完成提醒、风险预警等信息

### 📁 合同管理
- **文件上传**：支持批量上传PDF/Word格式合同，自动OCR识别文本内容
- **分类归档**：按合同类型、部门、时间等维度自动分类和手动归档
- **状态跟踪**：跟踪合同审查进度、审批状态、执行情况等生命周期状态
- **搜索筛选**：支持多维度搜索和筛选功能
- **批量操作**：批量状态更新、批量删除等操作

### 🔍 智能审查
- **AI风险识别**：自动识别合同中的法律风险点、不合规条款、缺失要素
- **要素自动抽取**：提取合同主体、金额、期限、违约条款等关键信息
- **审查报告生成**：生成详细的风险评估报告、修改建议、合规性分析

### 🔄 条款对比
- **版本对比**：智能对比不同版本合同条款，高亮显示差异内容
- **差异可视化**：以颜色标注、侧边对比等方式直观展示条款变更
- **版本历史**：记录合同修改历史，支持版本回溯和变更追踪

### ✍️ AI起草
- **智能起草助手**：基于用户需求和行业模板智能生成合同初稿
- **模板库管理**：提供标准合同模板，支持自定义模板创建和编辑
- **条款智能推荐**：根据合同类型和风险偏好推荐合适的法律条款

### 📚 知识库
- **法规数据库**：维护最新法律法规、司法解释、行业标准等参考资料
- **规则配置**：设置审查规则、风险阈值、合规标准等系统参数
- **模板配置**：管理合同模板、条款库、标准用语等基础数据

### 📊 数据统计
- **审查效率统计**：统计审查时间、处理量、准确率等效率指标
- **风险分析报表**：分析风险类型分布、高频风险点、趋势变化等
- **业务数据看板**：展示部门使用情况、合同类型分布、成本效益分析

## 🛠 技术架构

### 前端技术栈
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的JavaScript超集
- **Vite** - 快速的前端构建工具
- **Ant Design** - 企业级UI组件库
- **Tailwind CSS** - 实用优先的CSS框架
- **React Router** - 前端路由管理
- **Zustand** - 轻量级状态管理

### 后端技术栈
- **Node.js** - JavaScript运行时环境
- **Express.js** - Web应用框架
- **TypeScript** - 后端类型安全
- **Supabase** - 后端即服务平台
- **PostgreSQL** - 关系型数据库
- **JWT** - 身份认证

### 文档处理
- **Multer** - 文件上传处理
- **Tesseract.js** - OCR文字识别
- **PDF-Parse** - PDF文档解析
- **Mammoth** - Word文档处理

### 开发工具
- **ESLint** - 代码质量检查
- **Nodemon** - 开发环境热重载
- **Concurrently** - 并行运行脚本

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- pnpm >= 8.0.0 (推荐) 或 npm >= 9.0.0

### 安装依赖
```bash
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install
```

### 环境配置
1. 复制环境变量文件：
```bash
cp .env.example .env
```

2. 配置环境变量：
```env
# Supabase配置
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# 服务器配置
PORT=3001
NODE_ENV=development

# JWT密钥
JWT_SECRET=your_jwt_secret
```

### 数据库设置
1. 在Supabase中创建新项目
2. 运行数据库迁移：
```bash
# 按顺序执行迁移文件
supabase db push
```

### 启动开发服务器
```bash
# 同时启动前端和后端
pnpm dev

# 或分别启动
pnpm client:dev  # 前端开发服务器 (http://localhost:5173)
pnpm server:dev  # 后端API服务器 (http://localhost:3001)
```

### 构建生产版本
```bash
# 构建前端
pnpm build

# 预览构建结果
pnpm preview
```

## 📁 项目结构

```
ContractT2/
├── api/                    # 后端API
│   ├── data/              # 模拟数据
│   ├── middleware/        # 中间件
│   ├── routes/           # 路由定义
│   └── services/         # 业务逻辑服务
├── src/                   # 前端源码
│   ├── components/       # 可复用组件
│   ├── contexts/         # React上下文
│   ├── hooks/           # 自定义Hook
│   ├── lib/             # 工具库
│   └── pages/           # 页面组件
├── supabase/             # 数据库迁移
│   └── migrations/      # SQL迁移文件
├── public/               # 静态资源
└── dist/                # 构建输出
```

## 🔧 开发指南

### 代码规范
项目使用ESLint进行代码质量检查：
```bash
# 检查代码规范
pnpm lint

# 类型检查
pnpm check
```

### API开发
后端API遵循RESTful设计原则：
- `GET /api/contracts` - 获取合同列表
- `POST /api/contracts` - 创建新合同
- `PUT /api/contracts/:id` - 更新合同
- `DELETE /api/contracts/:id` - 删除合同
- `POST /api/review/analyze` - 智能审查
- `POST /api/review/compare` - 条款对比

### 数据库操作
使用Supabase客户端进行数据库操作：
```typescript
import { supabase } from './lib/supabase'

// 查询数据
const { data, error } = await supabase
  .from('contracts')
  .select('*')
  .eq('user_id', userId)
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 提交规范
使用约定式提交格式：
- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 提交 [Issue](https://github.com/your-username/ContractT2/issues)
- 发送邮件至：<EMAIL>

---

**智能合同审查系统** - 让合同管理更智能、更高效！
